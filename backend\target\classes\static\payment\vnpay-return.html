<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title><PERSON>ết quả thanh toán</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
        }
        .result {
            display: inline-block;
            padding: 30px;
            border: 2px solid #ccc;
            border-radius: 10px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
        }
        .fail {
            color: red;
        }

        button {
            background-color: #191C1F;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #333;
        }
    </style>
</head>
<body>

<div class="result" id="payment-result">
    <h2>Đang xử lý kết quả thanh toán...</h2>
</div>
<script>
    function getParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    const responseCode = getParam('vnp_ResponseCode');
    const txnRef = getParam('vnp_TxnRef');
    const amount = getParam('vnp_Amount');
    const bankCode = getParam('vnp_BankCode');
    const payDate = getParam('vnp_PayDate');
    const transactionNo = getParam('vnp_TransactionNo');
    const orderInfo = getParam('vnp_OrderInfo');

    const resultDiv = document.getElementById('payment-result');

    if (responseCode === '00') {
        resultDiv.innerHTML = `
            <h2 class="success">Thanh toán thành công!</h2>
            <p><strong>Mã đơn hàng:</strong> ${txnRef}</p>
            <p><strong>Mã giao dịch VNPAY:</strong> ${transactionNo}</p>
            <p><strong>Số tiền:</strong> ${(amount / 100).toLocaleString()} VND</p>
            <p><strong>Ngân hàng:</strong> ${bankCode}</p>
            <p><strong>Thời gian:</strong> ${payDate}</p>
            <button onclick="window.location.href='/'" style="margin-top: 20px; padding: 10px 20px; font-size: 16px;">Quay về Trang Chủ</button>
        `;

        fetch('/api/v1/payment/confirm', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                vnpTransactionNo: transactionNo,
                vnpTxnRef: txnRef,
                vnpAmount: amount,
                vnpOrderInfo: orderInfo,
                vnpPayDate: payDate,
                vnpResponseCode: responseCode
            })
        }).then(res => res.text()).then(data => {
            console.log("Server xác nhận:", data);
        });

    } else {
        resultDiv.innerHTML = `
            <h2 class="fail">Thanh toán thất bại!</h2>
            <p><strong>Mã đơn hàng:</strong> ${txnRef || 'Không xác định'}</p>
            <p><strong>Mã lỗi:</strong> ${responseCode}</p>
            <button onclick="window.location.href='/'" style="margin-top: 20px; padding: 10px 20px; font-size: 16px;">Quay về Trang Chủ</button>
        `;

        fetch('/api/v1/payment/confirm', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                vnpTxnRef: txnRef,
                vnpAmount: amount,
                vnpResponseCode: responseCode
            })
        }).then(res => res.text()).then(data => {
            console.log("Server xác nhận thất bại:", data);
        });
    }
</script>

</body>
</html>
