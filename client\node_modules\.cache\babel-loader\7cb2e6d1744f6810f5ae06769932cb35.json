{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\order\\\\CheckoutPage.js\";\nimport React, { Fragment } from \"react\";\nimport Layout from \"../layout\";\nimport CheckoutProducts from \"./CheckoutProducts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckoutPage = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(CheckoutProducts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = CheckoutPage;\nexport default CheckoutPage;\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");", "map": {"version": 3, "names": ["React", "Fragment", "Layout", "CheckoutProducts", "jsxDEV", "_jsxDEV", "CheckoutPage", "props", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/CheckoutPage.js"], "sourcesContent": ["import React, { Fragment } from \"react\";\r\nimport Layout from \"../layout\";\r\nimport CheckoutProducts from \"./CheckoutProducts\";\r\n\r\nconst CheckoutPage = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <Layout children={<CheckoutProducts />} />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default CheckoutPage;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAY,GAAIC,KAAK,IAAK;EAC9B,oBACEF,OAAA,CAACJ,QAAQ;IAAAO,QAAA,eACPH,OAAA,CAACH,MAAM;MAACM,QAAQ,eAAEH,OAAA,CAACF,gBAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEf,CAAC;AAACC,EAAA,GANIP,YAAY;AAQlB,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}