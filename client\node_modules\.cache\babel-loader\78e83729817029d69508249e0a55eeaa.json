{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\categories\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport AdminLayout from \"../layout\";\nimport CategoryMenu from \"./CategoryMenu\";\nimport AllCategories from \"./AllCategories\";\nimport { categoryState, categoryReducer } from \"./CategoryContext\";\n\n/* This context manage all of the caregories component's data */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CategoryContext = /*#__PURE__*/createContext();\nconst CategoryComponent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 space-y-4 p-4\",\n    children: [/*#__PURE__*/_jsxDEV(CategoryMenu, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AllCategories, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = CategoryComponent;\nconst Categories = props => {\n  _s();\n  const [data, dispatch] = useReducer(categoryReducer, categoryState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(CategoryContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n        children: /*#__PURE__*/_jsxDEV(CategoryComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(Categories, \"KsMUA/DbU6gCPEikgkAkUoXTsEo=\");\n_c2 = Categories;\nexport default Categories;\nvar _c, _c2;\n$RefreshReg$(_c, \"CategoryComponent\");\n$RefreshReg$(_c2, \"Categories\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "AdminLayout", "CategoryMenu", "AllCategories", "categoryState", "categoryReducer", "jsxDEV", "_jsxDEV", "CategoryContext", "CategoryComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Categories", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/index.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport AdminLayout from \"../layout\";\r\nimport CategoryMenu from \"./CategoryMenu\";\r\nimport AllCategories from \"./AllCategories\";\r\nimport { categoryState, categoryReducer } from \"./CategoryContext\";\r\n\r\n/* This context manage all of the caregories component's data */\r\nexport const CategoryContext = createContext();\r\n\r\nconst CategoryComponent = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 space-y-4 p-4\">\r\n      <CategoryMenu />\r\n      <AllCategories />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Categories = (props) => {\r\n  const [data, dispatch] = useReducer(categoryReducer, categoryState);\r\n  return (\r\n    <Fragment>\r\n      <CategoryContext.Provider value={{ data, dispatch }}>\r\n        <AdminLayout children={<CategoryComponent />} />\r\n      </CategoryContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Categories;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,aAAa,EAAEC,eAAe,QAAQ,mBAAmB;;AAElE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,eAAe,gBAAGT,aAAa,CAAC,CAAC;AAE9C,MAAMU,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,oBACEF,OAAA;IAAKG,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7CJ,OAAA,CAACL,YAAY;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBR,OAAA,CAACJ,aAAa;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIP,iBAAiB;AASvB,MAAMQ,UAAU,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC5B,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGrB,UAAU,CAACK,eAAe,EAAED,aAAa,CAAC;EACnE,oBACEG,OAAA,CAACT,QAAQ;IAAAa,QAAA,eACPJ,OAAA,CAACC,eAAe,CAACc,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAV,QAAA,eAClDJ,OAAA,CAACN,WAAW;QAACU,QAAQ,eAAEJ,OAAA,CAACE,iBAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEf,CAAC;AAACI,EAAA,CATIF,UAAU;AAAAO,GAAA,GAAVP,UAAU;AAWhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}