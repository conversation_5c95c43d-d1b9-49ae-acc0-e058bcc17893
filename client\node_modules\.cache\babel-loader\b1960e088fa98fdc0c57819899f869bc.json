{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst BearerToken = () => localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : false;\n_c = BearerToken;\nconst Headers = () => {\n  return {\n    headers: {\n      Authorization: `Bearer ${BearerToken()}` // Fixed: Use Authorization instead of token\n    }\n  };\n};\n\n// ⚠️ WARNING: Backend does not have CategoryController\n// These functions return mock data or throw errors\n_c2 = Headers;\nexport const getAllCategory = async () => {\n  console.warn(\"getAllCategory: Backend does not have CategoryController. Returning mock data.\");\n  try {\n    // Return mock categories based on the data-init.sql\n    return {\n      success: true,\n      Categories: [{\n        _id: 1,\n        cName: \"Electronics\",\n        cDescription: \"Electronic devices and gadgets\",\n        cStatus: \"Active\"\n      }, {\n        _id: 2,\n        cName: \"Fashion and Apparel\",\n        cDescription: \"Clothing and accessories\",\n        cStatus: \"Active\"\n      }, {\n        _id: 3,\n        cName: \"Beauty and Personal Care\",\n        cDescription: \"Cosmetics and personal care products\",\n        cStatus: \"Active\"\n      }, {\n        _id: 4,\n        cName: \"Furniture\",\n        cDescription: \"Home and office furniture\",\n        cStatus: \"Active\"\n      }, {\n        _id: 5,\n        cName: \"Beverages\",\n        cDescription: \"Drinks and beverages\",\n        cStatus: \"Active\"\n      }, {\n        _id: 6,\n        cName: \"Food\",\n        cDescription: \"Food items and snacks\",\n        cStatus: \"Active\"\n      }, {\n        _id: 7,\n        cName: \"Household Essentials\",\n        cDescription: \"Household cleaning and essentials\",\n        cStatus: \"Active\"\n      }, {\n        _id: 8,\n        cName: \"Toys and Hobbies\",\n        cDescription: \"Toys and hobby-related products\",\n        cStatus: \"Active\"\n      }, {\n        _id: 9,\n        cName: \"Media\",\n        cDescription: \"Books, movies, and music\",\n        cStatus: \"Active\"\n      }]\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const createCategory = async ({\n  cName,\n  cImage,\n  cDescription,\n  cStatus\n}) => {\n  console.warn(\"createCategory: Backend does not have CategoryController. This operation is not supported.\");\n  return {\n    success: false,\n    message: \"Category creation is not supported in the current backend implementation.\"\n  };\n};\nexport const editCategory = async (cId, des, status) => {\n  console.warn(\"editCategory: Backend does not have CategoryController. This operation is not supported.\");\n  return {\n    success: false,\n    message: \"Category editing is not supported in the current backend implementation.\"\n  };\n};\nexport const deleteCategory = async cId => {\n  console.warn(\"deleteCategory: Backend does not have CategoryController. This operation is not supported.\");\n  return {\n    success: false,\n    message: \"Category deletion is not supported in the current backend implementation.\"\n  };\n};\nvar _c, _c2;\n$RefreshReg$(_c, \"BearerToken\");\n$RefreshReg$(_c2, \"Headers\");", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "BearerToken", "localStorage", "getItem", "JSON", "parse", "token", "_c", "Headers", "headers", "Authorization", "_c2", "getAllCategory", "console", "warn", "success", "Categories", "_id", "cName", "cDescription", "cStatus", "error", "log", "createCategory", "cImage", "message", "editCategory", "cId", "des", "status", "deleteCategory", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst BearerToken = () =>\r\n  localStorage.getItem(\"jwt\")\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).token\r\n    : false;\r\nconst Headers = () => {\r\n  return {\r\n    headers: {\r\n      Authorization: `Bearer ${BearerToken()}`, // Fixed: Use Authorization instead of token\r\n    },\r\n  };\r\n};\r\n\r\n// ⚠️ WARNING: Backend does not have CategoryController\r\n// These functions return mock data or throw errors\r\nexport const getAllCategory = async () => {\r\n  console.warn(\"getAllCategory: Backend does not have CategoryController. Returning mock data.\");\r\n  try {\r\n    // Return mock categories based on the data-init.sql\r\n    return {\r\n      success: true,\r\n      Categories: [\r\n        { _id: 1, cName: \"Electronics\", cDescription: \"Electronic devices and gadgets\", cStatus: \"Active\" },\r\n        { _id: 2, cName: \"Fashion and Apparel\", cDescription: \"Clothing and accessories\", cStatus: \"Active\" },\r\n        { _id: 3, cName: \"Beauty and Personal Care\", cDescription: \"Cosmetics and personal care products\", cStatus: \"Active\" },\r\n        { _id: 4, cName: \"Furniture\", cDescription: \"Home and office furniture\", cStatus: \"Active\" },\r\n        { _id: 5, cName: \"Beverages\", cDescription: \"Drinks and beverages\", cStatus: \"Active\" },\r\n        { _id: 6, cName: \"Food\", cDescription: \"Food items and snacks\", cStatus: \"Active\" },\r\n        { _id: 7, cName: \"Household Essentials\", cDescription: \"Household cleaning and essentials\", cStatus: \"Active\" },\r\n        { _id: 8, cName: \"Toys and Hobbies\", cDescription: \"Toys and hobby-related products\", cStatus: \"Active\" },\r\n        { _id: 9, cName: \"Media\", cDescription: \"Books, movies, and music\", cStatus: \"Active\" }\r\n      ]\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const createCategory = async ({\r\n  cName,\r\n  cImage,\r\n  cDescription,\r\n  cStatus,\r\n}) => {\r\n  console.warn(\"createCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category creation is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n\r\nexport const editCategory = async (cId, des, status) => {\r\n  console.warn(\"editCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category editing is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n\r\nexport const deleteCategory = async (cId) => {\r\n  console.warn(\"deleteCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category deletion is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,WAAW,GAAGA,CAAA,KAClBC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GACvBC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACG,KAAK,GAC7C,KAAK;AAACC,EAAA,GAHNN,WAAW;AAIjB,MAAMO,OAAO,GAAGA,CAAA,KAAM;EACpB,OAAO;IACLC,OAAO,EAAE;MACPC,aAAa,EAAE,UAAUT,WAAW,CAAC,CAAC,EAAE,CAAE;IAC5C;EACF,CAAC;AACH,CAAC;;AAED;AACA;AAAAU,GAAA,GATMH,OAAO;AAUb,OAAO,MAAMI,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxCC,OAAO,CAACC,IAAI,CAAC,gFAAgF,CAAC;EAC9F,IAAI;IACF;IACA,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CACV;QAAEC,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEC,YAAY,EAAE,gCAAgC;QAAEC,OAAO,EAAE;MAAS,CAAC,EACnG;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,qBAAqB;QAAEC,YAAY,EAAE,0BAA0B;QAAEC,OAAO,EAAE;MAAS,CAAC,EACrG;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,0BAA0B;QAAEC,YAAY,EAAE,sCAAsC;QAAEC,OAAO,EAAE;MAAS,CAAC,EACtH;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,WAAW;QAAEC,YAAY,EAAE,2BAA2B;QAAEC,OAAO,EAAE;MAAS,CAAC,EAC5F;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,WAAW;QAAEC,YAAY,EAAE,sBAAsB;QAAEC,OAAO,EAAE;MAAS,CAAC,EACvF;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE,uBAAuB;QAAEC,OAAO,EAAE;MAAS,CAAC,EACnF;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,sBAAsB;QAAEC,YAAY,EAAE,mCAAmC;QAAEC,OAAO,EAAE;MAAS,CAAC,EAC/G;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,YAAY,EAAE,iCAAiC;QAAEC,OAAO,EAAE;MAAS,CAAC,EACzG;QAAEH,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEC,YAAY,EAAE,0BAA0B;QAAEC,OAAO,EAAE;MAAS,CAAC;IAE3F,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdR,OAAO,CAACS,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAO;EACnCL,KAAK;EACLM,MAAM;EACNL,YAAY;EACZC;AACF,CAAC,KAAK;EACJP,OAAO,CAACC,IAAI,CAAC,4FAA4F,CAAC;EAC1G,OAAO;IACLC,OAAO,EAAE,KAAK;IACdU,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,GAAG,EAAEC,GAAG,EAAEC,MAAM,KAAK;EACtDhB,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;EACxG,OAAO;IACLC,OAAO,EAAE,KAAK;IACdU,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAED,OAAO,MAAMK,cAAc,GAAG,MAAOH,GAAG,IAAK;EAC3Cd,OAAO,CAACC,IAAI,CAAC,4FAA4F,CAAC;EAC1G,OAAO;IACLC,OAAO,EAAE,KAAK;IACdU,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAAC,IAAAlB,EAAA,EAAAI,GAAA;AAAAoB,YAAA,CAAAxB,EAAA;AAAAwB,YAAA,CAAApB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}