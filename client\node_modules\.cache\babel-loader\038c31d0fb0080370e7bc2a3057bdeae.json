{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get user's cart\nexport const getCart = async userId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Add product to cart\nexport const addToCart = async (userId, productId, quantity = 1) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/add?productId=${productId}&quantity=${quantity}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Update cart item quantity\nexport const updateCartItem = async (userId, productId, quantity) => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/cart/${userId}/update?productId=${productId}&quantity=${quantity}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Remove item from cart\nexport const removeFromCart = async (userId, productId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/remove?productId=${productId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Empty entire cart\nexport const emptyCart = async userId => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/empty`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Check cart availability\nexport const checkCartAvailability = async userId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/check-availability`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getCart", "userId", "res", "get", "data", "error", "console", "log", "addToCart", "productId", "quantity", "post", "updateCartItem", "put", "removeFromCart", "delete", "emptyCart", "checkCartAvailability"], "sources": ["D:/ITSS_Reference/client/src/components/shop/cart/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get user's cart\nexport const getCart = async (userId) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Add product to cart\nexport const addToCart = async (userId, productId, quantity = 1) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/add?productId=${productId}&quantity=${quantity}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Update cart item quantity\nexport const updateCartItem = async (userId, productId, quantity) => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/cart/${userId}/update?productId=${productId}&quantity=${quantity}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Remove item from cart\nexport const removeFromCart = async (userId, productId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/remove?productId=${productId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Empty entire cart\nexport const emptyCart = async (userId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/empty`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Check cart availability\nexport const checkCartAvailability = async (userId) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/check-availability`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,OAAO,GAAG,MAAOC,MAAM,IAAK;EACvC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,GAAGP,MAAM,gBAAgBK,MAAM,EAAE,CAAC;IAC5D,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,SAAS,GAAG,MAAAA,CAAOP,MAAM,EAAEQ,SAAS,EAAEC,QAAQ,GAAG,CAAC,KAAK;EAClE,IAAI;IACF,IAAIR,GAAG,GAAG,MAAMP,KAAK,CAACgB,IAAI,CAAC,GAAGf,MAAM,gBAAgBK,MAAM,kBAAkBQ,SAAS,aAAaC,QAAQ,EAAE,CAAC;IAC7G,OAAOR,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAOX,MAAM,EAAEQ,SAAS,EAAEC,QAAQ,KAAK;EACnE,IAAI;IACF,IAAIR,GAAG,GAAG,MAAMP,KAAK,CAACkB,GAAG,CAAC,GAAGjB,MAAM,gBAAgBK,MAAM,qBAAqBQ,SAAS,aAAaC,QAAQ,EAAE,CAAC;IAC/G,OAAOR,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAG,MAAAA,CAAOb,MAAM,EAAEQ,SAAS,KAAK;EACzD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACoB,MAAM,CAAC,GAAGnB,MAAM,gBAAgBK,MAAM,qBAAqBQ,SAAS,EAAE,CAAC;IAC7F,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,SAAS,GAAG,MAAOf,MAAM,IAAK;EACzC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACoB,MAAM,CAAC,GAAGnB,MAAM,gBAAgBK,MAAM,QAAQ,CAAC;IACrE,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,qBAAqB,GAAG,MAAOhB,MAAM,IAAK;EACrD,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACgB,IAAI,CAAC,GAAGf,MAAM,gBAAgBK,MAAM,qBAAqB,CAAC;IAChF,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}