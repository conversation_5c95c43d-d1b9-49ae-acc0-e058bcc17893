{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\AllOrders.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Fragment, useContext, useEffect } from \"react\";\nimport moment from \"moment\";\nimport { OrderContext } from \"./index\";\nimport { fetchData, editOrderReq, deleteOrderReq } from \"./Actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst AllCategory = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(OrderContext);\n  const {\n    orders,\n    loading\n  } = data;\n  useEffect(() => {\n    fetchData(dispatch);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 animate-spin text-gray-600\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 overflow-auto bg-white shadow-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table-auto border w-full my-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Total\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Transaction Id\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Created at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Updated at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: orders && orders.length > 0 ? orders.map((item, i) => {\n            return /*#__PURE__*/_jsxDEV(CategoryTable, {\n              order: item,\n              editOrder: (oId, type, status) => editOrderReq(oId, type, status, dispatch)\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"12\",\n              className: \"text-xl text-center font-semibold py-8\",\n              children: \"No order found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 mt-2\",\n        children: [\"Total \", orders && orders.length, \" order found\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n\n/* Single Category Component */\n_s(AllCategory, \"Yu3WSwDPvtUKWDFIw3QA4Aw+HMM=\");\n_c = AllCategory;\nconst CategoryTable = ({\n  order,\n  editOrder\n}) => {\n  _s2();\n  const {\n    dispatch\n  } = useContext(OrderContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      className: \"border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"w-48 hover:bg-gray-200 p-2 flex flex-col space-y-1\",\n        children: order.allProduct.map((product, i) => {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"w-8 h-8 object-cover object-center\",\n              src: `${apiURL}/uploads/products/${product.id.pImages[0]}`,\n              alt: \"productImage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: product.id.pName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [product.quantitiy, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center cursor-default\",\n        children: [order.status === \"Not processed\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), order.status === \"Processing\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-yellow-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), order.status === \"Shipped\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), order.status === \"Delivered\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), order.status === \"Cancelled\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: [\"$\", order.amount, \".00\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.transactionId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.user.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.user.email\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.phone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.address\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: moment(order.createdAt).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: moment(order.updatedAt).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => editOrder(order._id, true, order.status),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 fill-current text-green-500\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => deleteOrderReq(order._id, dispatch),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-red-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s2(CategoryTable, \"y0TpHjmA0G5gpKtNPtwKueTIMNE=\");\n_c2 = CategoryTable;\nexport default AllCategory;\nvar _c, _c2;\n$RefreshReg$(_c, \"AllCategory\");\n$RefreshReg$(_c2, \"CategoryTable\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useEffect", "moment", "OrderContext", "fetchData", "editOrderReq", "deleteOrderReq", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "AllCategory", "props", "_s", "data", "dispatch", "orders", "loading", "className", "children", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "item", "i", "CategoryTable", "order", "editOrder", "oId", "type", "status", "colSpan", "_c", "_s2", "allProduct", "product", "src", "id", "pImages", "alt", "pName", "quantitiy", "amount", "transactionId", "user", "name", "email", "phone", "address", "createdAt", "format", "updatedAt", "onClick", "e", "_id", "fillRule", "clipRule", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/AllOrders.js"], "sourcesContent": ["import React, { Fragment, useContext, useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nimport { OrderContext } from \"./index\";\r\nimport { fetchData, editOrderReq, deleteOrderReq } from \"./Actions\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst AllCategory = (props) => {\r\n  const { data, dispatch } = useContext(OrderContext);\r\n  const { orders, loading } = data;\r\n\r\n  useEffect(() => {\r\n    fetchData(dispatch);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 overflow-auto bg-white shadow-lg p-4\">\r\n        <table className=\"table-auto border w-full my-2\">\r\n          <thead>\r\n            <tr>\r\n              <th className=\"px-4 py-2 border\">Products</th>\r\n              <th className=\"px-4 py-2 border\">Status</th>\r\n              <th className=\"px-4 py-2 border\">Total</th>\r\n              <th className=\"px-4 py-2 border\">Transaction Id</th>\r\n              <th className=\"px-4 py-2 border\">Customer</th>\r\n              <th className=\"px-4 py-2 border\">Email</th>\r\n              <th className=\"px-4 py-2 border\">Phone</th>\r\n              <th className=\"px-4 py-2 border\">Address</th>\r\n              <th className=\"px-4 py-2 border\">Created at</th>\r\n              <th className=\"px-4 py-2 border\">Updated at</th>\r\n              <th className=\"px-4 py-2 border\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {orders && orders.length > 0 ? (\r\n              orders.map((item, i) => {\r\n                return (\r\n                  <CategoryTable\r\n                    key={i}\r\n                    order={item}\r\n                    editOrder={(oId, type, status) =>\r\n                      editOrderReq(oId, type, status, dispatch)\r\n                    }\r\n                  />\r\n                );\r\n              })\r\n            ) : (\r\n              <tr>\r\n                <td\r\n                  colSpan=\"12\"\r\n                  className=\"text-xl text-center font-semibold py-8\"\r\n                >\r\n                  No order found\r\n                </td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </table>\r\n        <div className=\"text-sm text-gray-600 mt-2\">\r\n          Total {orders && orders.length} order found\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\n/* Single Category Component */\r\nconst CategoryTable = ({ order, editOrder }) => {\r\n  const { dispatch } = useContext(OrderContext);\r\n\r\n  return (\r\n    <Fragment>\r\n      <tr className=\"border-b\">\r\n        <td className=\"w-48 hover:bg-gray-200 p-2 flex flex-col space-y-1\">\r\n          {order.allProduct.map((product, i) => {\r\n            return (\r\n              <span className=\"block flex items-center space-x-2\" key={i}>\r\n                <img\r\n                  className=\"w-8 h-8 object-cover object-center\"\r\n                  src={`${apiURL}/uploads/products/${product.id.pImages[0]}`}\r\n                  alt=\"productImage\"\r\n                />\r\n                <span>{product.id.pName}</span>\r\n                <span>{product.quantitiy}x</span>\r\n              </span>\r\n            );\r\n          })}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center cursor-default\">\r\n          {order.status === \"Not processed\" && (\r\n            <span className=\"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.status}\r\n            </span>\r\n          )}\r\n          {order.status === \"Processing\" && (\r\n            <span className=\"block text-yellow-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.status}\r\n            </span>\r\n          )}\r\n          {order.status === \"Shipped\" && (\r\n            <span className=\"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.status}\r\n            </span>\r\n          )}\r\n          {order.status === \"Delivered\" && (\r\n            <span className=\"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.status}\r\n            </span>\r\n          )}\r\n          {order.status === \"Cancelled\" && (\r\n            <span className=\"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.status}\r\n            </span>\r\n          )}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          ${order.amount}.00\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {order.transactionId}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.user.name}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {order.user.email}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.phone}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.address}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {moment(order.createdAt).format(\"lll\")}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {moment(order.updatedAt).format(\"lll\")}\r\n        </td>\r\n        <td className=\"p-2 flex items-center justify-center\">\r\n          <span\r\n            onClick={(e) => editOrder(order._id, true, order.status)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 fill-current text-green-500\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\" />\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </span>\r\n          <span\r\n            onClick={(e) => deleteOrderReq(order._id, dispatch)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 text-red-500\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\r\n              />\r\n            </svg>\r\n          </span>\r\n        </td>\r\n      </tr>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default AllCategory;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,WAAW,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGjB,UAAU,CAACG,YAAY,CAAC;EACnD,MAAM;IAAEe,MAAM;IAAEC;EAAQ,CAAC,GAAGH,IAAI;EAEhCf,SAAS,CAAC,MAAM;IACdG,SAAS,CAACa,QAAQ,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIE,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKY,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDb,OAAA;QACEY,SAAS,EAAC,sCAAsC;QAChDE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAJ,QAAA,eAElCb,OAAA;UACEkB,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAC,GAAG;UACfC,CAAC,EAAC;QAA6G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EACA,oBACEzB,OAAA,CAACT,QAAQ;IAAAsB,QAAA,eACPb,OAAA;MAAKY,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9Db,OAAA;QAAOY,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC9Cb,OAAA;UAAAa,QAAA,eACEb,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDzB,OAAA;cAAIY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRzB,OAAA;UAAAa,QAAA,EACGH,MAAM,IAAIA,MAAM,CAACgB,MAAM,GAAG,CAAC,GAC1BhB,MAAM,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;YACtB,oBACE7B,OAAA,CAAC8B,aAAa;cAEZC,KAAK,EAAEH,IAAK;cACZI,SAAS,EAAEA,CAACC,GAAG,EAAEC,IAAI,EAAEC,MAAM,KAC3BtC,YAAY,CAACoC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAE1B,QAAQ;YACzC,GAJIoB,CAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKP,CAAC;UAEN,CAAC,CAAC,gBAEFzB,OAAA;YAAAa,QAAA,eACEb,OAAA;cACEoC,OAAO,EAAC,IAAI;cACZxB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACnD;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACRzB,OAAA;QAAKY,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,QACpC,EAACH,MAAM,IAAIA,MAAM,CAACgB,MAAM,EAAC,cACjC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;;AAED;AAAAlB,EAAA,CAjFMF,WAAW;AAAAgC,EAAA,GAAXhC,WAAW;AAkFjB,MAAMyB,aAAa,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAU,CAAC,KAAK;EAAAM,GAAA;EAC9C,MAAM;IAAE7B;EAAS,CAAC,GAAGjB,UAAU,CAACG,YAAY,CAAC;EAE7C,oBACEK,OAAA,CAACT,QAAQ;IAAAsB,QAAA,eACPb,OAAA;MAAIY,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACtBb,OAAA;QAAIY,SAAS,EAAC,oDAAoD;QAAAC,QAAA,EAC/DkB,KAAK,CAACQ,UAAU,CAACZ,GAAG,CAAC,CAACa,OAAO,EAAEX,CAAC,KAAK;UACpC,oBACE7B,OAAA;YAAMY,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBACjDb,OAAA;cACEY,SAAS,EAAC,oCAAoC;cAC9C6B,GAAG,EAAE,GAAGxC,MAAM,qBAAqBuC,OAAO,CAACE,EAAE,CAACC,OAAO,CAAC,CAAC,CAAC,EAAG;cAC3DC,GAAG,EAAC;YAAc;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFzB,OAAA;cAAAa,QAAA,EAAO2B,OAAO,CAACE,EAAE,CAACG;YAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BzB,OAAA;cAAAa,QAAA,GAAO2B,OAAO,CAACM,SAAS,EAAC,GAAC;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAPsBI,CAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQpD,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,kDAAkD;QAAAC,QAAA,GAC7DkB,KAAK,CAACI,MAAM,KAAK,eAAe,iBAC/BnC,OAAA;UAAMY,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EACrFkB,KAAK,CAACI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP,EACAM,KAAK,CAACI,MAAM,KAAK,YAAY,iBAC5BnC,OAAA;UAAMY,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EACxFkB,KAAK,CAACI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP,EACAM,KAAK,CAACI,MAAM,KAAK,SAAS,iBACzBnC,OAAA;UAAMY,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACtFkB,KAAK,CAACI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP,EACAM,KAAK,CAACI,MAAM,KAAK,WAAW,iBAC3BnC,OAAA;UAAMY,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EACvFkB,KAAK,CAACI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP,EACAM,KAAK,CAACI,MAAM,KAAK,WAAW,iBAC3BnC,OAAA;UAAMY,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EACrFkB,KAAK,CAACI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,GAAC,GAC/C,EAACkB,KAAK,CAACgB,MAAM,EAAC,KACjB;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CkB,KAAK,CAACiB;MAAa;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEkB,KAAK,CAACkB,IAAI,CAACC;MAAI;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxEzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CkB,KAAK,CAACkB,IAAI,CAACE;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEkB,KAAK,CAACqB;MAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpEzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEkB,KAAK,CAACsB;MAAO;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtEzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CnB,MAAM,CAACqC,KAAK,CAACuB,SAAS,CAAC,CAACC,MAAM,CAAC,KAAK;MAAC;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CnB,MAAM,CAACqC,KAAK,CAACyB,SAAS,CAAC,CAACD,MAAM,CAAC,KAAK;MAAC;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACLzB,OAAA;QAAIY,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBAClDb,OAAA;UACEyD,OAAO,EAAGC,CAAC,IAAK1B,SAAS,CAACD,KAAK,CAAC4B,GAAG,EAAE,IAAI,EAAE5B,KAAK,CAACI,MAAM,CAAE;UACzDvB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhEb,OAAA;YACEY,SAAS,EAAC,qCAAqC;YAC/CE,IAAI,EAAC,cAAc;YACnBE,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,gBAElCb,OAAA;cAAMqB,CAAC,EAAC;YAA+E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FzB,OAAA;cACE4D,QAAQ,EAAC,SAAS;cAClBvC,CAAC,EAAC,wFAAwF;cAC1FwC,QAAQ,EAAC;YAAS;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzB,OAAA;UACEyD,OAAO,EAAGC,CAAC,IAAK5D,cAAc,CAACiC,KAAK,CAAC4B,GAAG,EAAElD,QAAQ,CAAE;UACpDG,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhEb,OAAA;YACEY,SAAS,EAAC,sBAAsB;YAChCE,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,eAElCb,OAAA;cACEkB,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEf,CAAC;AAACa,GAAA,CA5GIR,aAAa;AAAAgC,GAAA,GAAbhC,aAAa;AA8GnB,eAAezB,WAAW;AAAC,IAAAgC,EAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}