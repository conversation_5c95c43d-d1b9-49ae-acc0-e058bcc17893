{"ast": null, "code": "/* global __webpack_require__ */\nconst Refresh = require('react-refresh/runtime');\n\n/**\n * Extracts exports from a webpack module object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {*} An exports object from the module.\n */\nfunction getModuleExports(moduleId) {\n  return __webpack_require__.c[moduleId].exports;\n}\n\n/**\n * Calculates the signature of a React refresh boundary.\n * If this signature changes, it's unsafe to accept the boundary.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L795-L816).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {string[]} A React refresh boundary signature array.\n */\nfunction getReactRefreshBoundarySignature(moduleExports) {\n  const signature = [];\n  signature.push(Refresh.getFamilyByType(moduleExports));\n  if (moduleExports == null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return signature;\n  }\n  for (let key in moduleExports) {\n    if (key === '__esModule') {\n      continue;\n    }\n    signature.push(key);\n    signature.push(Refresh.getFamilyByType(moduleExports[key]));\n  }\n  return signature;\n}\n\n/**\n * Creates a helper that performs a delayed React refresh.\n * @returns {enqueueUpdate} A debounced React refresh function.\n */\nfunction createDebounceUpdate() {\n  /**\n   * A cached setTimeout handler.\n   * @type {number | undefined}\n   */\n  let refreshTimeout;\n\n  /**\n   * Performs react refresh on a delay and clears the error overlay.\n   * @param {function(): void} callback\n   * @returns {void}\n   */\n  function enqueueUpdate(callback) {\n    if (typeof refreshTimeout === 'undefined') {\n      refreshTimeout = setTimeout(function () {\n        refreshTimeout = undefined;\n        Refresh.performReactRefresh();\n        callback();\n      }, 30);\n    }\n  }\n  return enqueueUpdate;\n}\n\n/**\n * Checks if all exports are likely a React component.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L748-L774).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {boolean} Whether the exports are React component like.\n */\nfunction isReactRefreshBoundary(moduleExports) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    return true;\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return false;\n  }\n  let hasExports = false;\n  let areAllExportsComponents = true;\n  for (let key in moduleExports) {\n    hasExports = true;\n\n    // This is the ES Module indicator flag\n    if (key === '__esModule') {\n      continue;\n    }\n\n    // We can (and have to) safely execute getters here,\n    // as Webpack manually assigns harmony exports to getters,\n    // without any side-effects attached.\n    // Ref: https://github.com/webpack/webpack/blob/b93048643fe74de2a6931755911da1212df55897/lib/MainTemplate.js#L281\n    const exportValue = moduleExports[key];\n    if (!Refresh.isLikelyComponentType(exportValue)) {\n      areAllExportsComponents = false;\n    }\n  }\n  return hasExports && areAllExportsComponents;\n}\n\n/**\n * Checks if exports are likely a React component and registers them.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L818-L835).\n * @param {*} moduleExports A Webpack module exports object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {void}\n */\nfunction registerExportsForReactRefresh(moduleExports, moduleId) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    // Register module.exports if it is likely a component\n    Refresh.register(moduleExports, moduleId + ' %exports%');\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over the exports.\n    return;\n  }\n  for (let key in moduleExports) {\n    // Skip registering the ES Module indicator\n    if (key === '__esModule') {\n      continue;\n    }\n    const exportValue = moduleExports[key];\n    if (Refresh.isLikelyComponentType(exportValue)) {\n      const typeID = moduleId + ' %exports% ' + key;\n      Refresh.register(exportValue, typeID);\n    }\n  }\n}\n\n/**\n * Compares previous and next module objects to check for mutated boundaries.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L776-L792).\n * @param {*} prevExports The current Webpack module exports object.\n * @param {*} nextExports The next Webpack module exports object.\n * @returns {boolean} Whether the React refresh boundary should be invalidated.\n */\nfunction shouldInvalidateReactRefreshBoundary(prevExports, nextExports) {\n  const prevSignature = getReactRefreshBoundarySignature(prevExports);\n  const nextSignature = getReactRefreshBoundarySignature(nextExports);\n  if (prevSignature.length !== nextSignature.length) {\n    return true;\n  }\n  for (let i = 0; i < nextSignature.length; i += 1) {\n    if (prevSignature[i] !== nextSignature[i]) {\n      return true;\n    }\n  }\n  return false;\n}\nmodule.exports = Object.freeze({\n  enqueueUpdate: createDebounceUpdate(),\n  getModuleExports: getModuleExports,\n  isReactRefreshBoundary: isReactRefreshBoundary,\n  shouldInvalidateReactRefreshBoundary: shouldInvalidateReactRefreshBoundary,\n  registerExportsForReactRefresh: registerExportsForReactRefresh\n});", "map": {"version": 3, "names": ["Refresh", "require", "getModuleExports", "moduleId", "__webpack_require__", "c", "exports", "getReactRefreshBoundarySignature", "moduleExports", "signature", "push", "getFamilyByType", "key", "createDebounceUpdate", "refreshTimeout", "enqueueUpdate", "callback", "setTimeout", "undefined", "performReactRefresh", "isReactRefreshBoundary", "isLikelyComponentType", "hasExports", "areAllExportsComponents", "exportValue", "registerExportsForReactRefresh", "register", "typeID", "shouldInvalidateReactRefreshBoundary", "prevExports", "nextExports", "prevSignature", "nextSignature", "length", "i", "module", "Object", "freeze"], "sources": ["D:/ITSS_Reference/client/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js"], "sourcesContent": ["/* global __webpack_require__ */\nconst Refresh = require('react-refresh/runtime');\n\n/**\n * Extracts exports from a webpack module object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {*} An exports object from the module.\n */\nfunction getModuleExports(moduleId) {\n  return __webpack_require__.c[moduleId].exports;\n}\n\n/**\n * Calculates the signature of a React refresh boundary.\n * If this signature changes, it's unsafe to accept the boundary.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L795-L816).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {string[]} A React refresh boundary signature array.\n */\nfunction getReactRefreshBoundarySignature(moduleExports) {\n  const signature = [];\n  signature.push(Refresh.getFamilyByType(moduleExports));\n\n  if (moduleExports == null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return signature;\n  }\n\n  for (let key in moduleExports) {\n    if (key === '__esModule') {\n      continue;\n    }\n\n    signature.push(key);\n    signature.push(Refresh.getFamilyByType(moduleExports[key]));\n  }\n\n  return signature;\n}\n\n/**\n * Creates a helper that performs a delayed React refresh.\n * @returns {enqueueUpdate} A debounced React refresh function.\n */\nfunction createDebounceUpdate() {\n  /**\n   * A cached setTimeout handler.\n   * @type {number | undefined}\n   */\n  let refreshTimeout;\n\n  /**\n   * Performs react refresh on a delay and clears the error overlay.\n   * @param {function(): void} callback\n   * @returns {void}\n   */\n  function enqueueUpdate(callback) {\n    if (typeof refreshTimeout === 'undefined') {\n      refreshTimeout = setTimeout(function () {\n        refreshTimeout = undefined;\n        Refresh.performReactRefresh();\n        callback();\n      }, 30);\n    }\n  }\n\n  return enqueueUpdate;\n}\n\n/**\n * Checks if all exports are likely a React component.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L748-L774).\n * @param {*} moduleExports A Webpack module exports object.\n * @returns {boolean} Whether the exports are React component like.\n */\nfunction isReactRefreshBoundary(moduleExports) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    return true;\n  }\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over exports.\n    return false;\n  }\n\n  let hasExports = false;\n  let areAllExportsComponents = true;\n  for (let key in moduleExports) {\n    hasExports = true;\n\n    // This is the ES Module indicator flag\n    if (key === '__esModule') {\n      continue;\n    }\n\n    // We can (and have to) safely execute getters here,\n    // as Webpack manually assigns harmony exports to getters,\n    // without any side-effects attached.\n    // Ref: https://github.com/webpack/webpack/blob/b93048643fe74de2a6931755911da1212df55897/lib/MainTemplate.js#L281\n    const exportValue = moduleExports[key];\n    if (!Refresh.isLikelyComponentType(exportValue)) {\n      areAllExportsComponents = false;\n    }\n  }\n\n  return hasExports && areAllExportsComponents;\n}\n\n/**\n * Checks if exports are likely a React component and registers them.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/febdba2383113c88296c61e28e4ef6a7f4939fda/packages/metro/src/lib/polyfills/require.js#L818-L835).\n * @param {*} moduleExports A Webpack module exports object.\n * @param {string} moduleId A Webpack module ID.\n * @returns {void}\n */\nfunction registerExportsForReactRefresh(moduleExports, moduleId) {\n  if (Refresh.isLikelyComponentType(moduleExports)) {\n    // Register module.exports if it is likely a component\n    Refresh.register(moduleExports, moduleId + ' %exports%');\n  }\n\n  if (moduleExports === undefined || moduleExports === null || typeof moduleExports !== 'object') {\n    // Exit if we can't iterate over the exports.\n    return;\n  }\n\n  for (let key in moduleExports) {\n    // Skip registering the ES Module indicator\n    if (key === '__esModule') {\n      continue;\n    }\n\n    const exportValue = moduleExports[key];\n    if (Refresh.isLikelyComponentType(exportValue)) {\n      const typeID = moduleId + ' %exports% ' + key;\n      Refresh.register(exportValue, typeID);\n    }\n  }\n}\n\n/**\n * Compares previous and next module objects to check for mutated boundaries.\n *\n * This implementation is based on the one in [Metro](https://github.com/facebook/metro/blob/907d6af22ac6ebe58572be418e9253a90665ecbd/packages/metro/src/lib/polyfills/require.js#L776-L792).\n * @param {*} prevExports The current Webpack module exports object.\n * @param {*} nextExports The next Webpack module exports object.\n * @returns {boolean} Whether the React refresh boundary should be invalidated.\n */\nfunction shouldInvalidateReactRefreshBoundary(prevExports, nextExports) {\n  const prevSignature = getReactRefreshBoundarySignature(prevExports);\n  const nextSignature = getReactRefreshBoundarySignature(nextExports);\n\n  if (prevSignature.length !== nextSignature.length) {\n    return true;\n  }\n\n  for (let i = 0; i < nextSignature.length; i += 1) {\n    if (prevSignature[i] !== nextSignature[i]) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nmodule.exports = Object.freeze({\n  enqueueUpdate: createDebounceUpdate(),\n  getModuleExports: getModuleExports,\n  isReactRefreshBoundary: isReactRefreshBoundary,\n  shouldInvalidateReactRefreshBoundary: shouldInvalidateReactRefreshBoundary,\n  registerExportsForReactRefresh: registerExportsForReactRefresh,\n});\n"], "mappings": "AAAA;AACA,MAAMA,OAAO,GAAGC,OAAO,CAAC,uBAAuB,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,OAAOC,mBAAmB,CAACC,CAAC,CAACF,QAAQ,CAAC,CAACG,OAAO;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgCA,CAACC,aAAa,EAAE;EACvD,MAAMC,SAAS,GAAG,EAAE;EACpBA,SAAS,CAACC,IAAI,CAACV,OAAO,CAACW,eAAe,CAACH,aAAa,CAAC,CAAC;EAEtD,IAAIA,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9D;IACA,OAAOC,SAAS;EAClB;EAEA,KAAK,IAAIG,GAAG,IAAIJ,aAAa,EAAE;IAC7B,IAAII,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;IAEAH,SAAS,CAACC,IAAI,CAACE,GAAG,CAAC;IACnBH,SAAS,CAACC,IAAI,CAACV,OAAO,CAACW,eAAe,CAACH,aAAa,CAACI,GAAG,CAAC,CAAC,CAAC;EAC7D;EAEA,OAAOH,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASI,oBAAoBA,CAAA,EAAG;EAC9B;AACF;AACA;AACA;EACE,IAAIC,cAAc;;EAElB;AACF;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI,OAAOF,cAAc,KAAK,WAAW,EAAE;MACzCA,cAAc,GAAGG,UAAU,CAAC,YAAY;QACtCH,cAAc,GAAGI,SAAS;QAC1BlB,OAAO,CAACmB,mBAAmB,CAAC,CAAC;QAC7BH,QAAQ,CAAC,CAAC;MACZ,CAAC,EAAE,EAAE,CAAC;IACR;EACF;EAEA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,sBAAsBA,CAACZ,aAAa,EAAE;EAC7C,IAAIR,OAAO,CAACqB,qBAAqB,CAACb,aAAa,CAAC,EAAE;IAChD,OAAO,IAAI;EACb;EACA,IAAIA,aAAa,KAAKU,SAAS,IAAIV,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9F;IACA,OAAO,KAAK;EACd;EAEA,IAAIc,UAAU,GAAG,KAAK;EACtB,IAAIC,uBAAuB,GAAG,IAAI;EAClC,KAAK,IAAIX,GAAG,IAAIJ,aAAa,EAAE;IAC7Bc,UAAU,GAAG,IAAI;;IAEjB;IACA,IAAIV,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;;IAEA;IACA;IACA;IACA;IACA,MAAMY,WAAW,GAAGhB,aAAa,CAACI,GAAG,CAAC;IACtC,IAAI,CAACZ,OAAO,CAACqB,qBAAqB,CAACG,WAAW,CAAC,EAAE;MAC/CD,uBAAuB,GAAG,KAAK;IACjC;EACF;EAEA,OAAOD,UAAU,IAAIC,uBAAuB;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,8BAA8BA,CAACjB,aAAa,EAAEL,QAAQ,EAAE;EAC/D,IAAIH,OAAO,CAACqB,qBAAqB,CAACb,aAAa,CAAC,EAAE;IAChD;IACAR,OAAO,CAAC0B,QAAQ,CAAClB,aAAa,EAAEL,QAAQ,GAAG,YAAY,CAAC;EAC1D;EAEA,IAAIK,aAAa,KAAKU,SAAS,IAAIV,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC9F;IACA;EACF;EAEA,KAAK,IAAII,GAAG,IAAIJ,aAAa,EAAE;IAC7B;IACA,IAAII,GAAG,KAAK,YAAY,EAAE;MACxB;IACF;IAEA,MAAMY,WAAW,GAAGhB,aAAa,CAACI,GAAG,CAAC;IACtC,IAAIZ,OAAO,CAACqB,qBAAqB,CAACG,WAAW,CAAC,EAAE;MAC9C,MAAMG,MAAM,GAAGxB,QAAQ,GAAG,aAAa,GAAGS,GAAG;MAC7CZ,OAAO,CAAC0B,QAAQ,CAACF,WAAW,EAAEG,MAAM,CAAC;IACvC;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oCAAoCA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtE,MAAMC,aAAa,GAAGxB,gCAAgC,CAACsB,WAAW,CAAC;EACnE,MAAMG,aAAa,GAAGzB,gCAAgC,CAACuB,WAAW,CAAC;EAEnE,IAAIC,aAAa,CAACE,MAAM,KAAKD,aAAa,CAACC,MAAM,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACC,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;IAChD,IAAIH,aAAa,CAACG,CAAC,CAAC,KAAKF,aAAa,CAACE,CAAC,CAAC,EAAE;MACzC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEAC,MAAM,CAAC7B,OAAO,GAAG8B,MAAM,CAACC,MAAM,CAAC;EAC7BtB,aAAa,EAAEF,oBAAoB,CAAC,CAAC;EACrCX,gBAAgB,EAAEA,gBAAgB;EAClCkB,sBAAsB,EAAEA,sBAAsB;EAC9CQ,oCAAoC,EAAEA,oCAAoC;EAC1EH,8BAA8B,EAAEA;AAClC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}