{"ast": null, "code": "export const homeState = {\n  categoryListDropdown: false,\n  filterListDropdown: false,\n  searchDropdown: false,\n  products: null,\n  loading: false,\n  sliderImages: []\n};\nexport const homeReducer = (state, action) => {\n  switch (action.type) {\n    case \"categoryListDropdown\":\n      return {\n        ...state,\n        categoryListDropdown: action.payload,\n        filterListDropdown: false,\n        searchDropdown: false\n      };\n    case \"filterListDropdown\":\n      return {\n        ...state,\n        categoryListDropdown: false,\n        filterListDropdown: action.payload,\n        searchDropdown: false\n      };\n    case \"searchDropdown\":\n      return {\n        ...state,\n        categoryListDropdown: false,\n        filterListDropdown: false,\n        searchDropdown: action.payload\n      };\n    case \"setProducts\":\n      return {\n        ...state,\n        products: action.payload\n      };\n    case \"searchHandleInReducer\":\n      return {\n        ...state,\n        products: action.productArray && action.productArray.filter(item => {\n          if (item.name.toUpperCase().indexOf(action.payload.toUpperCase()) !== -1) {\n            return item;\n          }\n          return null;\n        })\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    case \"sliderImages\":\n      return {\n        ...state,\n        sliderImages: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["homeState", "categoryListDropdown", "filterListDropdown", "searchDropdown", "products", "loading", "sliderImages", "homeReducer", "state", "action", "type", "payload", "productArray", "filter", "item", "name", "toUpperCase", "indexOf"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["export const homeState = {\r\n  categoryListDropdown: false,\r\n  filterListDropdown: false,\r\n  searchDropdown: false,\r\n  products: null,\r\n  loading: false,\r\n  sliderImages: [],\r\n};\r\n\r\nexport const homeReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"categoryListDropdown\":\r\n      return {\r\n        ...state,\r\n        categoryListDropdown: action.payload,\r\n        filterListDropdown: false,\r\n        searchDropdown: false,\r\n      };\r\n    case \"filterListDropdown\":\r\n      return {\r\n        ...state,\r\n        categoryListDropdown: false,\r\n        filterListDropdown: action.payload,\r\n        searchDropdown: false,\r\n      };\r\n    case \"searchDropdown\":\r\n      return {\r\n        ...state,\r\n        categoryListDropdown: false,\r\n        filterListDropdown: false,\r\n        searchDropdown: action.payload,\r\n      };\r\n    case \"setProducts\":\r\n      return {\r\n        ...state,\r\n        products: action.payload,\r\n      };\r\n    case \"searchHandleInReducer\":\r\n      return {\r\n        ...state,\r\n        products:\r\n          action.productArray &&\r\n          action.productArray.filter((item) => {\r\n            if (\r\n              item.name.toUpperCase().indexOf(action.payload.toUpperCase()) !==\r\n              -1\r\n            ) {\r\n              return item;\r\n            }\r\n            return null;\r\n          }),\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    case \"sliderImages\":\r\n      return {\r\n        ...state,\r\n        sliderImages: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,GAAG;EACvBC,oBAAoB,EAAE,KAAK;EAC3BC,kBAAkB,EAAE,KAAK;EACzBC,cAAc,EAAE,KAAK;EACrBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE;AAChB,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC5C,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,sBAAsB;MACzB,OAAO;QACL,GAAGF,KAAK;QACRP,oBAAoB,EAAEQ,MAAM,CAACE,OAAO;QACpCT,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAK,oBAAoB;MACvB,OAAO;QACL,GAAGK,KAAK;QACRP,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAEO,MAAM,CAACE,OAAO;QAClCR,cAAc,EAAE;MAClB,CAAC;IACH,KAAK,gBAAgB;MACnB,OAAO;QACL,GAAGK,KAAK;QACRP,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAEM,MAAM,CAACE;MACzB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRJ,QAAQ,EAAEK,MAAM,CAACE;MACnB,CAAC;IACH,KAAK,uBAAuB;MAC1B,OAAO;QACL,GAAGH,KAAK;QACRJ,QAAQ,EACNK,MAAM,CAACG,YAAY,IACnBH,MAAM,CAACG,YAAY,CAACC,MAAM,CAAEC,IAAI,IAAK;UACnC,IACEA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACR,MAAM,CAACE,OAAO,CAACK,WAAW,CAAC,CAAC,CAAC,KAC7D,CAAC,CAAC,EACF;YACA,OAAOF,IAAI;UACb;UACA,OAAO,IAAI;QACb,CAAC;MACL,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGN,KAAK;QACRH,OAAO,EAAEI,MAAM,CAACE;MAClB,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGH,KAAK;QACRF,YAAY,EAAEG,MAAM,CAACE;MACvB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}