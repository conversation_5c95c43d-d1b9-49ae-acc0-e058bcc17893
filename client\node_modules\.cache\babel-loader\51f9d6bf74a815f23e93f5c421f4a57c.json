{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData(config.data, config.headers, config.transformRequest);\n\n  // Flatten headers\n  config.headers = utils.merge(config.headers.common || {}, config.headers[config.method] || {}, config.headers);\n  utils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch', 'common'], function cleanHeaderConfig(method) {\n    delete config.headers[method];\n  });\n  var adapter = config.adapter || defaults.adapter;\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData(response.data, response.headers, config.transformResponse);\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData(reason.response.data, reason.response.headers, config.transformResponse);\n      }\n    }\n    return Promise.reject(reason);\n  });\n};", "map": {"version": 3, "names": ["utils", "require", "transformData", "isCancel", "defaults", "throwIfCancellationRequested", "config", "cancelToken", "throwIfRequested", "module", "exports", "dispatchRequest", "headers", "data", "transformRequest", "merge", "common", "method", "for<PERSON>ach", "cleanHeaderConfig", "adapter", "then", "onAdapterResolution", "response", "transformResponse", "onAdapterRejection", "reason", "Promise", "reject"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/dispatchRequest.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData(\n    config.data,\n    config.headers,\n    config.transformRequest\n  );\n\n  // Flatten headers\n  config.headers = utils.merge(\n    config.headers.common || {},\n    config.headers[config.method] || {},\n    config.headers\n  );\n\n  utils.forEach(\n    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n    function cleanHeaderConfig(method) {\n      delete config.headers[method];\n    }\n  );\n\n  var adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData(\n      response.data,\n      response.headers,\n      config.transformResponse\n    );\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData(\n          reason.response.data,\n          reason.response.headers,\n          config.transformResponse\n        );\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC9C,IAAIE,QAAQ,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAC5C,IAAIG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA,SAASI,4BAA4BA,CAACC,MAAM,EAAE;EAC5C,IAAIA,MAAM,CAACC,WAAW,EAAE;IACtBD,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAAC,CAAC;EACvC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAACL,MAAM,EAAE;EAChDD,4BAA4B,CAACC,MAAM,CAAC;;EAEpC;EACAA,MAAM,CAACM,OAAO,GAAGN,MAAM,CAACM,OAAO,IAAI,CAAC,CAAC;;EAErC;EACAN,MAAM,CAACO,IAAI,GAAGX,aAAa,CACzBI,MAAM,CAACO,IAAI,EACXP,MAAM,CAACM,OAAO,EACdN,MAAM,CAACQ,gBACT,CAAC;;EAED;EACAR,MAAM,CAACM,OAAO,GAAGZ,KAAK,CAACe,KAAK,CAC1BT,MAAM,CAACM,OAAO,CAACI,MAAM,IAAI,CAAC,CAAC,EAC3BV,MAAM,CAACM,OAAO,CAACN,MAAM,CAACW,MAAM,CAAC,IAAI,CAAC,CAAC,EACnCX,MAAM,CAACM,OACT,CAAC;EAEDZ,KAAK,CAACkB,OAAO,CACX,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,EAC3D,SAASC,iBAAiBA,CAACF,MAAM,EAAE;IACjC,OAAOX,MAAM,CAACM,OAAO,CAACK,MAAM,CAAC;EAC/B,CACF,CAAC;EAED,IAAIG,OAAO,GAAGd,MAAM,CAACc,OAAO,IAAIhB,QAAQ,CAACgB,OAAO;EAEhD,OAAOA,OAAO,CAACd,MAAM,CAAC,CAACe,IAAI,CAAC,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;IACjElB,4BAA4B,CAACC,MAAM,CAAC;;IAEpC;IACAiB,QAAQ,CAACV,IAAI,GAAGX,aAAa,CAC3BqB,QAAQ,CAACV,IAAI,EACbU,QAAQ,CAACX,OAAO,EAChBN,MAAM,CAACkB,iBACT,CAAC;IAED,OAAOD,QAAQ;EACjB,CAAC,EAAE,SAASE,kBAAkBA,CAACC,MAAM,EAAE;IACrC,IAAI,CAACvB,QAAQ,CAACuB,MAAM,CAAC,EAAE;MACrBrB,4BAA4B,CAACC,MAAM,CAAC;;MAEpC;MACA,IAAIoB,MAAM,IAAIA,MAAM,CAACH,QAAQ,EAAE;QAC7BG,MAAM,CAACH,QAAQ,CAACV,IAAI,GAAGX,aAAa,CAClCwB,MAAM,CAACH,QAAQ,CAACV,IAAI,EACpBa,MAAM,CAACH,QAAQ,CAACX,OAAO,EACvBN,MAAM,CAACkB,iBACT,CAAC;MACH;IACF;IAEA,OAAOG,OAAO,CAACC,MAAM,CAACF,MAAM,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}