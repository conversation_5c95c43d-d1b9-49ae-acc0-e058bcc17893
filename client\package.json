{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/material": "^5.12.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^0.20.0", "bootstrap": "^4.5.2", "braintree-web-drop-in-react": "^1.2.1", "moment": "^2.29.0", "notistack": "^2.0.5", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}