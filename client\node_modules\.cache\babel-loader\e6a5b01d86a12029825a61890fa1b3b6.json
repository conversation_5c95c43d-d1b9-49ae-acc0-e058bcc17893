{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\productDetails\\\\ProductDetailsSectionTwo.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Fragment, useContext, useEffect, useState } from \"react\";\nimport AllReviews from \"./AllReviews\";\nimport ReviewForm from \"./ReviewForm\";\nimport { ProductDetailsContext } from \"./\";\nimport { LayoutContext } from \"../layout\";\nimport { isAuthenticate } from \"../auth/fetchApi\";\nimport \"./style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Menu = () => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(ProductDetailsContext);\n  const {\n    data: layoutData\n  } = useContext(LayoutContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: e => dispatch({\n          type: \"menu\",\n          payload: true\n        }),\n        className: `${data.menu ? \"border-b-2 border-yellow-700\" : \"\"} px-4 py-3 cursor-pointer`,\n        children: \"Description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: e => dispatch({\n          type: \"menu\",\n          payload: false\n        }),\n        className: `${!data.menu ? \"border-b-2 border-yellow-700\" : \"\"} px-4 py-3 relative flex cursor-pointer`,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Reviews\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"absolute text-xs top-0 right-0 mt-2 bg-yellow-700 text-white rounded px-1\",\n          children: layoutData.singleProductDetail.pRatingsReviews.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Menu, \"eEiR2egYEQNsv6tkCg0+/NqXNEw=\");\n_c = Menu;\nconst RatingReview = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AllReviews, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), isAuthenticate() ? /*#__PURE__*/_jsxDEV(ReviewForm, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-12 md:mx-16 lg:mx-20 xl:mx-24 bg-red-200 px-4 py-2 rounded mb-4\",\n      children: \"You need to login in for review\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c2 = RatingReview;\nconst ProductDetailsSectionTwo = props => {\n  _s2();\n  const {\n    data\n  } = useContext(ProductDetailsContext);\n  const {\n    data: layoutData\n  } = useContext(LayoutContext);\n  const [singleProduct, setSingleproduct] = useState({});\n  useEffect(() => {\n    setSingleproduct(layoutData.singleProductDetail ? layoutData.singleProductDetail : \"\");\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-12 md:my-8\",\n      children: [/*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), data.menu ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6\",\n        children: singleProduct.pDescription\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(RatingReview, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"m-4 md:mx-8 md:my-6 flex justify-center capitalize font-light tracking-widest bg-white border-t border-b text-gray-800 px-4 py-4 space-x-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Category :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: [\" \", singleProduct.pCategory ? singleProduct.pCategory.cName : \"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s2(ProductDetailsSectionTwo, \"6XUUD4aDQ0qcAQw17tb6WYJh6+E=\");\n_c3 = ProductDetailsSectionTwo;\nexport default ProductDetailsSectionTwo;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Menu\");\n$RefreshReg$(_c2, \"RatingReview\");\n$RefreshReg$(_c3, \"ProductDetailsSectionTwo\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useEffect", "useState", "AllReviews", "ReviewForm", "ProductDetailsContext", "LayoutContext", "isAuthenticate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "data", "dispatch", "layoutData", "children", "className", "onClick", "e", "type", "payload", "menu", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "singleProductDetail", "pRatingsReviews", "length", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c2", "ProductDetailsSectionTwo", "props", "_s2", "singleProduct", "setSingleproduct", "pDescription", "pCategory", "cName", "_c3", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/ProductDetailsSectionTwo.js"], "sourcesContent": ["import React, { Fragment, useContext, useEffect, useState } from \"react\";\r\nimport AllReviews from \"./AllReviews\";\r\nimport ReviewForm from \"./ReviewForm\";\r\n\r\nimport { ProductDetailsContext } from \"./\";\r\nimport { LayoutContext } from \"../layout\";\r\n\r\nimport { isAuthenticate } from \"../auth/fetchApi\";\r\n\r\nimport \"./style.css\";\r\n\r\nconst Menu = () => {\r\n  const { data, dispatch } = useContext(ProductDetailsContext);\r\n  const { data: layoutData } = useContext(LayoutContext);\r\n\r\n  return (\r\n    <Fragment>\r\n      <div className=\"flex flex-col md:flex-row items-center justify-center\">\r\n        <div\r\n          onClick={(e) => dispatch({ type: \"menu\", payload: true })}\r\n          className={`${\r\n            data.menu ? \"border-b-2 border-yellow-700\" : \"\"\r\n          } px-4 py-3 cursor-pointer`}\r\n        >\r\n          Description\r\n        </div>\r\n        <div\r\n          onClick={(e) => dispatch({ type: \"menu\", payload: false })}\r\n          className={`${\r\n            !data.menu ? \"border-b-2 border-yellow-700\" : \"\"\r\n          } px-4 py-3 relative flex cursor-pointer`}\r\n        >\r\n          <span>Reviews</span>\r\n          <span className=\"absolute text-xs top-0 right-0 mt-2 bg-yellow-700 text-white rounded px-1\">\r\n            {layoutData.singleProductDetail.pRatingsReviews.length}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst RatingReview = () => {\r\n  return (\r\n    <Fragment>\r\n      <AllReviews />\r\n      {isAuthenticate() ? (\r\n        <ReviewForm />\r\n      ) : (\r\n        <div className=\"mb-12 md:mx-16 lg:mx-20 xl:mx-24 bg-red-200 px-4 py-2 rounded mb-4\">\r\n          You need to login in for review\r\n        </div>\r\n      )}\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst ProductDetailsSectionTwo = (props) => {\r\n  const { data } = useContext(ProductDetailsContext);\r\n  const { data: layoutData } = useContext(LayoutContext);\r\n  const [singleProduct, setSingleproduct] = useState({});\r\n\r\n  useEffect(() => {\r\n    setSingleproduct(\r\n      layoutData.singleProductDetail ? layoutData.singleProductDetail : \"\"\r\n    );\r\n\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  return (\r\n    <Fragment>\r\n      <section className=\"m-4 md:mx-12 md:my-8\">\r\n        <Menu />\r\n        {data.menu ? (\r\n          <div className=\"mt-6\">{singleProduct.pDescription}</div>\r\n        ) : (\r\n          <RatingReview />\r\n        )}\r\n      </section>\r\n      <div className=\"m-4 md:mx-8 md:my-6 flex justify-center capitalize font-light tracking-widest bg-white border-t border-b text-gray-800 px-4 py-4 space-x-4\">\r\n        <div>\r\n          <span>Category :</span>\r\n          <span className=\"text-sm text-gray-600\">\r\n            {\" \"}\r\n            {singleProduct.pCategory ? singleProduct.pCategory.cName : \"\"}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductDetailsSectionTwo;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACxE,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AAErC,SAASC,qBAAqB,QAAQ,IAAI;AAC1C,SAASC,aAAa,QAAQ,WAAW;AAEzC,SAASC,cAAc,QAAQ,kBAAkB;AAEjD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGb,UAAU,CAACK,qBAAqB,CAAC;EAC5D,MAAM;IAAEO,IAAI,EAAEE;EAAW,CAAC,GAAGd,UAAU,CAACM,aAAa,CAAC;EAEtD,oBACEG,OAAA,CAACV,QAAQ;IAAAgB,QAAA,eACPN,OAAA;MAAKO,SAAS,EAAC,uDAAuD;MAAAD,QAAA,gBACpEN,OAAA;QACEQ,OAAO,EAAGC,CAAC,IAAKL,QAAQ,CAAC;UAAEM,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAE;QAC1DJ,SAAS,EAAE,GACTJ,IAAI,CAACS,IAAI,GAAG,8BAA8B,GAAG,EAAE,2BACrB;QAAAN,QAAA,EAC7B;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhB,OAAA;QACEQ,OAAO,EAAGC,CAAC,IAAKL,QAAQ,CAAC;UAAEM,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAE;QAC3DJ,SAAS,EAAE,GACT,CAACJ,IAAI,CAACS,IAAI,GAAG,8BAA8B,GAAG,EAAE,yCACR;QAAAN,QAAA,gBAE1CN,OAAA;UAAAM,QAAA,EAAM;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpBhB,OAAA;UAAMO,SAAS,EAAC,2EAA2E;UAAAD,QAAA,EACxFD,UAAU,CAACY,mBAAmB,CAACC,eAAe,CAACC;QAAM;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACd,EAAA,CA7BID,IAAI;AAAAmB,EAAA,GAAJnB,IAAI;AA+BV,MAAMoB,YAAY,GAAGA,CAAA,KAAM;EACzB,oBACErB,OAAA,CAACV,QAAQ;IAAAgB,QAAA,gBACPN,OAAA,CAACN,UAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACblB,cAAc,CAAC,CAAC,gBACfE,OAAA,CAACL,UAAU;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEdhB,OAAA;MAAKO,SAAS,EAAC,oEAAoE;MAAAD,QAAA,EAAC;IAEpF;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEf,CAAC;AAACM,GAAA,GAbID,YAAY;AAelB,MAAME,wBAAwB,GAAIC,KAAK,IAAK;EAAAC,GAAA;EAC1C,MAAM;IAAEtB;EAAK,CAAC,GAAGZ,UAAU,CAACK,qBAAqB,CAAC;EAClD,MAAM;IAAEO,IAAI,EAAEE;EAAW,CAAC,GAAGd,UAAU,CAACM,aAAa,CAAC;EACtD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACdmC,gBAAgB,CACdtB,UAAU,CAACY,mBAAmB,GAAGZ,UAAU,CAACY,mBAAmB,GAAG,EACpE,CAAC;;IAED;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEjB,OAAA,CAACV,QAAQ;IAAAgB,QAAA,gBACPN,OAAA;MAASO,SAAS,EAAC,sBAAsB;MAAAD,QAAA,gBACvCN,OAAA,CAACC,IAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACPb,IAAI,CAACS,IAAI,gBACRZ,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAD,QAAA,EAAEoB,aAAa,CAACE;MAAY;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAExDhB,OAAA,CAACqB,YAAY;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAChB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACVhB,OAAA;MAAKO,SAAS,EAAC,4IAA4I;MAAAD,QAAA,eACzJN,OAAA;QAAAM,QAAA,gBACEN,OAAA;UAAAM,QAAA,EAAM;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvBhB,OAAA;UAAMO,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GACpC,GAAG,EACHoB,aAAa,CAACG,SAAS,GAAGH,aAAa,CAACG,SAAS,CAACC,KAAK,GAAG,EAAE;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACS,GAAA,CAlCIF,wBAAwB;AAAAQ,GAAA,GAAxBR,wBAAwB;AAoC9B,eAAeA,wBAAwB;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}