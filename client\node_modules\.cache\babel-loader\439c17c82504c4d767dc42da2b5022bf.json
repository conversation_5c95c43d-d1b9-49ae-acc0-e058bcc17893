{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// ⚠️ WARNING: Backend does not have UserController\n// These functions return mock data or use alternative endpoints\n\nexport const getUserById = async uId => {\n  console.warn(\"getUserById: Backend does not have UserController. Returning mock user data.\");\n  try {\n    // Return mock user data based on JWT token\n    const jwt = localStorage.getItem(\"jwt\");\n    if (jwt) {\n      const userData = JSON.parse(jwt).user;\n      return {\n        success: true,\n        User: {\n          _id: userData.id || userData._id,\n          name: userData.username || userData.name || \"User\",\n          email: userData.email || \"<EMAIL>\",\n          phoneNumber: userData.phoneNumber || \"N/A\",\n          role: userData.role || 0\n        }\n      };\n    }\n    return {\n      success: false,\n      message: \"User not found\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const updatePersonalInformationFetch = async userData => {\n  console.warn(\"updatePersonalInformationFetch: Backend does not have UserController. This operation is not supported.\");\n  try {\n    // Mock successful update\n    return {\n      success: true,\n      message: \"User information updated successfully (mock response)\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const getOrderByUser = async uId => {\n  try {\n    // Use the available backend endpoint for order history\n    let res = await axios.get(`${apiURL}/api/v1/order/history/${uId}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\n      }\n    });\n    return {\n      success: true,\n      Order: res.data\n    };\n  } catch (error) {\n    console.log(\"Error fetching user orders:\", error);\n    // Return empty orders if endpoint fails\n    return {\n      success: true,\n      Order: []\n    };\n  }\n};\nexport const updatePassword = async formData => {\n  console.warn(\"updatePassword: Backend does not have UserController. This operation is not supported.\");\n  try {\n    // Mock successful password update\n    return {\n      success: true,\n      message: \"Password updated successfully (mock response)\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const cancelOrder = async orderId => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/order/cancel/${orderId}`, {}, {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(\"Error cancelling order:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getUserById", "uId", "console", "warn", "jwt", "localStorage", "getItem", "userData", "JSON", "parse", "user", "success", "User", "_id", "id", "name", "username", "email", "phoneNumber", "role", "message", "error", "log", "updatePersonalInformationFetch", "getOrderByUser", "res", "get", "headers", "token", "Order", "data", "updatePassword", "formData", "cancelOrder", "orderId", "put"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// ⚠️ WARNING: Backend does not have UserController\r\n// These functions return mock data or use alternative endpoints\r\n\r\nexport const getUserById = async (uId) => {\r\n  console.warn(\"getUserById: Backend does not have UserController. Returning mock user data.\");\r\n  try {\r\n    // Return mock user data based on JWT token\r\n    const jwt = localStorage.getItem(\"jwt\");\r\n    if (jwt) {\r\n      const userData = JSON.parse(jwt).user;\r\n      return {\r\n        success: true,\r\n        User: {\r\n          _id: userData.id || userData._id,\r\n          name: userData.username || userData.name || \"User\",\r\n          email: userData.email || \"<EMAIL>\",\r\n          phoneNumber: userData.phoneNumber || \"N/A\",\r\n          role: userData.role || 0\r\n        }\r\n      };\r\n    }\r\n    return { success: false, message: \"User not found\" };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updatePersonalInformationFetch = async (userData) => {\r\n  console.warn(\"updatePersonalInformationFetch: Backend does not have UserController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful update\r\n    return {\r\n      success: true,\r\n      message: \"User information updated successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getOrderByUser = async (uId) => {\r\n  try {\r\n    // Use the available backend endpoint for order history\r\n    let res = await axios.get(`${apiURL}/api/v1/order/history/${uId}`, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n      }\r\n    });\r\n    return {\r\n      success: true,\r\n      Order: res.data\r\n    };\r\n  } catch (error) {\r\n    console.log(\"Error fetching user orders:\", error);\r\n    // Return empty orders if endpoint fails\r\n    return {\r\n      success: true,\r\n      Order: []\r\n    };\r\n  }\r\n};\r\n\r\nexport const updatePassword = async (formData) => {\r\n  console.warn(\"updatePassword: Backend does not have UserController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful password update\r\n    return {\r\n      success: true,\r\n      message: \"Password updated successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const cancelOrder = async (orderId) => {\r\n  try {\r\n    let res = await axios.put(`${apiURL}/api/v1/order/cancel/${orderId}`, {}, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(\"Error cancelling order:\", error);\r\n    throw error;\r\n  }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA;;AAEA,OAAO,MAAMC,WAAW,GAAG,MAAOC,GAAG,IAAK;EACxCC,OAAO,CAACC,IAAI,CAAC,8EAA8E,CAAC;EAC5F,IAAI;IACF;IACA,MAAMC,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;IACvC,IAAIF,GAAG,EAAE;MACP,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC,CAACM,IAAI;MACrC,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,GAAG,EAAEN,QAAQ,CAACO,EAAE,IAAIP,QAAQ,CAACM,GAAG;UAChCE,IAAI,EAAER,QAAQ,CAACS,QAAQ,IAAIT,QAAQ,CAACQ,IAAI,IAAI,MAAM;UAClDE,KAAK,EAAEV,QAAQ,CAACU,KAAK,IAAI,kBAAkB;UAC3CC,WAAW,EAAEX,QAAQ,CAACW,WAAW,IAAI,KAAK;UAC1CC,IAAI,EAAEZ,QAAQ,CAACY,IAAI,IAAI;QACzB;MACF,CAAC;IACH;IACA,OAAO;MAAER,OAAO,EAAE,KAAK;MAAES,OAAO,EAAE;IAAiB,CAAC;EACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdnB,OAAO,CAACoB,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,8BAA8B,GAAG,MAAOhB,QAAQ,IAAK;EAChEL,OAAO,CAACC,IAAI,CAAC,wGAAwG,CAAC;EACtH,IAAI;IACF;IACA,OAAO;MACLQ,OAAO,EAAE,IAAI;MACbS,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdnB,OAAO,CAACoB,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,cAAc,GAAG,MAAOvB,GAAG,IAAK;EAC3C,IAAI;IACF;IACA,IAAIwB,GAAG,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,GAAG9B,MAAM,yBAAyBK,GAAG,EAAE,EAAE;MACjE0B,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAUtB,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACsB,KAAK,GAAG,EAAE;MAC7G;IACF,CAAC,CAAC;IACF,OAAO;MACLjB,OAAO,EAAE,IAAI;MACbkB,KAAK,EAAEJ,GAAG,CAACK;IACb,CAAC;EACH,CAAC,CAAC,OAAOT,KAAK,EAAE;IACdnB,OAAO,CAACoB,GAAG,CAAC,6BAA6B,EAAED,KAAK,CAAC;IACjD;IACA,OAAO;MACLV,OAAO,EAAE,IAAI;MACbkB,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAOC,QAAQ,IAAK;EAChD9B,OAAO,CAACC,IAAI,CAAC,wFAAwF,CAAC;EACtG,IAAI;IACF;IACA,OAAO;MACLQ,OAAO,EAAE,IAAI;MACbS,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdnB,OAAO,CAACoB,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMY,WAAW,GAAG,MAAOC,OAAO,IAAK;EAC5C,IAAI;IACF,IAAIT,GAAG,GAAG,MAAM9B,KAAK,CAACwC,GAAG,CAAC,GAAGvC,MAAM,wBAAwBsC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE;MACxEP,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,UAAUtB,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACsB,KAAK,GAAG,EAAE;MAC7G;IACF,CAAC,CAAC;IACF,OAAOH,GAAG,CAACK,IAAI;EACjB,CAAC,CAAC,OAAOT,KAAK,EAAE;IACdnB,OAAO,CAACoB,GAAG,CAAC,yBAAyB,EAAED,KAAK,CAAC;IAC7C,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}