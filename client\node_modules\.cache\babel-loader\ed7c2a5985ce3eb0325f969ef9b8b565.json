{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const DashboardData = async () => {\n  try {\n    let res = await axios.post(`${apiURL}/api/customize/dashboard-data`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\n_c = DashboardData;\nexport const getSliderImages = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/customize/get-slide-image`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const postUploadImage = async formData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/customize/upload-slide-image`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const postDeleteImage = async id => {\n  try {\n    let res = await axios.post(`${apiURL}/api/customize/delete-slide-image`, {\n      id\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nvar _c;\n$RefreshReg$(_c, \"DashboardData\");", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "DashboardData", "res", "post", "data", "error", "console", "log", "_c", "getSliderImages", "get", "postUploadImage", "formData", "postDeleteImage", "id", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/dashboardAdmin/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const DashboardData = async () => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/customize/dashboard-data`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getSliderImages = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/customize/get-slide-image`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const postUploadImage = async (formData) => {\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/customize/upload-slide-image`,\r\n      formData\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const postDeleteImage = async (id) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/customize/delete-slide-image`, {\r\n      id,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,MAAM,+BAA+B,CAAC;IACpE,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAACG,EAAA,GAPWP,aAAa;AAS1B,OAAO,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;EACzC,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMN,KAAK,CAACc,GAAG,CAAC,GAAGb,MAAM,gCAAgC,CAAC;IACpE,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMM,eAAe,GAAG,MAAOC,QAAQ,IAAK;EACjD,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMN,KAAK,CAACO,IAAI,CACxB,GAAGN,MAAM,mCAAmC,EAC5Ce,QACF,CAAC;IACD,OAAOV,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMQ,eAAe,GAAG,MAAOC,EAAE,IAAK;EAC3C,IAAI;IACF,IAAIZ,GAAG,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,MAAM,mCAAmC,EAAE;MACvEiB;IACF,CAAC,CAAC;IACF,OAAOZ,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAAC,IAAAG,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}