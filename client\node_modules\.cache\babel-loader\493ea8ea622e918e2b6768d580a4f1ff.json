{"ast": null, "code": "export const cartList = () => {\n  let carts = localStorage.getItem(\"cart\") ? JSON.parse(localStorage.getItem(\"cart\")) : null;\n  let list = [];\n  if (carts !== null) {\n    for (let cart of carts) {\n      list.push(cart.id);\n    }\n    return list;\n  } else {\n    return list = null;\n  }\n};\nexport const updateQuantity = (type, totalQuantitiy, quantitiy, setQuantitiy, setAlertq) => {\n  if (type === \"increase\") {\n    if (quantitiy === totalQuantitiy) {\n      setAlertq(true);\n    } else {\n      setQuantitiy(quantitiy + 1);\n    }\n  } else if (type === \"decrease\") {\n    if (quantitiy === 1) {\n      setQuantitiy(1);\n      setAlertq(false);\n    } else {\n      setQuantitiy(quantitiy - 1);\n    }\n  }\n};\nexport const slideImage = (type, active, count, setCount, pImages) => {\n  if (active === count) {\n    return true;\n  }\n  if (type === \"increase\") {\n    if (count === pImages.length - 1) {\n      setCount(0);\n    } else if (count < pImages.length) {\n      setCount(count + 1);\n    }\n  }\n};\nexport const inCart = id => {\n  if (localStorage.getItem(\"cart\")) {\n    let cartProducts = JSON.parse(localStorage.getItem(\"cart\"));\n    for (let product of cartProducts) {\n      if (product.id === id) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nexport const addToCart = (id, quantitiy, price, layoutDispatch, setQuantitiy, setAlertq, fetchData, totalCost) => {\n  let isObj = false;\n  let cart = localStorage.getItem(\"cart\") ? JSON.parse(localStorage.getItem(\"cart\")) : [];\n  if (cart.length > 0) {\n    cart.forEach(item => {\n      if (item.id === id) {\n        isObj = true;\n      }\n    });\n    if (!isObj) {\n      cart.push({\n        id,\n        quantitiy,\n        price\n      });\n      localStorage.setItem(\"cart\", JSON.stringify(cart));\n    }\n  } else {\n    cart.push({\n      id,\n      quantitiy,\n      price\n    });\n    localStorage.setItem(\"cart\", JSON.stringify(cart));\n  }\n  layoutDispatch({\n    type: \"inCart\",\n    payload: cartList()\n  });\n  layoutDispatch({\n    type: \"cartTotalCost\",\n    payload: totalCost()\n  });\n  setQuantitiy(1);\n  setAlertq(false);\n  fetchData();\n};", "map": {"version": 3, "names": ["cartList", "carts", "localStorage", "getItem", "JSON", "parse", "list", "cart", "push", "id", "updateQuantity", "type", "totalQuantitiy", "quantitiy", "setQuantitiy", "setAlertq", "slideImage", "active", "count", "setCount", "pImages", "length", "inCart", "cartProducts", "product", "addToCart", "price", "layoutDispatch", "fetchData", "totalCost", "isObj", "for<PERSON>ach", "item", "setItem", "stringify", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/Mixins.js"], "sourcesContent": ["export const cartList = () => {\r\n  let carts = localStorage.getItem(\"cart\")\r\n    ? JSON.parse(localStorage.getItem(\"cart\"))\r\n    : null;\r\n  let list = [];\r\n  if (carts !== null) {\r\n    for (let cart of carts) {\r\n      list.push(cart.id);\r\n    }\r\n    return list;\r\n  } else {\r\n    return (list = null);\r\n  }\r\n};\r\n\r\nexport const updateQuantity = (\r\n  type,\r\n  totalQuantitiy,\r\n  quantitiy,\r\n  setQuantitiy,\r\n  setAlertq\r\n) => {\r\n  if (type === \"increase\") {\r\n    if (quantitiy === totalQuantitiy) {\r\n      setAlertq(true);\r\n    } else {\r\n      setQuantitiy(quantitiy + 1);\r\n    }\r\n  } else if (type === \"decrease\") {\r\n    if (quantitiy === 1) {\r\n      setQuantitiy(1);\r\n      setAlertq(false);\r\n    } else {\r\n      setQuantitiy(quantitiy - 1);\r\n    }\r\n  }\r\n};\r\n\r\nexport const slideImage = (type, active, count, setCount, pImages) => {\r\n  if (active === count) {\r\n    return true;\r\n  }\r\n  if (type === \"increase\") {\r\n    if (count === pImages.length - 1) {\r\n      setCount(0);\r\n    } else if (count < pImages.length) {\r\n      setCount(count + 1);\r\n    }\r\n  }\r\n};\r\n\r\nexport const inCart = (id) => {\r\n  if (localStorage.getItem(\"cart\")) {\r\n    let cartProducts = JSON.parse(localStorage.getItem(\"cart\"));\r\n    for (let product of cartProducts) {\r\n      if (product.id === id) {\r\n        return true;\r\n      }\r\n    }\r\n  }\r\n  return false;\r\n};\r\n\r\nexport const addToCart = (\r\n  id,\r\n  quantitiy,\r\n  price,\r\n  layoutDispatch,\r\n  setQuantitiy,\r\n  setAlertq,\r\n  fetchData,\r\n  totalCost\r\n) => {\r\n  let isObj = false;\r\n  let cart = localStorage.getItem(\"cart\")\r\n    ? JSON.parse(localStorage.getItem(\"cart\"))\r\n    : [];\r\n  if (cart.length > 0) {\r\n    cart.forEach((item) => {\r\n      if (item.id === id) {\r\n        isObj = true;\r\n      }\r\n    });\r\n    if (!isObj) {\r\n      cart.push({ id, quantitiy, price });\r\n      localStorage.setItem(\"cart\", JSON.stringify(cart));\r\n    }\r\n  } else {\r\n    cart.push({ id, quantitiy, price });\r\n    localStorage.setItem(\"cart\", JSON.stringify(cart));\r\n  }\r\n  layoutDispatch({ type: \"inCart\", payload: cartList() });\r\n  layoutDispatch({ type: \"cartTotalCost\", payload: totalCost() });\r\n  setQuantitiy(1);\r\n  setAlertq(false);\r\n  fetchData();\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAGA,CAAA,KAAM;EAC5B,IAAIC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GACpCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GACxC,IAAI;EACR,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIL,KAAK,KAAK,IAAI,EAAE;IAClB,KAAK,IAAIM,IAAI,IAAIN,KAAK,EAAE;MACtBK,IAAI,CAACE,IAAI,CAACD,IAAI,CAACE,EAAE,CAAC;IACpB;IACA,OAAOH,IAAI;EACb,CAAC,MAAM;IACL,OAAQA,IAAI,GAAG,IAAI;EACrB;AACF,CAAC;AAED,OAAO,MAAMI,cAAc,GAAGA,CAC5BC,IAAI,EACJC,cAAc,EACdC,SAAS,EACTC,YAAY,EACZC,SAAS,KACN;EACH,IAAIJ,IAAI,KAAK,UAAU,EAAE;IACvB,IAAIE,SAAS,KAAKD,cAAc,EAAE;MAChCG,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,MAAM;MACLD,YAAY,CAACD,SAAS,GAAG,CAAC,CAAC;IAC7B;EACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIE,SAAS,KAAK,CAAC,EAAE;MACnBC,YAAY,CAAC,CAAC,CAAC;MACfC,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,MAAM;MACLD,YAAY,CAACD,SAAS,GAAG,CAAC,CAAC;IAC7B;EACF;AACF,CAAC;AAED,OAAO,MAAMG,UAAU,GAAGA,CAACL,IAAI,EAAEM,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACpE,IAAIH,MAAM,KAAKC,KAAK,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAIP,IAAI,KAAK,UAAU,EAAE;IACvB,IAAIO,KAAK,KAAKE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAChCF,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,MAAM,IAAID,KAAK,GAAGE,OAAO,CAACC,MAAM,EAAE;MACjCF,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC;IACrB;EACF;AACF,CAAC;AAED,OAAO,MAAMI,MAAM,GAAIb,EAAE,IAAK;EAC5B,IAAIP,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,EAAE;IAChC,IAAIoB,YAAY,GAAGnB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3D,KAAK,IAAIqB,OAAO,IAAID,YAAY,EAAE;MAChC,IAAIC,OAAO,CAACf,EAAE,KAAKA,EAAE,EAAE;QACrB,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd,CAAC;AAED,OAAO,MAAMgB,SAAS,GAAGA,CACvBhB,EAAE,EACFI,SAAS,EACTa,KAAK,EACLC,cAAc,EACdb,YAAY,EACZC,SAAS,EACTa,SAAS,EACTC,SAAS,KACN;EACH,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAIvB,IAAI,GAAGL,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GACnCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,GACxC,EAAE;EACN,IAAII,IAAI,CAACc,MAAM,GAAG,CAAC,EAAE;IACnBd,IAAI,CAACwB,OAAO,CAAEC,IAAI,IAAK;MACrB,IAAIA,IAAI,CAACvB,EAAE,KAAKA,EAAE,EAAE;QAClBqB,KAAK,GAAG,IAAI;MACd;IACF,CAAC,CAAC;IACF,IAAI,CAACA,KAAK,EAAE;MACVvB,IAAI,CAACC,IAAI,CAAC;QAAEC,EAAE;QAAEI,SAAS;QAAEa;MAAM,CAAC,CAAC;MACnCxB,YAAY,CAAC+B,OAAO,CAAC,MAAM,EAAE7B,IAAI,CAAC8B,SAAS,CAAC3B,IAAI,CAAC,CAAC;IACpD;EACF,CAAC,MAAM;IACLA,IAAI,CAACC,IAAI,CAAC;MAAEC,EAAE;MAAEI,SAAS;MAAEa;IAAM,CAAC,CAAC;IACnCxB,YAAY,CAAC+B,OAAO,CAAC,MAAM,EAAE7B,IAAI,CAAC8B,SAAS,CAAC3B,IAAI,CAAC,CAAC;EACpD;EACAoB,cAAc,CAAC;IAAEhB,IAAI,EAAE,QAAQ;IAAEwB,OAAO,EAAEnC,QAAQ,CAAC;EAAE,CAAC,CAAC;EACvD2B,cAAc,CAAC;IAAEhB,IAAI,EAAE,eAAe;IAAEwB,OAAO,EAAEN,SAAS,CAAC;EAAE,CAAC,CAAC;EAC/Df,YAAY,CAAC,CAAC,CAAC;EACfC,SAAS,CAAC,KAAK,CAAC;EAChBa,SAAS,CAAC,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}