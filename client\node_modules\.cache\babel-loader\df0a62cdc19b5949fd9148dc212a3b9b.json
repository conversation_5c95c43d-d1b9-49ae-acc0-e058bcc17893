{"ast": null, "code": "import resolveProps from '@mui/utils/resolveProps';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}", "map": {"version": 3, "names": ["resolveProps", "getThemeProps", "params", "theme", "name", "props", "components", "defaultProps"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/system/esm/useThemeProps/getThemeProps.js"], "sourcesContent": ["import resolveProps from '@mui/utils/resolveProps';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5C,MAAM;IACJC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGH,MAAM;EACV,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,IAAI,CAACD,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,CAACG,YAAY,EAAE;IAClG,OAAOF,KAAK;EACd;EACA,OAAOL,YAAY,CAACG,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,CAACG,YAAY,EAAEF,KAAK,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}