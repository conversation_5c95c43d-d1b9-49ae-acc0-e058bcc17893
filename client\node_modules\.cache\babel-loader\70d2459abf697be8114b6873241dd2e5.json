{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const cartListProduct = async (userId = 1) => {\n  try {\n    // Sử dụng API cart mới thay vì localStorage\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\n\n    // Convert cart data to match old format for compatibility\n    if (res.data && res.data.cartItems) {\n      const products = res.data.cartItems.map(item => ({\n        _id: item.productId,\n        pName: item.productName,\n        pPrice: item.price,\n        pImages: item.images || [],\n        quantity: item.quantity,\n        pCategory: {\n          cName: item.category || 'Unknown'\n        }\n      }));\n      return {\n        Products: products\n      };\n    }\n    return {\n      Products: []\n    };\n  } catch (error) {\n    console.log(\"⚠️ cartListProduct: Cart empty or error:\", error.message);\n    return {\n      Products: []\n    };\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "cartListProduct", "userId", "res", "get", "data", "cartItems", "products", "map", "item", "_id", "productId", "pName", "productName", "pPrice", "price", "pImages", "images", "quantity", "pCategory", "cName", "category", "Products", "error", "console", "log", "message"], "sources": ["D:/ITSS_Reference/client/src/components/shop/partials/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const cartListProduct = async (userId = 1) => {\r\n  try {\r\n    // Sử dụng API cart mới thay vì localStorage\r\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\r\n\r\n    // Convert cart data to match old format for compatibility\r\n    if (res.data && res.data.cartItems) {\r\n      const products = res.data.cartItems.map(item => ({\r\n        _id: item.productId,\r\n        pName: item.productName,\r\n        pPrice: item.price,\r\n        pImages: item.images || [],\r\n        quantity: item.quantity,\r\n        pCategory: { cName: item.category || 'Unknown' }\r\n      }));\r\n\r\n      return { Products: products };\r\n    }\r\n\r\n    return { Products: [] };\r\n  } catch (error) {\r\n    console.log(\"⚠️ cartListProduct: Cart empty or error:\", error.message);\r\n    return { Products: [] };\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,KAAK;EACnD,IAAI;IACF;IACA,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,GAAGP,MAAM,gBAAgBK,MAAM,EAAE,CAAC;;IAE5D;IACA,IAAIC,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACE,IAAI,CAACC,SAAS,EAAE;MAClC,MAAMC,QAAQ,GAAGJ,GAAG,CAACE,IAAI,CAACC,SAAS,CAACE,GAAG,CAACC,IAAI,KAAK;QAC/CC,GAAG,EAAED,IAAI,CAACE,SAAS;QACnBC,KAAK,EAAEH,IAAI,CAACI,WAAW;QACvBC,MAAM,EAAEL,IAAI,CAACM,KAAK;QAClBC,OAAO,EAAEP,IAAI,CAACQ,MAAM,IAAI,EAAE;QAC1BC,QAAQ,EAAET,IAAI,CAACS,QAAQ;QACvBC,SAAS,EAAE;UAAEC,KAAK,EAAEX,IAAI,CAACY,QAAQ,IAAI;QAAU;MACjD,CAAC,CAAC,CAAC;MAEH,OAAO;QAAEC,QAAQ,EAAEf;MAAS,CAAC;IAC/B;IAEA,OAAO;MAAEe,QAAQ,EAAE;IAAG,CAAC;EACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,KAAK,CAACG,OAAO,CAAC;IACtE,OAAO;MAAEJ,QAAQ,EAAE;IAAG,CAAC;EACzB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}