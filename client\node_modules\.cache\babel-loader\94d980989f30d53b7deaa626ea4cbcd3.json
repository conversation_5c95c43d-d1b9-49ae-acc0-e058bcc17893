{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\categories\\\\CategoryMenu.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext } from \"react\";\nimport { CategoryContext } from \"./index\";\nimport AddCategoryModal from \"./AddCategoryModal\";\nimport EditCategoryModal from \"./EditCategoryModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryMenu = props => {\n  _s();\n  const {\n    dispatch\n  } = useContext(CategoryContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col space-y-4 md:flex-row md:justify-between md:items-center w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: \"#303031\"\n          },\n          onClick: e => dispatch({\n            type: \"addCategoryModal\",\n            payload: true\n          }),\n          className: \"cursor-pointer rounded-full p-2 flex items-center justify-center text-gray-100 text-sm font-semibold uppercase\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-gray-100 mr-2\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), \"Add Category\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddCategoryModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditCategoryModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryMenu, \"y0TpHjmA0G5gpKtNPtwKueTIMNE=\");\n_c = CategoryMenu;\nexport default CategoryMenu;\nvar _c;\n$RefreshReg$(_c, \"CategoryMenu\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "CategoryContext", "AddCategoryModal", "EditCategoryModal", "jsxDEV", "_jsxDEV", "CategoryMenu", "props", "_s", "dispatch", "children", "className", "style", "background", "onClick", "e", "type", "payload", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/CategoryMenu.js"], "sourcesContent": ["import React, { Fragment, useContext } from \"react\";\r\nimport { CategoryContext } from \"./index\";\r\nimport AddCategoryModal from \"./AddCategoryModal\";\r\nimport EditCategoryModal from \"./EditCategoryModal\";\r\n\r\nconst CategoryMenu = (props) => {\r\n  const { dispatch } = useContext(CategoryContext);\r\n\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 flex items-center\">\r\n        <div className=\"flex flex-col space-y-4 md:flex-row md:justify-between md:items-center w-full\">\r\n          {/* It's open the add category modal */}\r\n          <div\r\n            style={{ background: \"#303031\" }}\r\n            onClick={(e) =>\r\n              dispatch({ type: \"addCategoryModal\", payload: true })\r\n            }\r\n            className=\"cursor-pointer rounded-full p-2 flex items-center justify-center text-gray-100 text-sm font-semibold uppercase\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 text-gray-100 mr-2\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Add Category\r\n          </div>\r\n        </div>\r\n        <AddCategoryModal />\r\n        <EditCategoryModal />\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default CategoryMenu;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,eAAe,QAAQ,SAAS;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAY,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAS,CAAC,GAAGT,UAAU,CAACC,eAAe,CAAC;EAEhD,oBACEI,OAAA,CAACN,QAAQ;IAAAW,QAAA,eACPL,OAAA;MAAKM,SAAS,EAAC,8BAA8B;MAAAD,QAAA,gBAC3CL,OAAA;QAAKM,SAAS,EAAC,+EAA+E;QAAAD,QAAA,eAE5FL,OAAA;UACEO,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAU,CAAE;UACjCC,OAAO,EAAGC,CAAC,IACTN,QAAQ,CAAC;YAAEO,IAAI,EAAE,kBAAkB;YAAEC,OAAO,EAAE;UAAK,CAAC,CACrD;UACDN,SAAS,EAAC,gHAAgH;UAAAD,QAAA,gBAE1HL,OAAA;YACEM,SAAS,EAAC,4BAA4B;YACtCO,IAAI,EAAC,cAAc;YACnBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAV,QAAA,eAElCL,OAAA;cACEgB,QAAQ,EAAC,SAAS;cAClBC,CAAC,EAAC,4GAA4G;cAC9GC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtB,OAAA,CAACH,gBAAgB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBtB,OAAA,CAACF,iBAAiB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACnB,EAAA,CAnCIF,YAAY;AAAAsB,EAAA,GAAZtB,YAAY;AAqClB,eAAeA,YAAY;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}