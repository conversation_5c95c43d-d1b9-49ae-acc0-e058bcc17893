{"ast": null, "code": "// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://bit.ly/CRA-PWA\n\nconst isLocalhost = Boolean(window.location.hostname === 'localhost' ||\n// [::1] is the IPv6 localhost address.\nwindow.location.hostname === '[::1]' ||\n// *********/8 are considered localhost for IPv4.\nwindow.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));\nexport function register(config) {\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log('This web app is being served cache-first by a service ' + 'worker. To learn more, visit https://bit.ly/CRA-PWA');\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\nfunction registerValidSW(swUrl, config) {\n  navigator.serviceWorker.register(swUrl).then(registration => {\n    registration.onupdatefound = () => {\n      const installingWorker = registration.installing;\n      if (installingWorker == null) {\n        return;\n      }\n      installingWorker.onstatechange = () => {\n        if (installingWorker.state === 'installed') {\n          if (navigator.serviceWorker.controller) {\n            // At this point, the updated precached content has been fetched,\n            // but the previous service worker will still serve the older\n            // content until all client tabs are closed.\n            console.log('New content is available and will be used when all ' + 'tabs for this page are closed. See https://bit.ly/CRA-PWA.');\n\n            // Execute callback\n            if (config && config.onUpdate) {\n              config.onUpdate(registration);\n            }\n          } else {\n            // At this point, everything has been precached.\n            // It's the perfect time to display a\n            // \"Content is cached for offline use.\" message.\n            console.log('Content is cached for offline use.');\n\n            // Execute callback\n            if (config && config.onSuccess) {\n              config.onSuccess(registration);\n            }\n          }\n        }\n      };\n    };\n  }).catch(error => {\n    console.error('Error during service worker registration:', error);\n  });\n}\nfunction checkValidServiceWorker(swUrl, config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: {\n      'Service-Worker': 'script'\n    }\n  }).then(response => {\n    // Ensure service worker exists, and that we really are getting a JS file.\n    const contentType = response.headers.get('content-type');\n    if (response.status === 404 || contentType != null && contentType.indexOf('javascript') === -1) {\n      // No service worker found. Probably a different app. Reload the page.\n      navigator.serviceWorker.ready.then(registration => {\n        registration.unregister().then(() => {\n          window.location.reload();\n        });\n      });\n    } else {\n      // Service worker found. Proceed as normal.\n      registerValidSW(swUrl, config);\n    }\n  }).catch(() => {\n    console.log('No internet connection found. App is running in offline mode.');\n  });\n}\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready.then(registration => {\n      registration.unregister();\n    }).catch(error => {\n      console.error(error.message);\n    });\n  }\n}", "map": {"version": 3, "names": ["isLocalhost", "Boolean", "window", "location", "hostname", "match", "register", "config", "process", "env", "NODE_ENV", "navigator", "publicUrl", "URL", "PUBLIC_URL", "href", "origin", "addEventListener", "swUrl", "checkValidServiceWorker", "serviceWorker", "ready", "then", "console", "log", "registerValidSW", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "state", "controller", "onUpdate", "onSuccess", "catch", "error", "fetch", "headers", "response", "contentType", "get", "status", "indexOf", "unregister", "reload", "message"], "sources": ["D:/ITSS_Reference/client/src/serviceWorker.js"], "sourcesContent": ["// This optional code is used to register a service worker.\r\n// register() is not called by default.\r\n\r\n// This lets the app load faster on subsequent visits in production, and gives\r\n// it offline capabilities. However, it also means that developers (and users)\r\n// will only see deployed updates on subsequent visits to a page, after all the\r\n// existing tabs open on the page have been closed, since previously cached\r\n// resources are updated in the background.\r\n\r\n// To learn more about the benefits of this model and instructions on how to\r\n// opt-in, read https://bit.ly/CRA-PWA\r\n\r\nconst isLocalhost = Boolean(\r\n  window.location.hostname === 'localhost' ||\r\n    // [::1] is the IPv6 localhost address.\r\n    window.location.hostname === '[::1]' ||\r\n    // *********/8 are considered localhost for IPv4.\r\n    window.location.hostname.match(\r\n      /^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/\r\n    )\r\n);\r\n\r\nexport function register(config) {\r\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\r\n    // The URL constructor is available in all browsers that support SW.\r\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\r\n    if (publicUrl.origin !== window.location.origin) {\r\n      // Our service worker won't work if PUBLIC_URL is on a different origin\r\n      // from what our page is served on. This might happen if a CDN is used to\r\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\r\n      return;\r\n    }\r\n\r\n    window.addEventListener('load', () => {\r\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\r\n\r\n      if (isLocalhost) {\r\n        // This is running on localhost. Let's check if a service worker still exists or not.\r\n        checkValidServiceWorker(swUrl, config);\r\n\r\n        // Add some additional logging to localhost, pointing developers to the\r\n        // service worker/PWA documentation.\r\n        navigator.serviceWorker.ready.then(() => {\r\n          console.log(\r\n            'This web app is being served cache-first by a service ' +\r\n              'worker. To learn more, visit https://bit.ly/CRA-PWA'\r\n          );\r\n        });\r\n      } else {\r\n        // Is not localhost. Just register service worker\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nfunction registerValidSW(swUrl, config) {\r\n  navigator.serviceWorker\r\n    .register(swUrl)\r\n    .then(registration => {\r\n      registration.onupdatefound = () => {\r\n        const installingWorker = registration.installing;\r\n        if (installingWorker == null) {\r\n          return;\r\n        }\r\n        installingWorker.onstatechange = () => {\r\n          if (installingWorker.state === 'installed') {\r\n            if (navigator.serviceWorker.controller) {\r\n              // At this point, the updated precached content has been fetched,\r\n              // but the previous service worker will still serve the older\r\n              // content until all client tabs are closed.\r\n              console.log(\r\n                'New content is available and will be used when all ' +\r\n                  'tabs for this page are closed. See https://bit.ly/CRA-PWA.'\r\n              );\r\n\r\n              // Execute callback\r\n              if (config && config.onUpdate) {\r\n                config.onUpdate(registration);\r\n              }\r\n            } else {\r\n              // At this point, everything has been precached.\r\n              // It's the perfect time to display a\r\n              // \"Content is cached for offline use.\" message.\r\n              console.log('Content is cached for offline use.');\r\n\r\n              // Execute callback\r\n              if (config && config.onSuccess) {\r\n                config.onSuccess(registration);\r\n              }\r\n            }\r\n          }\r\n        };\r\n      };\r\n    })\r\n    .catch(error => {\r\n      console.error('Error during service worker registration:', error);\r\n    });\r\n}\r\n\r\nfunction checkValidServiceWorker(swUrl, config) {\r\n  // Check if the service worker can be found. If it can't reload the page.\r\n  fetch(swUrl, {\r\n    headers: { 'Service-Worker': 'script' },\r\n  })\r\n    .then(response => {\r\n      // Ensure service worker exists, and that we really are getting a JS file.\r\n      const contentType = response.headers.get('content-type');\r\n      if (\r\n        response.status === 404 ||\r\n        (contentType != null && contentType.indexOf('javascript') === -1)\r\n      ) {\r\n        // No service worker found. Probably a different app. Reload the page.\r\n        navigator.serviceWorker.ready.then(registration => {\r\n          registration.unregister().then(() => {\r\n            window.location.reload();\r\n          });\r\n        });\r\n      } else {\r\n        // Service worker found. Proceed as normal.\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    })\r\n    .catch(() => {\r\n      console.log(\r\n        'No internet connection found. App is running in offline mode.'\r\n      );\r\n    });\r\n}\r\n\r\nexport function unregister() {\r\n  if ('serviceWorker' in navigator) {\r\n    navigator.serviceWorker.ready\r\n      .then(registration => {\r\n        registration.unregister();\r\n      })\r\n      .catch(error => {\r\n        console.error(error.message);\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,MAAMA,WAAW,GAAGC,OAAO,CACzBC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;AACtC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,OAAO;AACpC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,KAAK,CAC5B,wDACF,CACJ,CAAC;AAED,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAE;EAC/B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,eAAe,IAAIC,SAAS,EAAE;IACzE;IACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAACL,OAAO,CAACC,GAAG,CAACK,UAAU,EAAEZ,MAAM,CAACC,QAAQ,CAACY,IAAI,CAAC;IACvE,IAAIH,SAAS,CAACI,MAAM,KAAKd,MAAM,CAACC,QAAQ,CAACa,MAAM,EAAE;MAC/C;MACA;MACA;MACA;IACF;IAEAd,MAAM,CAACe,gBAAgB,CAAC,MAAM,EAAE,MAAM;MACpC,MAAMC,KAAK,GAAG,GAAGV,OAAO,CAACC,GAAG,CAACK,UAAU,oBAAoB;MAE3D,IAAId,WAAW,EAAE;QACf;QACAmB,uBAAuB,CAACD,KAAK,EAAEX,MAAM,CAAC;;QAEtC;QACA;QACAI,SAAS,CAACS,aAAa,CAACC,KAAK,CAACC,IAAI,CAAC,MAAM;UACvCC,OAAO,CAACC,GAAG,CACT,wDAAwD,GACtD,qDACJ,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAC,eAAe,CAACP,KAAK,EAAEX,MAAM,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;AACF;AAEA,SAASkB,eAAeA,CAACP,KAAK,EAAEX,MAAM,EAAE;EACtCI,SAAS,CAACS,aAAa,CACpBd,QAAQ,CAACY,KAAK,CAAC,CACfI,IAAI,CAACI,YAAY,IAAI;IACpBA,YAAY,CAACC,aAAa,GAAG,MAAM;MACjC,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,UAAU;MAChD,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC5B;MACF;MACAA,gBAAgB,CAACE,aAAa,GAAG,MAAM;QACrC,IAAIF,gBAAgB,CAACG,KAAK,KAAK,WAAW,EAAE;UAC1C,IAAIpB,SAAS,CAACS,aAAa,CAACY,UAAU,EAAE;YACtC;YACA;YACA;YACAT,OAAO,CAACC,GAAG,CACT,qDAAqD,GACnD,4DACJ,CAAC;;YAED;YACA,IAAIjB,MAAM,IAAIA,MAAM,CAAC0B,QAAQ,EAAE;cAC7B1B,MAAM,CAAC0B,QAAQ,CAACP,YAAY,CAAC;YAC/B;UACF,CAAC,MAAM;YACL;YACA;YACA;YACAH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;YAEjD;YACA,IAAIjB,MAAM,IAAIA,MAAM,CAAC2B,SAAS,EAAE;cAC9B3B,MAAM,CAAC2B,SAAS,CAACR,YAAY,CAAC;YAChC;UACF;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC,CAAC,CACDS,KAAK,CAACC,KAAK,IAAI;IACdb,OAAO,CAACa,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;EACnE,CAAC,CAAC;AACN;AAEA,SAASjB,uBAAuBA,CAACD,KAAK,EAAEX,MAAM,EAAE;EAC9C;EACA8B,KAAK,CAACnB,KAAK,EAAE;IACXoB,OAAO,EAAE;MAAE,gBAAgB,EAAE;IAAS;EACxC,CAAC,CAAC,CACChB,IAAI,CAACiB,QAAQ,IAAI;IAChB;IACA,MAAMC,WAAW,GAAGD,QAAQ,CAACD,OAAO,CAACG,GAAG,CAAC,cAAc,CAAC;IACxD,IACEF,QAAQ,CAACG,MAAM,KAAK,GAAG,IACtBF,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAE,EACjE;MACA;MACAhC,SAAS,CAACS,aAAa,CAACC,KAAK,CAACC,IAAI,CAACI,YAAY,IAAI;QACjDA,YAAY,CAACkB,UAAU,CAAC,CAAC,CAACtB,IAAI,CAAC,MAAM;UACnCpB,MAAM,CAACC,QAAQ,CAAC0C,MAAM,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACApB,eAAe,CAACP,KAAK,EAAEX,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,CACD4B,KAAK,CAAC,MAAM;IACXZ,OAAO,CAACC,GAAG,CACT,+DACF,CAAC;EACH,CAAC,CAAC;AACN;AAEA,OAAO,SAASoB,UAAUA,CAAA,EAAG;EAC3B,IAAI,eAAe,IAAIjC,SAAS,EAAE;IAChCA,SAAS,CAACS,aAAa,CAACC,KAAK,CAC1BC,IAAI,CAACI,YAAY,IAAI;MACpBA,YAAY,CAACkB,UAAU,CAAC,CAAC;IAC3B,CAAC,CAAC,CACDT,KAAK,CAACC,KAAK,IAAI;MACdb,OAAO,CAACa,KAAK,CAACA,KAAK,CAACU,OAAO,CAAC;IAC9B,CAAC,CAAC;EACN;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}