{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useState, useContext } from \"react\";\nimport { loginReq } from \"./fetchApi\";\nimport { LayoutContext } from \"../index\";\nimport { useSnackbar } from 'notistack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = props => {\n  _s();\n  const {\n    data: layoutData,\n    dispatch: layoutDispatch\n  } = useContext(LayoutContext);\n  const [data, setData] = useState({\n    email: \"\",\n    password: \"\",\n    error: false,\n    loading: true\n  });\n  const alert = msg => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-xs text-red-500\",\n    children: msg\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 26\n  }, this);\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const formSubmit = async () => {\n    setData({\n      ...data,\n      loading: true\n    });\n    try {\n      let responseData = await loginReq({\n        email: data.email,\n        password: data.password\n      });\n      if (responseData.error) {\n        setData({\n          ...data,\n          loading: false,\n          error: responseData.error,\n          password: \"\"\n        });\n      } else if (responseData.token) {\n        setData({\n          email: \"\",\n          password: \"\",\n          loading: false,\n          error: false\n        });\n        localStorage.setItem(\"jwt\", JSON.stringify(responseData));\n        enqueueSnackbar('Login Completed Successfully..!', {\n          variant: 'success'\n        });\n        window.location.href = \"/\";\n      }\n    } catch (error) {\n      console.log(error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-2xl mb-6\",\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), layoutData.loginSignupError ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-200 py-2 px-4 rounded\",\n      children: \"You need to login for checkout. Haven't accont? Create new one.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this) : \"\", /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: [\"Username or email address\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 ml-1\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          onChange: e => {\n            setData({\n              ...data,\n              email: e.target.value,\n              error: false\n            });\n            layoutDispatch({\n              type: \"loginSignupError\",\n              payload: false\n            });\n          },\n          value: data.email,\n          type: \"text\",\n          id: \"name\",\n          className: `${!data.error ? \"\" : \"border-red-500\"} px-4 py-2 focus:outline-none border`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), !data.error ? \"\" : alert(data.error)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: [\"Password\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 ml-1\",\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          onChange: e => {\n            setData({\n              ...data,\n              password: e.target.value,\n              error: false\n            });\n            layoutDispatch({\n              type: \"loginSignupError\",\n              payload: false\n            });\n          },\n          value: data.password,\n          type: \"password\",\n          id: \"password\",\n          className: `${!data.error ? \"\" : \"border-red-500\"} px-4 py-2 focus:outline-none border`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), !data.error ? \"\" : alert(data.error)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col space-y-2 md:flex-row md:justify-between md:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"rememberMe\",\n            className: \"px-4 py-2 focus:outline-none border mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"rememberMe\",\n            children: [\"Remember me\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 26\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          className: \"block text-gray-600\",\n          href: \"/\",\n          children: \"Lost your password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: e => formSubmit(),\n        style: {\n          background: \"#303031\"\n        },\n        className: \"font-medium px-4 py-2 text-white text-center cursor-pointer\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"7fECIX3AW2+rFEdrRyJO9L9ZtcA=\", false, function () {\n  return [useSnackbar];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "Fragment", "useState", "useContext", "loginReq", "LayoutContext", "useSnackbar", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "props", "_s", "data", "layoutData", "dispatch", "layoutDispatch", "setData", "email", "password", "error", "loading", "alert", "msg", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "enqueueSnackbar", "formSubmit", "responseData", "token", "localStorage", "setItem", "JSON", "stringify", "variant", "window", "location", "href", "console", "log", "loginSignupError", "htmlFor", "onChange", "e", "target", "value", "type", "payload", "id", "onClick", "style", "background", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/auth/Login.js"], "sourcesContent": ["import React, { Fragment, useState, useContext } from \"react\";\r\nimport { loginReq } from \"./fetchApi\";\r\nimport { LayoutContext } from \"../index\";\r\nimport { useSnackbar } from 'notistack';\r\nconst Login = (props) => {\r\n  const { data: layoutData, dispatch: layoutDispatch } = useContext(\r\n    LayoutContext\r\n  );\r\n\r\n  const [data, setData] = useState({\r\n    email: \"\",\r\n    password: \"\",\r\n    error: false,\r\n    loading: true,\r\n  });\r\n\r\n  const alert = (msg) => <div className=\"text-xs text-red-500\">{msg}</div>;\r\n\r\n  const { enqueueSnackbar } = useSnackbar();\r\n\r\n  const formSubmit = async () => {\r\n    setData({ ...data, loading: true });\r\n    try {\r\n      let responseData = await loginReq({\r\n        email: data.email,\r\n        password: data.password,\r\n      });\r\n      if (responseData.error) {\r\n        setData({\r\n          ...data,\r\n          loading: false,\r\n          error: responseData.error,\r\n          password: \"\",\r\n        });\r\n      } else if (responseData.token) {\r\n        setData({ email: \"\", password: \"\", loading: false, error: false });\r\n        localStorage.setItem(\"jwt\", JSON.stringify(responseData));\r\n       enqueueSnackbar('Login Completed Successfully..!', { variant: 'success' })\r\n        window.location.href = \"/\";\r\n\r\n      }    \r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Fragment>\r\n      <div className=\"text-center text-2xl mb-6\">Login</div>\r\n      {layoutData.loginSignupError ? (\r\n        <div className=\"bg-red-200 py-2 px-4 rounded\">\r\n          You need to login for checkout. Haven't accont? Create new one.\r\n        </div>\r\n      ) : (\r\n        \"\"\r\n      )}\r\n      <form className=\"space-y-4\">\r\n        <div className=\"flex flex-col\">\r\n          <label htmlFor=\"name\">\r\n            Username or email address\r\n            <span className=\"text-sm text-gray-600 ml-1\">*</span>\r\n          </label>\r\n          <input\r\n            onChange={(e) => {\r\n              setData({ ...data, email: e.target.value, error: false });\r\n              layoutDispatch({ type: \"loginSignupError\", payload: false });\r\n            }}\r\n            value={data.email}\r\n            type=\"text\"\r\n            id=\"name\"\r\n            className={`${\r\n              !data.error ? \"\" : \"border-red-500\"\r\n            } px-4 py-2 focus:outline-none border`}\r\n          />\r\n          {!data.error ? \"\" : alert(data.error)}\r\n        </div>\r\n        <div className=\"flex flex-col\">\r\n          <label htmlFor=\"password\">\r\n            Password<span className=\"text-sm text-gray-600 ml-1\">*</span>\r\n          </label>\r\n          <input\r\n            onChange={(e) => {\r\n              setData({ ...data, password: e.target.value, error: false });\r\n              layoutDispatch({ type: \"loginSignupError\", payload: false });\r\n            }}\r\n            value={data.password}\r\n            type=\"password\"\r\n            id=\"password\"\r\n            className={`${\r\n              !data.error ? \"\" : \"border-red-500\"\r\n            } px-4 py-2 focus:outline-none border`}\r\n          />\r\n          {!data.error ? \"\" : alert(data.error)}\r\n        </div>\r\n        <div className=\"flex flex-col space-y-2 md:flex-row md:justify-between md:items-center\">\r\n          <div>\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"rememberMe\"\r\n              className=\"px-4 py-2 focus:outline-none border mr-1\"\r\n            />\r\n            <label htmlFor=\"rememberMe\">\r\n              Remember me<span className=\"text-sm text-gray-600\">*</span>\r\n            </label>\r\n          </div>\r\n          <a className=\"block text-gray-600\" href=\"/\">\r\n            Lost your password?\r\n          </a>\r\n        </div>\r\n        <div\r\n          onClick={(e) => formSubmit()}\r\n          style={{ background: \"#303031\" }}\r\n          className=\"font-medium px-4 py-2 text-white text-center cursor-pointer\"\r\n        >\r\n          Login\r\n        </div>\r\n      </form>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC7D,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,aAAa,QAAQ,UAAU;AACxC,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACxC,MAAMC,KAAK,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACvB,MAAM;IAAEC,IAAI,EAAEC,UAAU;IAAEC,QAAQ,EAAEC;EAAe,CAAC,GAAGZ,UAAU,CAC/DE,aACF,CAAC;EAED,MAAM,CAACO,IAAI,EAAEI,OAAO,CAAC,GAAGd,QAAQ,CAAC;IAC/Be,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAIC,GAAG,iBAAKd,OAAA;IAAKe,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAAEF;EAAG;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAExE,MAAM;IAAEC;EAAgB,CAAC,GAAGvB,WAAW,CAAC,CAAC;EAEzC,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bd,OAAO,CAAC;MAAE,GAAGJ,IAAI;MAAEQ,OAAO,EAAE;IAAK,CAAC,CAAC;IACnC,IAAI;MACF,IAAIW,YAAY,GAAG,MAAM3B,QAAQ,CAAC;QAChCa,KAAK,EAAEL,IAAI,CAACK,KAAK;QACjBC,QAAQ,EAAEN,IAAI,CAACM;MACjB,CAAC,CAAC;MACF,IAAIa,YAAY,CAACZ,KAAK,EAAE;QACtBH,OAAO,CAAC;UACN,GAAGJ,IAAI;UACPQ,OAAO,EAAE,KAAK;UACdD,KAAK,EAAEY,YAAY,CAACZ,KAAK;UACzBD,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIa,YAAY,CAACC,KAAK,EAAE;QAC7BhB,OAAO,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEE,OAAO,EAAE,KAAK;UAAED,KAAK,EAAE;QAAM,CAAC,CAAC;QAClEc,YAAY,CAACC,OAAO,CAAC,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACL,YAAY,CAAC,CAAC;QAC1DF,eAAe,CAAC,iCAAiC,EAAE;UAAEQ,OAAO,EAAE;QAAU,CAAC,CAAC;QACzEC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAE5B;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdsB,OAAO,CAACC,GAAG,CAACvB,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACEX,OAAA,CAACP,QAAQ;IAAAuB,QAAA,gBACPhB,OAAA;MAAKe,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EACrDf,UAAU,CAAC8B,gBAAgB,gBAC1BnC,OAAA;MAAKe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,EAAC;IAE9C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,GAEN,EACD,eACDpB,OAAA;MAAMe,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACzBhB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhB,OAAA;UAAOoC,OAAO,EAAC,MAAM;UAAApB,QAAA,GAAC,2BAEpB,eAAAhB,OAAA;YAAMe,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACRpB,OAAA;UACEqC,QAAQ,EAAGC,CAAC,IAAK;YACf9B,OAAO,CAAC;cAAE,GAAGJ,IAAI;cAAEK,KAAK,EAAE6B,CAAC,CAACC,MAAM,CAACC,KAAK;cAAE7B,KAAK,EAAE;YAAM,CAAC,CAAC;YACzDJ,cAAc,CAAC;cAAEkC,IAAI,EAAE,kBAAkB;cAAEC,OAAO,EAAE;YAAM,CAAC,CAAC;UAC9D,CAAE;UACFF,KAAK,EAAEpC,IAAI,CAACK,KAAM;UAClBgC,IAAI,EAAC,MAAM;UACXE,EAAE,EAAC,MAAM;UACT5B,SAAS,EAAE,GACT,CAACX,IAAI,CAACO,KAAK,GAAG,EAAE,GAAG,gBAAgB;QACE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EACD,CAAChB,IAAI,CAACO,KAAK,GAAG,EAAE,GAAGE,KAAK,CAACT,IAAI,CAACO,KAAK,CAAC;MAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNpB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhB,OAAA;UAAOoC,OAAO,EAAC,UAAU;UAAApB,QAAA,GAAC,UAChB,eAAAhB,OAAA;YAAMe,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACRpB,OAAA;UACEqC,QAAQ,EAAGC,CAAC,IAAK;YACf9B,OAAO,CAAC;cAAE,GAAGJ,IAAI;cAAEM,QAAQ,EAAE4B,CAAC,CAACC,MAAM,CAACC,KAAK;cAAE7B,KAAK,EAAE;YAAM,CAAC,CAAC;YAC5DJ,cAAc,CAAC;cAAEkC,IAAI,EAAE,kBAAkB;cAAEC,OAAO,EAAE;YAAM,CAAC,CAAC;UAC9D,CAAE;UACFF,KAAK,EAAEpC,IAAI,CAACM,QAAS;UACrB+B,IAAI,EAAC,UAAU;UACfE,EAAE,EAAC,UAAU;UACb5B,SAAS,EAAE,GACT,CAACX,IAAI,CAACO,KAAK,GAAG,EAAE,GAAG,gBAAgB;QACE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EACD,CAAChB,IAAI,CAACO,KAAK,GAAG,EAAE,GAAGE,KAAK,CAACT,IAAI,CAACO,KAAK,CAAC;MAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNpB,OAAA;QAAKe,SAAS,EAAC,wEAAwE;QAAAC,QAAA,gBACrFhB,OAAA;UAAAgB,QAAA,gBACEhB,OAAA;YACEyC,IAAI,EAAC,UAAU;YACfE,EAAE,EAAC,YAAY;YACf5B,SAAS,EAAC;UAA0C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACFpB,OAAA;YAAOoC,OAAO,EAAC,YAAY;YAAApB,QAAA,GAAC,aACf,eAAAhB,OAAA;cAAMe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpB,OAAA;UAAGe,SAAS,EAAC,qBAAqB;UAACiB,IAAI,EAAC,GAAG;UAAAhB,QAAA,EAAC;QAE5C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpB,OAAA;QACE4C,OAAO,EAAGN,CAAC,IAAKhB,UAAU,CAAC,CAAE;QAC7BuB,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAU,CAAE;QACjC/B,SAAS,EAAC,6DAA6D;QAAAC,QAAA,EACxE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEf,CAAC;AAACjB,EAAA,CAnHIF,KAAK;EAAA,QAcmBH,WAAW;AAAA;AAAAiD,EAAA,GAdnC9C,KAAK;AAqHX,eAAeA,KAAK;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}