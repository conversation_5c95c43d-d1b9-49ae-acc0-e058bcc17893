{"ast": null, "code": "export { default } from './getDisplayName';\nexport * from './getDisplayName';", "map": {"version": 3, "names": ["default"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/utils/esm/getDisplayName/index.js"], "sourcesContent": ["export { default } from './getDisplayName';\nexport * from './getDisplayName';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}