{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllOrder = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/order/get-all-orders`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const editCategory = async (oId, status) => {\n  let data = {\n    oId: oId,\n    status: status\n  };\n  console.log(data);\n  try {\n    let res = await axios.post(`${apiURL}/api/order/update-order`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteOrder = async oId => {\n  let data = {\n    oId: oId\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/order/delete-order`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllOrder", "res", "get", "data", "error", "console", "log", "editCategory", "oId", "status", "post", "deleteOrder"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllOrder = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/order/get-all-orders`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const editCategory = async (oId, status) => {\r\n  let data = { oId: oId, status: status };\r\n  console.log(data);\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/update-order`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteOrder = async (oId) => {\r\n  let data = { oId: oId };\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/delete-order`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,2BAA2B,CAAC;IAC/D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,YAAY,GAAG,MAAAA,CAAOC,GAAG,EAAEC,MAAM,KAAK;EACjD,IAAIN,IAAI,GAAG;IAAEK,GAAG,EAAEA,GAAG;IAAEC,MAAM,EAAEA;EAAO,CAAC;EACvCJ,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;EACjB,IAAI;IACF,IAAIF,GAAG,GAAG,MAAMN,KAAK,CAACe,IAAI,CAAC,GAAGd,MAAM,yBAAyB,EAAEO,IAAI,CAAC;IACpE,OAAOF,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMO,WAAW,GAAG,MAAOH,GAAG,IAAK;EACxC,IAAIL,IAAI,GAAG;IAAEK,GAAG,EAAEA;EAAI,CAAC;EACvB,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMN,KAAK,CAACe,IAAI,CAAC,GAAGd,MAAM,yBAAyB,EAAEO,IAAI,CAAC;IACpE,OAAOF,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}