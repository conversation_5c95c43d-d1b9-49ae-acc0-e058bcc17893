{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport App from \"./App\";\nimport * as serviceWorker from \"./serviceWorker\";\nimport { SnackbarProvider } from 'notistack';\nimport { BrowserRouter } from \"react-router-dom\";\nimport ErrorBoundary from \"./components/ErrorBoundary\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(SnackbarProvider, {\n      maxSnack: 3,\n      children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this), document.getElementById(\"root\"));\nserviceWorker.unregister();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "serviceWorker", "SnackbarProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "render", "StrictMode", "children", "maxSnack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById", "unregister"], "sources": ["D:/ITSS_Reference/client/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom\";\r\nimport App from \"./App\";\r\nimport * as serviceWorker from \"./serviceWorker\";\r\nimport { SnackbarProvider } from 'notistack';\r\nimport { <PERSON>rowserRouter } from \"react-router-dom\";\r\nimport ErrorBoundary from \"./components/ErrorBoundary\";\r\n\r\nReactDOM.render(\r\n  <React.StrictMode>\r\n    <BrowserRouter>\r\n      <SnackbarProvider maxSnack={3}>\r\n        <App />\r\n      </SnackbarProvider>\r\n    </BrowserRouter>\r\n  </React.StrictMode>,\r\n  document.getElementById(\"root\")\r\n);\r\n\r\nserviceWorker.unregister();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,KAAKC,aAAa,MAAM,iBAAiB;AAChD,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvDP,QAAQ,CAACQ,MAAM,cACbD,OAAA,CAACR,KAAK,CAACU,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACH,aAAa;IAAAM,QAAA,eACZH,OAAA,CAACJ,gBAAgB;MAACQ,QAAQ,EAAE,CAAE;MAAAD,QAAA,eAC5BH,OAAA,CAACN,GAAG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CAAC,EACnBC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDf,aAAa,CAACgB,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}