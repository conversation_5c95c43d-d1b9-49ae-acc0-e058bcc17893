<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-button {
            background: #ff4757;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: #ff3742;
            transform: translateY(-2px);
        }
        .success {
            background: #2ed573 !important;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
        }
        .emergency-script {
            background: #2f3542;
            color: #f1f2f6;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Test Page</h1>
        <p>Trang này hoàn toàn tách biệt khỏi React app để test tương tác cơ bản.</p>
        
        <div class="result" id="result">
            <strong>Trạng thái:</strong> Chưa test
        </div>
        
        <h2>🧪 Basic Tests</h2>
        <button class="test-button" onclick="testClick()">Test Click</button>
        <button class="test-button" onclick="testAlert()">Test Alert</button>
        <button class="test-button" onclick="testConsole()">Test Console</button>
        <button class="test-button" onclick="goToReactApp()">Quay lại React App</button>
        
        <h2>🔧 Emergency Fix Script</h2>
        <p>Nếu React app không hoạt động, copy script dưới đây và paste vào Console (F12):</p>
        
        <div class="emergency-script" id="script">
// EMERGENCY FIX - Copy và paste vào Console
console.log('🚨 EMERGENCY FIX START');

// Ẩn tất cả overlay có vấn đề
document.querySelectorAll('*').forEach(el => {
  const style = getComputedStyle(el);
  const rect = el.getBoundingClientRect();
  
  if ((style.position === 'fixed' || style.position === 'absolute') &&
      rect.width > window.innerWidth * 0.7 &&
      rect.height > window.innerHeight * 0.7 &&
      style.zIndex > 10) {
    if (!el.textContent.includes('TEST CLICK')) {
      el.style.display = 'none';
      console.log('Ẩn overlay:', el.className);
    }
  }
});

// Enable pointer events
document.body.style.pointerEvents = 'auto';
document.documentElement.style.pointerEvents = 'auto';

// Tạo test button
const btn = document.createElement('button');
btn.innerHTML = '🧪 TEST CLICK';
btn.style.cssText = `
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 999999 !important;
  background: #ff0000 !important;
  color: white !important;
  padding: 20px !important;
  border: none !important;
  border-radius: 10px !important;
  font-size: 18px !important;
  cursor: pointer !important;
`;
btn.onclick = () => alert('✅ CLICK HOẠT ĐỘNG!');
document.body.appendChild(btn);

console.log('✅ Emergency fix hoàn thành!');
        </div>
        
        <button class="test-button" onclick="copyScript()">📋 Copy Script</button>
        
        <h2>📋 Hướng dẫn Debug</h2>
        <ol>
            <li><strong>Test trang này trước:</strong> Click các nút ở trên để đảm bảo browser hoạt động bình thường</li>
            <li><strong>Nếu trang này hoạt động:</strong> Vấn đề ở React app</li>
            <li><strong>Quay lại React app:</strong> Click "Quay lại React App"</li>
            <li><strong>Mở Console:</strong> Nhấn F12 → Console tab</li>
            <li><strong>Chạy Emergency Fix:</strong> Copy script ở trên và paste vào Console</li>
            <li><strong>Tìm nút TEST CLICK:</strong> Sau khi chạy script, tìm nút đỏ ở trên cùng</li>
            <li><strong>Test click:</strong> Click nút đó để xem có hoạt động không</li>
        </ol>
        
        <h2>🔗 Links</h2>
        <p>
            <a href="/" style="color: #ffeaa7;">🏠 React App Home</a> |
            <a href="/simple-test" style="color: #ffeaa7;">🧪 Simple Test</a> |
            <a href="/test-products" style="color: #ffeaa7;">📦 Test Products</a>
        </p>
    </div>

    <script>
        let clickCount = 0;
        
        function testClick() {
            clickCount++;
            document.getElementById('result').innerHTML = 
                `<strong>✅ Click Test:</strong> Hoạt động! (${clickCount} clicks)`;
            document.getElementById('result').style.background = 'rgba(46, 213, 115, 0.3)';
        }
        
        function testAlert() {
            alert('✅ Alert hoạt động! Browser JavaScript OK.');
            document.getElementById('result').innerHTML = 
                '<strong>✅ Alert Test:</strong> Hoạt động!';
        }
        
        function testConsole() {
            console.log('✅ Console test hoạt động!');
            console.warn('⚠️ Warning test');
            console.error('❌ Error test (không phải lỗi thật)');
            document.getElementById('result').innerHTML = 
                '<strong>✅ Console Test:</strong> Hoạt động! Kiểm tra Console (F12)';
        }
        
        function goToReactApp() {
            window.location.href = '/';
        }
        
        function copyScript() {
            const script = document.getElementById('script').textContent;
            navigator.clipboard.writeText(script).then(() => {
                alert('✅ Script đã được copy! Paste vào Console (F12) của React app.');
            }).catch(() => {
                alert('❌ Không thể copy tự động. Hãy select và copy thủ công.');
            });
        }
        
        // Auto test khi load trang
        window.onload = function() {
            console.log('🧪 Emergency test page loaded');
            document.getElementById('result').innerHTML = 
                '<strong>✅ Page Load:</strong> Trang test hoạt động bình thường!';
        };
    </script>
</body>
</html>
