import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

// Get user's cart
export const getCart = async (userId) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Add product to cart
export const addToCart = async (userId, productId, quantity = 1) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/add?productId=${productId}&quantity=${quantity}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Update cart item quantity
export const updateCartItem = async (userId, productId, quantity) => {
  try {
    let res = await axios.put(`${apiURL}/api/v1/cart/${userId}/update?productId=${productId}&quantity=${quantity}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Remove item from cart
export const removeFromCart = async (userId, productId) => {
  try {
    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/remove?productId=${productId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Empty entire cart
export const emptyCart = async (userId) => {
  try {
    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/empty`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Check cart availability
export const checkCartAvailability = async (userId) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/check-availability`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
