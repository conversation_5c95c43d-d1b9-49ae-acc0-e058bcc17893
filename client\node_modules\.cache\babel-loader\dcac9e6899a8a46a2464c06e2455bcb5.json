{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn(data, headers);\n  });\n  return data;\n};", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "transformData", "data", "headers", "fns", "for<PERSON>ach", "transform", "fn"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/transformData.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn(data, headers);\n  });\n\n  return data;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC1D;EACAP,KAAK,CAACQ,OAAO,CAACD,GAAG,EAAE,SAASE,SAASA,CAACC,EAAE,EAAE;IACxCL,IAAI,GAAGK,EAAE,CAACL,IAAI,EAAEC,OAAO,CAAC;EAC1B,CAAC,CAAC;EAEF,OAAOD,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}