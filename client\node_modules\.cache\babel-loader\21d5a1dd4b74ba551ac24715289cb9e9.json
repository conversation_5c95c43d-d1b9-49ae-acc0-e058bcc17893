{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  // Hook up interceptors middleware\n  var chain = [dispatchRequest, undefined];\n  var promise = Promise.resolve(config);\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    chain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    chain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n  while (chain.length) {\n    promise = promise.then(chain.shift(), chain.shift());\n  }\n  return promise;\n};\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function (url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url\n    }));\n  };\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function (url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\nmodule.exports = Axios;", "map": {"version": 3, "names": ["utils", "require", "buildURL", "InterceptorManager", "dispatchRequest", "mergeConfig", "A<PERSON>os", "instanceConfig", "defaults", "interceptors", "request", "response", "prototype", "config", "arguments", "url", "method", "toLowerCase", "chain", "undefined", "promise", "Promise", "resolve", "for<PERSON>ach", "unshiftRequestInterceptors", "interceptor", "unshift", "fulfilled", "rejected", "pushResponseInterceptors", "push", "length", "then", "shift", "get<PERSON><PERSON>", "params", "paramsSerializer", "replace", "forEachMethodNoData", "forEachMethodWithData", "data", "module", "exports"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/Axios.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  // Hook up interceptors middleware\n  var chain = [dispatchRequest, undefined];\n  var promise = Promise.resolve(config);\n\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    chain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    chain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  while (chain.length) {\n    promise = promise.then(chain.shift(), chain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\n\nmodule.exports = Axios;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAIE,kBAAkB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AACxD,IAAIG,eAAe,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAClD,IAAII,WAAW,GAAGJ,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA,SAASK,KAAKA,CAACC,cAAc,EAAE;EAC7B,IAAI,CAACC,QAAQ,GAAGD,cAAc;EAC9B,IAAI,CAACE,YAAY,GAAG;IAClBC,OAAO,EAAE,IAAIP,kBAAkB,CAAC,CAAC;IACjCQ,QAAQ,EAAE,IAAIR,kBAAkB,CAAC;EACnC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACAG,KAAK,CAACM,SAAS,CAACF,OAAO,GAAG,SAASA,OAAOA,CAACG,MAAM,EAAE;EACjD;EACA;EACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3BD,MAAM,CAACE,GAAG,GAAGD,SAAS,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACvB;EAEAA,MAAM,GAAGR,WAAW,CAAC,IAAI,CAACG,QAAQ,EAAEK,MAAM,CAAC;;EAE3C;EACA,IAAIA,MAAM,CAACG,MAAM,EAAE;IACjBH,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACG,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACQ,MAAM,EAAE;IAC/BH,MAAM,CAACG,MAAM,GAAG,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAACC,WAAW,CAAC,CAAC;EACpD,CAAC,MAAM;IACLJ,MAAM,CAACG,MAAM,GAAG,KAAK;EACvB;;EAEA;EACA,IAAIE,KAAK,GAAG,CAACd,eAAe,EAAEe,SAAS,CAAC;EACxC,IAAIC,OAAO,GAAGC,OAAO,CAACC,OAAO,CAACT,MAAM,CAAC;EAErC,IAAI,CAACJ,YAAY,CAACC,OAAO,CAACa,OAAO,CAAC,SAASC,0BAA0BA,CAACC,WAAW,EAAE;IACjFP,KAAK,CAACQ,OAAO,CAACD,WAAW,CAACE,SAAS,EAAEF,WAAW,CAACG,QAAQ,CAAC;EAC5D,CAAC,CAAC;EAEF,IAAI,CAACnB,YAAY,CAACE,QAAQ,CAACY,OAAO,CAAC,SAASM,wBAAwBA,CAACJ,WAAW,EAAE;IAChFP,KAAK,CAACY,IAAI,CAACL,WAAW,CAACE,SAAS,EAAEF,WAAW,CAACG,QAAQ,CAAC;EACzD,CAAC,CAAC;EAEF,OAAOV,KAAK,CAACa,MAAM,EAAE;IACnBX,OAAO,GAAGA,OAAO,CAACY,IAAI,CAACd,KAAK,CAACe,KAAK,CAAC,CAAC,EAAEf,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC;EACtD;EAEA,OAAOb,OAAO;AAChB,CAAC;AAEDd,KAAK,CAACM,SAAS,CAACsB,MAAM,GAAG,SAASA,MAAMA,CAACrB,MAAM,EAAE;EAC/CA,MAAM,GAAGR,WAAW,CAAC,IAAI,CAACG,QAAQ,EAAEK,MAAM,CAAC;EAC3C,OAAOX,QAAQ,CAACW,MAAM,CAACE,GAAG,EAAEF,MAAM,CAACsB,MAAM,EAAEtB,MAAM,CAACuB,gBAAgB,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACxF,CAAC;;AAED;AACArC,KAAK,CAACuB,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,SAASe,mBAAmBA,CAACtB,MAAM,EAAE;EACvF;EACAV,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG,UAASD,GAAG,EAAEF,MAAM,EAAE;IAC9C,OAAO,IAAI,CAACH,OAAO,CAACL,WAAW,CAACQ,MAAM,IAAI,CAAC,CAAC,EAAE;MAC5CG,MAAM,EAAEA,MAAM;MACdD,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AAEFf,KAAK,CAACuB,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASgB,qBAAqBA,CAACvB,MAAM,EAAE;EAC7E;EACAV,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG,UAASD,GAAG,EAAEyB,IAAI,EAAE3B,MAAM,EAAE;IACpD,OAAO,IAAI,CAACH,OAAO,CAACL,WAAW,CAACQ,MAAM,IAAI,CAAC,CAAC,EAAE;MAC5CG,MAAM,EAAEA,MAAM;MACdD,GAAG,EAAEA,GAAG;MACRyB,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGpC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}