{"ast": null, "code": "import { DashboardData, postUploadImage, getSliderImages, postDeleteImage } from \"./FetchApi\";\nimport { getAllOrder } from \"../orders/FetchApi.js\";\nexport const GetAllData = async dispatch => {\n  let responseData = await DashboardData();\n  if (responseData) {\n    dispatch({\n      type: \"totalData\",\n      payload: responseData\n    });\n  }\n};\n_c = GetAllData;\nexport const todayAllOrders = async dispatch => {\n  let responseData = await getAllOrder();\n  if (responseData) {\n    dispatch({\n      type: \"totalOrders\",\n      payload: responseData\n    });\n  }\n};\nexport const sliderImages = async dispatch => {\n  try {\n    let responseData = await getSliderImages();\n    if (responseData && responseData.Images) {\n      dispatch({\n        type: \"sliderImages\",\n        payload: responseData.Images\n      });\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteImage = async (id, dispatch) => {\n  dispatch({\n    type: \"imageUpload\",\n    payload: true\n  });\n  try {\n    let responseData = await postDeleteImage(id);\n    if (responseData && responseData.success) {\n      setTimeout(function () {\n        sliderImages(dispatch);\n        dispatch({\n          type: \"imageUpload\",\n          payload: false\n        });\n      }, 1000);\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const uploadImage = async (image, dispatch) => {\n  dispatch({\n    type: \"imageUpload\",\n    payload: true\n  });\n  let formData = new FormData();\n  formData.append(\"image\", image);\n  console.log(formData.get(\"image\"));\n  try {\n    let responseData = await postUploadImage(formData);\n    if (responseData && responseData.success) {\n      setTimeout(function () {\n        dispatch({\n          type: \"imageUpload\",\n          payload: false\n        });\n        sliderImages(dispatch);\n      }, 1000);\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nvar _c;\n$RefreshReg$(_c, \"GetAllData\");", "map": {"version": 3, "names": ["DashboardData", "postUploadImage", "getSliderImages", "postDeleteImage", "getAllOrder", "GetAllData", "dispatch", "responseData", "type", "payload", "_c", "todayAllOrders", "sliderImages", "Images", "error", "console", "log", "deleteImage", "id", "success", "setTimeout", "uploadImage", "image", "formData", "FormData", "append", "get", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/dashboardAdmin/Action.js"], "sourcesContent": ["import {\r\n  DashboardData,\r\n  postUploadImage,\r\n  getSliderImages,\r\n  postDeleteImage,\r\n} from \"./FetchApi\";\r\nimport { getAllOrder } from \"../orders/FetchApi.js\";\r\n\r\nexport const GetAllData = async (dispatch) => {\r\n  let responseData = await DashboardData();\r\n  if (responseData) {\r\n    dispatch({ type: \"totalData\", payload: responseData });\r\n  }\r\n};\r\n\r\nexport const todayAllOrders = async (dispatch) => {\r\n  let responseData = await getAllOrder();\r\n  if (responseData) {\r\n    dispatch({ type: \"totalOrders\", payload: responseData });\r\n  }\r\n};\r\n\r\nexport const sliderImages = async (dispatch) => {\r\n  try {\r\n    let responseData = await getSliderImages();\r\n    if (responseData && responseData.Images) {\r\n      dispatch({ type: \"sliderImages\", payload: responseData.Images });\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteImage = async (id, dispatch) => {\r\n  dispatch({ type: \"imageUpload\", payload: true });\r\n  try {\r\n    let responseData = await postDeleteImage(id);\r\n    if (responseData && responseData.success) {\r\n      setTimeout(function () {\r\n        sliderImages(dispatch);\r\n        dispatch({ type: \"imageUpload\", payload: false });\r\n      }, 1000);\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const uploadImage = async (image, dispatch) => {\r\n  dispatch({ type: \"imageUpload\", payload: true });\r\n  let formData = new FormData();\r\n  formData.append(\"image\", image);\r\n  console.log(formData.get(\"image\"));\r\n  try {\r\n    let responseData = await postUploadImage(formData);\r\n    if (responseData && responseData.success) {\r\n      setTimeout(function () {\r\n        dispatch({ type: \"imageUpload\", payload: false });\r\n        sliderImages(dispatch);\r\n      }, 1000);\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,SACEA,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,eAAe,QACV,YAAY;AACnB,SAASC,WAAW,QAAQ,uBAAuB;AAEnD,OAAO,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5C,IAAIC,YAAY,GAAG,MAAMP,aAAa,CAAC,CAAC;EACxC,IAAIO,YAAY,EAAE;IAChBD,QAAQ,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAEF;IAAa,CAAC,CAAC;EACxD;AACF,CAAC;AAACG,EAAA,GALWL,UAAU;AAOvB,OAAO,MAAMM,cAAc,GAAG,MAAOL,QAAQ,IAAK;EAChD,IAAIC,YAAY,GAAG,MAAMH,WAAW,CAAC,CAAC;EACtC,IAAIG,YAAY,EAAE;IAChBD,QAAQ,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEF;IAAa,CAAC,CAAC;EAC1D;AACF,CAAC;AAED,OAAO,MAAMK,YAAY,GAAG,MAAON,QAAQ,IAAK;EAC9C,IAAI;IACF,IAAIC,YAAY,GAAG,MAAML,eAAe,CAAC,CAAC;IAC1C,IAAIK,YAAY,IAAIA,YAAY,CAACM,MAAM,EAAE;MACvCP,QAAQ,CAAC;QAAEE,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEF,YAAY,CAACM;MAAO,CAAC,CAAC;IAClE;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,WAAW,GAAG,MAAAA,CAAOC,EAAE,EAAEZ,QAAQ,KAAK;EACjDA,QAAQ,CAAC;IAAEE,IAAI,EAAE,aAAa;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAChD,IAAI;IACF,IAAIF,YAAY,GAAG,MAAMJ,eAAe,CAACe,EAAE,CAAC;IAC5C,IAAIX,YAAY,IAAIA,YAAY,CAACY,OAAO,EAAE;MACxCC,UAAU,CAAC,YAAY;QACrBR,YAAY,CAACN,QAAQ,CAAC;QACtBA,QAAQ,CAAC;UAAEE,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MACnD,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMO,WAAW,GAAG,MAAAA,CAAOC,KAAK,EAAEhB,QAAQ,KAAK;EACpDA,QAAQ,CAAC;IAAEE,IAAI,EAAE,aAAa;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAChD,IAAIc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAAC;EAC/BP,OAAO,CAACC,GAAG,CAACO,QAAQ,CAACG,GAAG,CAAC,OAAO,CAAC,CAAC;EAClC,IAAI;IACF,IAAInB,YAAY,GAAG,MAAMN,eAAe,CAACsB,QAAQ,CAAC;IAClD,IAAIhB,YAAY,IAAIA,YAAY,CAACY,OAAO,EAAE;MACxCC,UAAU,CAAC,YAAY;QACrBd,QAAQ,CAAC;UAAEE,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACjDG,YAAY,CAACN,QAAQ,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAAC,IAAAJ,EAAA;AAAAiB,YAAA,CAAAjB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}