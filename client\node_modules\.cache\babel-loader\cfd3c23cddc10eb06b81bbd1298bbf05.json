{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\OrderMenu.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useState, useContext } from \"react\";\nimport { OrderContext } from \"./index\";\nimport UpdateOrderModal from \"./UpdateOrderModal\";\nimport SearchFilter from \"./SearchFilter\";\nimport { filterOrder } from \"./Actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderMenu = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(OrderContext);\n  const [dropdown, setDropdown] = useState(false);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0 w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: \"#303031\"\n          },\n          className: \"relative rounded-full text-gray-100 text-sm font-semibold uppercase\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: e => setDropdown(!dropdown),\n            className: \"flex items-center cursor-pointer rounded-full overflow-hidden p-2 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-gray-100 mr-2\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pr-2\",\n              children: \"Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: \"#303031\"\n            },\n            className: `${dropdown ? \"\" : \"hidden\"} absolute top-0 left-0 mt-12 rounded-lg overflow-hidden w-full md:w-48 flex flex-col z-10`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"All\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"Not processed\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"Not processed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"Processing\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"Shipped\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"Shipped\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"Delivered\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"Delivered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: e => filterOrder(\"Cancelled\", data, dispatch, dropdown, setDropdown),\n              className: \"px-4 py-2 hover:bg-black text-center cursor-pointer\",\n              children: \"Cancelled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(SearchFilter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UpdateOrderModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderMenu, \"UhV2kDGGlUOsQ6mNYvyEkcdtSDs=\");\n_c = OrderMenu;\nexport default OrderMenu;\nvar _c;\n$RefreshReg$(_c, \"OrderMenu\");", "map": {"version": 3, "names": ["React", "Fragment", "useState", "useContext", "OrderContext", "UpdateOrderModal", "SearchFilter", "filterOrder", "jsxDEV", "_jsxDEV", "OrderMenu", "props", "_s", "data", "dispatch", "dropdown", "setDropdown", "children", "className", "style", "background", "onClick", "e", "fill", "viewBox", "xmlns", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/OrderMenu.js"], "sourcesContent": ["import React, { Fragment, useState, useContext } from \"react\";\r\nimport { OrderContext } from \"./index\";\r\nimport UpdateOrderModal from \"./UpdateOrderModal\";\r\nimport SearchFilter from \"./SearchFilter\";\r\nimport { filterOrder } from \"./Actions\";\r\n\r\nconst OrderMenu = (props) => {\r\n  const { data, dispatch } = useContext(OrderContext);\r\n  const [dropdown, setDropdown] = useState(false);\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 flex items-center\">\r\n        <div className=\"flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0 w-full\">\r\n          {/* It's open the add order modal */}\r\n          <div\r\n            style={{ background: \"#303031\" }}\r\n            className=\"relative rounded-full text-gray-100 text-sm font-semibold uppercase\"\r\n          >\r\n            <div\r\n              onClick={(e) => setDropdown(!dropdown)}\r\n              className=\"flex items-center cursor-pointer rounded-full overflow-hidden p-2 justify-center\"\r\n            >\r\n              <svg\r\n                className=\"w-6 h-6 text-gray-100 mr-2\"\r\n                fill=\"currentColor\"\r\n                viewBox=\"0 0 20 20\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <path d=\"M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z\" />\r\n              </svg>\r\n              <span className=\"pr-2\">Filter</span>\r\n            </div>\r\n            <div\r\n              style={{ background: \"#303031\" }}\r\n              className={`${\r\n                dropdown ? \"\" : \"hidden\"\r\n              } absolute top-0 left-0 mt-12 rounded-lg overflow-hidden w-full md:w-48 flex flex-col z-10`}\r\n            >\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\"All\", data, dispatch, dropdown, setDropdown)\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                All\r\n              </span>\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\r\n                    \"Not processed\",\r\n                    data,\r\n                    dispatch,\r\n                    dropdown,\r\n                    setDropdown\r\n                  )\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                Not processed\r\n              </span>\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\r\n                    \"Processing\",\r\n                    data,\r\n                    dispatch,\r\n                    dropdown,\r\n                    setDropdown\r\n                  )\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                Processing\r\n              </span>\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\"Shipped\", data, dispatch, dropdown, setDropdown)\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                Shipped\r\n              </span>\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\r\n                    \"Delivered\",\r\n                    data,\r\n                    dispatch,\r\n                    dropdown,\r\n                    setDropdown\r\n                  )\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                Delivered\r\n              </span>\r\n              <span\r\n                onClick={(e) =>\r\n                  filterOrder(\r\n                    \"Cancelled\",\r\n                    data,\r\n                    dispatch,\r\n                    dropdown,\r\n                    setDropdown\r\n                  )\r\n                }\r\n                className=\"px-4 py-2 hover:bg-black text-center cursor-pointer\"\r\n              >\r\n                Cancelled\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <SearchFilter />\r\n          </div>\r\n        </div>\r\n        {/*<AddCategoryModal/>*/}\r\n        <UpdateOrderModal />\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default OrderMenu;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC7D,SAASC,YAAY,QAAQ,SAAS;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGX,UAAU,CAACC,YAAY,CAAC;EACnD,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC/C,oBACEO,OAAA,CAACR,QAAQ;IAAAgB,QAAA,eACPR,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAD,QAAA,gBAC3CR,OAAA;QAAKS,SAAS,EAAC,4FAA4F;QAAAD,QAAA,gBAEzGR,OAAA;UACEU,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAU,CAAE;UACjCF,SAAS,EAAC,qEAAqE;UAAAD,QAAA,gBAE/ER,OAAA;YACEY,OAAO,EAAGC,CAAC,IAAKN,WAAW,CAAC,CAACD,QAAQ,CAAE;YACvCG,SAAS,EAAC,kFAAkF;YAAAD,QAAA,gBAE5FR,OAAA;cACES,SAAS,EAAC,4BAA4B;cACtCK,IAAI,EAAC,cAAc;cACnBC,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,4BAA4B;cAAAR,QAAA,eAElCR,OAAA;gBAAMiB,CAAC,EAAC;cAA+P;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvQ,CAAC,eACNrB,OAAA;cAAMS,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNrB,OAAA;YACEU,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCF,SAAS,EAAE,GACTH,QAAQ,GAAG,EAAE,GAAG,QAAQ,2FACkE;YAAAE,QAAA,gBAE5FR,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CAAC,KAAK,EAAEM,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,CACzD;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrB,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CACT,eAAe,EACfM,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,WACF,CACD;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrB,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CACT,YAAY,EACZM,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,WACF,CACD;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrB,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CAAC,SAAS,EAAEM,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,CAC7D;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrB,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CACT,WAAW,EACXM,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,WACF,CACD;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrB,OAAA;cACEY,OAAO,EAAGC,CAAC,IACTf,WAAW,CACT,WAAW,EACXM,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,WACF,CACD;cACDE,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAChE;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrB,OAAA;UAAAQ,QAAA,eACER,OAAA,CAACH,YAAY;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrB,OAAA,CAACJ,gBAAgB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAClB,EAAA,CAnHIF,SAAS;AAAAqB,EAAA,GAATrB,SAAS;AAqHf,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}