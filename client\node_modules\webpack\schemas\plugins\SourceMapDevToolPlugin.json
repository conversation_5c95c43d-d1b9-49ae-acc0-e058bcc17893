{"definitions": {"rule": {"oneOf": [{"instanceof": "RegExp", "tsType": "RegExp"}, {"type": "string", "minLength": 1}]}, "rules": {"oneOf": [{"type": "array", "items": {"description": "A rule condition", "anyOf": [{"$ref": "#/definitions/rule"}]}}, {"$ref": "#/definitions/rule"}]}}, "title": "SourceMapDevToolPluginOptions", "type": "object", "additionalProperties": false, "properties": {"append": {"description": "Appends the given value to the original asset. Usually the #sourceMappingURL comment. [url] is replaced with a URL to the source map file. false disables the appending", "oneOf": [{"description": "Append no SourceMap comment to the bundle, but still generate SourceMaps", "enum": [false, null]}, {"type": "string", "minLength": 1}]}, "columns": {"description": "Indicates whether column mappings should be used (defaults to true)", "type": "boolean"}, "exclude": {"description": "Exclude modules that match the given value from source map generation", "anyOf": [{"$ref": "#/definitions/rules"}]}, "fallbackModuleFilenameTemplate": {"description": "Generator string or function to create identifiers of modules for the 'sources' array in the SourceMap used only if 'moduleFilenameTemplate' would result in a conflict", "oneOf": [{"description": "Custom function generating the identifer", "instanceof": "Function", "tsType": "Function"}, {"type": "string", "minLength": 1}]}, "fileContext": {"description": "Path prefix to which the [file] placeholder is relative to", "type": "string"}, "filename": {"description": "Defines the output filename of the SourceMap (will be inlined if no value is provided)", "oneOf": [{"description": "Disable separate SourceMap file and inline SourceMap as DataUrl", "enum": [false, null]}, {"type": "string", "absolutePath": false, "minLength": 1}]}, "include": {"description": "Include source maps for module paths that match the given value", "anyOf": [{"$ref": "#/definitions/rules"}]}, "lineToLine": {"description": "(deprecated) try to map original files line to line to generated files", "anyOf": [{"type": "boolean"}, {"description": "Simplify and speed up source mapping by using line to line source mappings for matched modules", "type": "object", "additionalProperties": false, "properties": {"exclude": {"description": "Exclude modules that match the given value from source map generation", "anyOf": [{"$ref": "#/definitions/rules"}]}, "include": {"description": "Include source maps for module paths that match the given value", "anyOf": [{"$ref": "#/definitions/rules"}]}, "test": {"description": "Include source maps for modules based on their extension (defaults to .js and .css)", "anyOf": [{"$ref": "#/definitions/rules"}]}}}]}, "module": {"description": "Indicates whether SourceMaps from loaders should be used (defaults to true)", "type": "boolean"}, "moduleFilenameTemplate": {"description": "Generator string or function to create identifiers of modules for the 'sources' array in the SourceMap", "oneOf": [{"description": "Custom function generating the identifer", "instanceof": "Function", "tsType": "Function"}, {"type": "string", "minLength": 1}]}, "namespace": {"description": "Namespace prefix to allow multiple webpack roots in the devtools", "type": "string"}, "noSources": {"description": "Omit the 'sourceContents' array from the SourceMap", "type": "boolean"}, "publicPath": {"description": "Provide a custom public path for the SourceMapping comment", "type": "string"}, "sourceRoot": {"description": "Provide a custom value for the 'sourceRoot' property in the SourceMap", "type": "string"}, "test": {"description": "Include source maps for modules based on their extension (defaults to .js and .css)", "anyOf": [{"$ref": "#/definitions/rules"}]}}}