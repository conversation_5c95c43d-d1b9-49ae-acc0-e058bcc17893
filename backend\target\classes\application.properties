spring.application.name=code
# Logging configuration
logging.level.root=INFO
logging.level.com.darian.ecommerce=DEBUG
logging.level.org.springframework.security=DEBUG
logging.file.name=logs/app.log
#logging.file.max-size=10MB
#logging.file.max-history=7
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n


# Server port
server.port=8080


# Th�ng tin k?t n?i PostgreSQL
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=0000

# C?u h�nh JPA/Hibernate
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

## Generate DDL scripts
#spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
#spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
#spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=create.sql

# JWT Configuration
app.jwt.secret=9a4f2c8d3b7a1e6f45c8a0b3f267d8b1d4e6f3c8a9d2b5f8e3a9c6b5d0e7f4a
app.jwt.expiration=86400000

# VNPay Configuration
vnpay.version=2.1.0
vnpay.tmnCode=YOUR_TMN_CODE
vnpay.hashSecret=YOUR_HASH_SECRET
vnpay.payUrl=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
vnpay.returnUrl=http://localhost:8080/api/payment/vnpay-return
vnpay.refundUrl=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction