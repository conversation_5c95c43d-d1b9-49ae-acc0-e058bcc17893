{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\home\\\\ProductCategoryDropdown.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { Fragment, useContext, useState, useEffect } from \"react\";\nimport { useHistory } from \"react-router-dom\";\nimport { HomeContext } from \"./index\";\nimport { getAllCategory } from \"../../admin/categories/FetchApi\";\nimport { getAllProduct, productByPrice } from \"../../admin/products/FetchApi\";\nimport \"./style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst CategoryList = () => {\n  _s();\n  const history = useHistory();\n  const {\n    data\n  } = useContext(HomeContext);\n  const [categories, setCategories] = useState(null);\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      // Tạm thời sử dụng mock data cho categories vì backend chưa có CategoryController\n      const mockCategories = [{\n        _id: 'electronics',\n        cName: 'Electronics',\n        cImage: 'electronics.jpg'\n      }, {\n        _id: 'fashion',\n        cName: 'Fashion',\n        cImage: 'fashion.jpg'\n      }, {\n        _id: 'beauty',\n        cName: 'Beauty',\n        cImage: 'beauty.jpg'\n      }, {\n        _id: 'furniture',\n        cName: 'Furniture',\n        cImage: 'furniture.jpg'\n      }, {\n        _id: 'beverages',\n        cName: 'Beverages',\n        cImage: 'beverages.jpg'\n      }, {\n        _id: 'food',\n        cName: 'Food',\n        cImage: 'food.jpg'\n      }, {\n        _id: 'household',\n        cName: 'Household',\n        cImage: 'household.jpg'\n      }, {\n        _id: 'toys',\n        cName: 'Toys',\n        cImage: 'toys.jpg'\n      }, {\n        _id: 'media',\n        cName: 'Media',\n        cImage: 'media.jpg'\n      }];\n      setCategories(mockCategories);\n    } catch (error) {\n      console.log(error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${data.categoryListDropdown ? \"\" : \"hidden\"} my-4`,\n    children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"py-1 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\",\n      children: categories && categories.length > 0 ? categories.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: e => history.push(`/products/category/${item._id}`),\n            className: \"col-span-1 m-2 flex flex-col items-center justify-center space-y-2 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: [item.cName === 'Electronics' && '📱', item.cName === 'Fashion' && '👕', item.cName === 'Beauty' && '💄', item.cName === 'Furniture' && '🪑', item.cName === 'Beverages' && '🥤', item.cName === 'Food' && '🍝', item.cName === 'Household' && '🧽', item.cName === 'Toys' && '🧸', item.cName === 'Media' && '📚']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium\",\n              children: item.cName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xl text-center my-4\",\n        children: \"No Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryList, \"uxUn5eeEmyv3jg+bICgvd2QGF9E=\", false, function () {\n  return [useHistory];\n});\n_c = CategoryList;\nconst FilterList = () => {\n  _s2();\n  const {\n    data,\n    dispatch\n  } = useContext(HomeContext);\n  const [range, setRange] = useState(0);\n  const rangeHandle = e => {\n    setRange(e.target.value);\n    fetchData(e.target.value);\n  };\n  const fetchData = async price => {\n    if (price === \"all\") {\n      try {\n        let responseData = await getAllProduct();\n        if (responseData && responseData.Products) {\n          dispatch({\n            type: \"setProducts\",\n            payload: responseData.Products\n          });\n        }\n      } catch (error) {\n        console.log(error);\n      }\n    } else {\n      dispatch({\n        type: \"loading\",\n        payload: true\n      });\n      try {\n        setTimeout(async () => {\n          let responseData = await productByPrice(price);\n          if (responseData && responseData.Products) {\n            console.log(responseData.Products);\n            dispatch({\n              type: \"setProducts\",\n              payload: responseData.Products\n            });\n            dispatch({\n              type: \"loading\",\n              payload: false\n            });\n          }\n        }, 700);\n      } catch (error) {\n        console.log(error);\n      }\n    }\n  };\n  const closeFilterBar = () => {\n    fetchData(\"all\");\n    dispatch({\n      type: \"filterListDropdown\",\n      payload: !data.filterListDropdown\n    });\n    setRange(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${data.filterListDropdown ? \"\" : \"hidden\"} my-4`,\n    children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium py-2\",\n        children: \"Filter by price\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-2  w-2/3 lg:w-2/4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"points\",\n            className: \"text-sm\",\n            children: [\"Price (between 0 and 10$):\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-yellow-700\",\n              children: [range, \".00$\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            value: range,\n            className: \"slider\",\n            type: \"range\",\n            id: \"points\",\n            min: \"0\",\n            max: \"1000\",\n            step: \"10\",\n            onChange: e => rangeHandle(e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: e => closeFilterBar(),\n          className: \"cursor-pointer\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s2(FilterList, \"6cFKPNOZjLqRwi7ilR3em8bWU3s=\");\n_c2 = FilterList;\nconst Search = () => {\n  _s3();\n  const {\n    data,\n    dispatch\n  } = useContext(HomeContext);\n  const [search, setSearch] = useState(\"\");\n  const [productArray, setPa] = useState(null);\n  const searchHandle = e => {\n    setSearch(e.target.value);\n    fetchData();\n    dispatch({\n      type: \"searchHandleInReducer\",\n      payload: e.target.value,\n      productArray: productArray\n    });\n  };\n  const fetchData = async () => {\n    dispatch({\n      type: \"loading\",\n      payload: true\n    });\n    try {\n      setTimeout(async () => {\n        let responseData = await getAllProduct();\n        if (responseData && responseData.Products) {\n          setPa(responseData.Products);\n          dispatch({\n            type: \"loading\",\n            payload: false\n          });\n        }\n      }, 700);\n    } catch (error) {\n      console.log(error);\n    }\n  };\n  const closeSearchBar = () => {\n    dispatch({\n      type: \"searchDropdown\",\n      payload: !data.searchDropdown\n    });\n    fetchData();\n    dispatch({\n      type: \"setProducts\",\n      payload: productArray\n    });\n    setSearch(\"\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${data.searchDropdown ? \"\" : \"hidden\"} my-4 flex items-center justify-between`,\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      value: search,\n      onChange: e => searchHandle(e),\n      className: \"px-4 text-xl py-4 focus:outline-none\",\n      type: \"text\",\n      placeholder: \"Search products...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: e => closeSearchBar(),\n      className: \"cursor-pointer\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M6 18L18 6M6 6l12 12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s3(Search, \"u45SEi94mN7AAayMgFGzawOa61s=\");\n_c3 = Search;\nconst ProductCategoryDropdown = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CategoryList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_c4 = ProductCategoryDropdown;\nexport default ProductCategoryDropdown;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"CategoryList\");\n$RefreshReg$(_c2, \"FilterList\");\n$RefreshReg$(_c3, \"Search\");\n$RefreshReg$(_c4, \"ProductCategoryDropdown\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useState", "useEffect", "useHistory", "HomeContext", "getAllCategory", "getAllProduct", "productByPrice", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "CategoryList", "_s", "history", "data", "categories", "setCategories", "fetchData", "mockCategories", "_id", "cName", "cImage", "error", "console", "log", "className", "categoryListDropdown", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "item", "index", "onClick", "e", "push", "_c", "FilterList", "_s2", "dispatch", "range", "setRang<PERSON>", "rangeHandle", "target", "value", "price", "responseData", "Products", "type", "payload", "setTimeout", "closeFilterBar", "filterListDropdown", "htmlFor", "id", "min", "max", "step", "onChange", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c2", "Search", "_s3", "search", "setSearch", "productArray", "setPa", "searchHandle", "closeSearchBar", "searchDropdown", "placeholder", "_c3", "ProductCategoryDropdown", "props", "_c4", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["import React, { Fragment, useContext, useState, useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { HomeContext } from \"./index\";\r\nimport { getAllCategory } from \"../../admin/categories/FetchApi\";\r\nimport { getAllProduct, productByPrice } from \"../../admin/products/FetchApi\";\r\nimport \"./style.css\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst CategoryList = () => {\r\n  const history = useHistory();\r\n  const { data } = useContext(HomeContext);\r\n  const [categories, setCategories] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      // Tạm thời sử dụng mock data cho categories vì backend chưa có CategoryController\r\n      const mockCategories = [\r\n        { _id: 'electronics', cName: 'Electronics', cImage: 'electronics.jpg' },\r\n        { _id: 'fashion', cName: 'Fashion', cImage: 'fashion.jpg' },\r\n        { _id: 'beauty', cName: 'Beauty', cImage: 'beauty.jpg' },\r\n        { _id: 'furniture', cName: 'Furniture', cImage: 'furniture.jpg' },\r\n        { _id: 'beverages', cName: 'Beverages', cImage: 'beverages.jpg' },\r\n        { _id: 'food', cName: 'Food', cImage: 'food.jpg' },\r\n        { _id: 'household', cName: 'Household', cImage: 'household.jpg' },\r\n        { _id: 'toys', cName: 'Toys', cImage: 'toys.jpg' },\r\n        { _id: 'media', cName: 'Media', cImage: 'media.jpg' }\r\n      ];\r\n      setCategories(mockCategories);\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`${data.categoryListDropdown ? \"\" : \"hidden\"} my-4`}>\r\n      <hr />\r\n      <div className=\"py-1 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\r\n        {categories && categories.length > 0 ? (\r\n          categories.map((item, index) => {\r\n            return (\r\n              <Fragment key={index}>\r\n                <div\r\n                  onClick={(e) =>\r\n                    history.push(`/products/category/${item._id}`)\r\n                  }\r\n                  className=\"col-span-1 m-2 flex flex-col items-center justify-center space-y-2 cursor-pointer\"\r\n                >\r\n                  <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-2xl\">\r\n                      {item.cName === 'Electronics' && '📱'}\r\n                      {item.cName === 'Fashion' && '👕'}\r\n                      {item.cName === 'Beauty' && '💄'}\r\n                      {item.cName === 'Furniture' && '🪑'}\r\n                      {item.cName === 'Beverages' && '🥤'}\r\n                      {item.cName === 'Food' && '🍝'}\r\n                      {item.cName === 'Household' && '🧽'}\r\n                      {item.cName === 'Toys' && '🧸'}\r\n                      {item.cName === 'Media' && '📚'}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"font-medium\">{item.cName}</div>\r\n                </div>\r\n              </Fragment>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className=\"text-xl text-center my-4\">No Category</div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst FilterList = () => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const [range, setRange] = useState(0);\r\n\r\n  const rangeHandle = (e) => {\r\n    setRange(e.target.value);\r\n    fetchData(e.target.value);\r\n  };\r\n\r\n  const fetchData = async (price) => {\r\n    if (price === \"all\") {\r\n      try {\r\n        let responseData = await getAllProduct();\r\n        if (responseData && responseData.Products) {\r\n          dispatch({ type: \"setProducts\", payload: responseData.Products });\r\n        }\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    } else {\r\n      dispatch({ type: \"loading\", payload: true });\r\n      try {\r\n        setTimeout(async () => {\r\n          let responseData = await productByPrice(price);\r\n          if (responseData && responseData.Products) {\r\n            console.log(responseData.Products);\r\n            dispatch({ type: \"setProducts\", payload: responseData.Products });\r\n            dispatch({ type: \"loading\", payload: false });\r\n          }\r\n        }, 700);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const closeFilterBar = () => {\r\n    fetchData(\"all\");\r\n    dispatch({ type: \"filterListDropdown\", payload: !data.filterListDropdown });\r\n    setRange(0);\r\n  };\r\n\r\n  return (\r\n    <div className={`${data.filterListDropdown ? \"\" : \"hidden\"} my-4`}>\r\n      <hr />\r\n      <div className=\"w-full flex flex-col\">\r\n        <div className=\"font-medium py-2\">Filter by price</div>\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"flex flex-col space-y-2  w-2/3 lg:w-2/4\">\r\n            <label htmlFor=\"points\" className=\"text-sm\">\r\n              Price (between 0 and 10$):{\" \"}\r\n              <span className=\"font-semibold text-yellow-700\">{range}.00$</span>{\" \"}\r\n            </label>\r\n            <input\r\n              value={range}\r\n              className=\"slider\"\r\n              type=\"range\"\r\n              id=\"points\"\r\n              min=\"0\"\r\n              max=\"1000\"\r\n              step=\"10\"\r\n              onChange={(e) => rangeHandle(e)}\r\n            />\r\n          </div>\r\n          <div onClick={(e) => closeFilterBar()} className=\"cursor-pointer\">\r\n            <svg\r\n              className=\"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M6 18L18 6M6 6l12 12\"\r\n              />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Search = () => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [productArray, setPa] = useState(null);\r\n\r\n  const searchHandle = (e) => {\r\n    setSearch(e.target.value);\r\n    fetchData();\r\n    dispatch({\r\n      type: \"searchHandleInReducer\",\r\n      payload: e.target.value,\r\n      productArray: productArray,\r\n    });\r\n  };\r\n\r\n  const fetchData = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      setTimeout(async () => {\r\n        let responseData = await getAllProduct();\r\n        if (responseData && responseData.Products) {\r\n          setPa(responseData.Products);\r\n          dispatch({ type: \"loading\", payload: false });\r\n        }\r\n      }, 700);\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  };\r\n\r\n  const closeSearchBar = () => {\r\n    dispatch({ type: \"searchDropdown\", payload: !data.searchDropdown });\r\n    fetchData();\r\n    dispatch({ type: \"setProducts\", payload: productArray });\r\n    setSearch(\"\");\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`${\r\n        data.searchDropdown ? \"\" : \"hidden\"\r\n      } my-4 flex items-center justify-between`}\r\n    >\r\n      <input\r\n        value={search}\r\n        onChange={(e) => searchHandle(e)}\r\n        className=\"px-4 text-xl py-4 focus:outline-none\"\r\n        type=\"text\"\r\n        placeholder=\"Search products...\"\r\n      />\r\n      <div onClick={(e) => closeSearchBar()} className=\"cursor-pointer\">\r\n        <svg\r\n          className=\"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth={2}\r\n            d=\"M6 18L18 6M6 6l12 12\"\r\n          />\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ProductCategoryDropdown = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <CategoryList />\r\n      <FilterList />\r\n      <Search />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductCategoryDropdown;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACxE,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,aAAa,EAAEC,cAAc,QAAQ,+BAA+B;AAC7E,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,OAAO,GAAGb,UAAU,CAAC,CAAC;EAC5B,MAAM;IAAEc;EAAK,CAAC,GAAGjB,UAAU,CAACI,WAAW,CAAC;EACxC,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdkB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF;MACA,MAAMC,cAAc,GAAG,CACrB;QAAEC,GAAG,EAAE,aAAa;QAAEC,KAAK,EAAE,aAAa;QAAEC,MAAM,EAAE;MAAkB,CAAC,EACvE;QAAEF,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAc,CAAC,EAC3D;QAAEF,GAAG,EAAE,QAAQ;QAAEC,KAAK,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAa,CAAC,EACxD;QAAEF,GAAG,EAAE,WAAW;QAAEC,KAAK,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAgB,CAAC,EACjE;QAAEF,GAAG,EAAE,WAAW;QAAEC,KAAK,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAgB,CAAC,EACjE;QAAEF,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAW,CAAC,EAClD;QAAEF,GAAG,EAAE,WAAW;QAAEC,KAAK,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAgB,CAAC,EACjE;QAAEF,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAW,CAAC,EAClD;QAAEF,GAAG,EAAE,OAAO;QAAEC,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAY,CAAC,CACtD;MACDL,aAAa,CAACE,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKmB,SAAS,EAAE,GAAGX,IAAI,CAACY,oBAAoB,GAAG,EAAE,GAAG,QAAQ,OAAQ;IAAAC,QAAA,gBAClErB,OAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNzB,OAAA;MAAKmB,SAAS,EAAC,qDAAqD;MAAAE,QAAA,EACjEZ,UAAU,IAAIA,UAAU,CAACiB,MAAM,GAAG,CAAC,GAClCjB,UAAU,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC9B,oBACE7B,OAAA,CAACV,QAAQ;UAAA+B,QAAA,eACPrB,OAAA;YACE8B,OAAO,EAAGC,CAAC,IACTxB,OAAO,CAACyB,IAAI,CAAC,sBAAsBJ,IAAI,CAACf,GAAG,EAAE,CAC9C;YACDM,SAAS,EAAC,mFAAmF;YAAAE,QAAA,gBAE7FrB,OAAA;cAAKmB,SAAS,EAAC,qEAAqE;cAAAE,QAAA,eAClFrB,OAAA;gBAAMmB,SAAS,EAAC,UAAU;gBAAAE,QAAA,GACvBO,IAAI,CAACd,KAAK,KAAK,aAAa,IAAI,IAAI,EACpCc,IAAI,CAACd,KAAK,KAAK,SAAS,IAAI,IAAI,EAChCc,IAAI,CAACd,KAAK,KAAK,QAAQ,IAAI,IAAI,EAC/Bc,IAAI,CAACd,KAAK,KAAK,WAAW,IAAI,IAAI,EAClCc,IAAI,CAACd,KAAK,KAAK,WAAW,IAAI,IAAI,EAClCc,IAAI,CAACd,KAAK,KAAK,MAAM,IAAI,IAAI,EAC7Bc,IAAI,CAACd,KAAK,KAAK,WAAW,IAAI,IAAI,EAClCc,IAAI,CAACd,KAAK,KAAK,MAAM,IAAI,IAAI,EAC7Bc,IAAI,CAACd,KAAK,KAAK,OAAO,IAAI,IAAI;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNzB,OAAA;cAAKmB,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAEO,IAAI,CAACd;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC,GArBOI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CAAC;MAEf,CAAC,CAAC,gBAEFzB,OAAA;QAAKmB,SAAS,EAAC,0BAA0B;QAAAE,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAC3D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAnEID,YAAY;EAAA,QACAX,UAAU;AAAA;AAAAuC,EAAA,GADtB5B,YAAY;AAqElB,MAAM6B,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAE3B,IAAI;IAAE4B;EAAS,CAAC,GAAG7C,UAAU,CAACI,WAAW,CAAC;EAClD,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAErC,MAAM+C,WAAW,GAAIR,CAAC,IAAK;IACzBO,QAAQ,CAACP,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;IACxB9B,SAAS,CAACoB,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAM9B,SAAS,GAAG,MAAO+B,KAAK,IAAK;IACjC,IAAIA,KAAK,KAAK,KAAK,EAAE;MACnB,IAAI;QACF,IAAIC,YAAY,GAAG,MAAM9C,aAAa,CAAC,CAAC;QACxC,IAAI8C,YAAY,IAAIA,YAAY,CAACC,QAAQ,EAAE;UACzCR,QAAQ,CAAC;YAAES,IAAI,EAAE,aAAa;YAAEC,OAAO,EAAEH,YAAY,CAACC;UAAS,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;MACpB;IACF,CAAC,MAAM;MACLoB,QAAQ,CAAC;QAAES,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5C,IAAI;QACFC,UAAU,CAAC,YAAY;UACrB,IAAIJ,YAAY,GAAG,MAAM7C,cAAc,CAAC4C,KAAK,CAAC;UAC9C,IAAIC,YAAY,IAAIA,YAAY,CAACC,QAAQ,EAAE;YACzC3B,OAAO,CAACC,GAAG,CAACyB,YAAY,CAACC,QAAQ,CAAC;YAClCR,QAAQ,CAAC;cAAES,IAAI,EAAE,aAAa;cAAEC,OAAO,EAAEH,YAAY,CAACC;YAAS,CAAC,CAAC;YACjER,QAAQ,CAAC;cAAES,IAAI,EAAE,SAAS;cAAEC,OAAO,EAAE;YAAM,CAAC,CAAC;UAC/C;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;MACpB;IACF;EACF,CAAC;EAED,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAC3BrC,SAAS,CAAC,KAAK,CAAC;IAChByB,QAAQ,CAAC;MAAES,IAAI,EAAE,oBAAoB;MAAEC,OAAO,EAAE,CAACtC,IAAI,CAACyC;IAAmB,CAAC,CAAC;IAC3EX,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC;EAED,oBACEtC,OAAA;IAAKmB,SAAS,EAAE,GAAGX,IAAI,CAACyC,kBAAkB,GAAG,EAAE,GAAG,QAAQ,OAAQ;IAAA5B,QAAA,gBAChErB,OAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNzB,OAAA;MAAKmB,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCrB,OAAA;QAAKmB,SAAS,EAAC,kBAAkB;QAAAE,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvDzB,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAE,QAAA,gBAChDrB,OAAA;UAAKmB,SAAS,EAAC,yCAAyC;UAAAE,QAAA,gBACtDrB,OAAA;YAAOkD,OAAO,EAAC,QAAQ;YAAC/B,SAAS,EAAC,SAAS;YAAAE,QAAA,GAAC,4BAChB,EAAC,GAAG,eAC9BrB,OAAA;cAAMmB,SAAS,EAAC,+BAA+B;cAAAE,QAAA,GAAEgB,KAAK,EAAC,MAAI;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACRzB,OAAA;YACEyC,KAAK,EAAEJ,KAAM;YACblB,SAAS,EAAC,QAAQ;YAClB0B,IAAI,EAAC,OAAO;YACZM,EAAE,EAAC,QAAQ;YACXC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,MAAM;YACVC,IAAI,EAAC,IAAI;YACTC,QAAQ,EAAGxB,CAAC,IAAKQ,WAAW,CAACR,CAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAK8B,OAAO,EAAGC,CAAC,IAAKiB,cAAc,CAAC,CAAE;UAAC7B,SAAS,EAAC,gBAAgB;UAAAE,QAAA,eAC/DrB,OAAA;YACEmB,SAAS,EAAC,0DAA0D;YACpEqC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAtC,QAAA,eAElCrB,OAAA;cACE4D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAsB;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACU,GAAA,CApFID,UAAU;AAAA8B,GAAA,GAAV9B,UAAU;AAsFhB,MAAM+B,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB,MAAM;IAAE1D,IAAI;IAAE4B;EAAS,CAAC,GAAG7C,UAAU,CAACI,WAAW,CAAC;EAClD,MAAM,CAACwE,MAAM,EAAEC,SAAS,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6E,YAAY,EAAEC,KAAK,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM+E,YAAY,GAAIxC,CAAC,IAAK;IAC1BqC,SAAS,CAACrC,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;IACzB9B,SAAS,CAAC,CAAC;IACXyB,QAAQ,CAAC;MACPS,IAAI,EAAE,uBAAuB;MAC7BC,OAAO,EAAEf,CAAC,CAACS,MAAM,CAACC,KAAK;MACvB4B,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM1D,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5ByB,QAAQ,CAAC;MAAES,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5C,IAAI;MACFC,UAAU,CAAC,YAAY;QACrB,IAAIJ,YAAY,GAAG,MAAM9C,aAAa,CAAC,CAAC;QACxC,IAAI8C,YAAY,IAAIA,YAAY,CAACC,QAAQ,EAAE;UACzC0B,KAAK,CAAC3B,YAAY,CAACC,QAAQ,CAAC;UAC5BR,QAAQ,CAAC;YAAES,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;QAC/C;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IAC3BpC,QAAQ,CAAC;MAAES,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAE,CAACtC,IAAI,CAACiE;IAAe,CAAC,CAAC;IACnE9D,SAAS,CAAC,CAAC;IACXyB,QAAQ,CAAC;MAAES,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEuB;IAAa,CAAC,CAAC;IACxDD,SAAS,CAAC,EAAE,CAAC;EACf,CAAC;EAED,oBACEpE,OAAA;IACEmB,SAAS,EAAE,GACTX,IAAI,CAACiE,cAAc,GAAG,EAAE,GAAG,QAAQ,yCACK;IAAApD,QAAA,gBAE1CrB,OAAA;MACEyC,KAAK,EAAE0B,MAAO;MACdZ,QAAQ,EAAGxB,CAAC,IAAKwC,YAAY,CAACxC,CAAC,CAAE;MACjCZ,SAAS,EAAC,sCAAsC;MAChD0B,IAAI,EAAC,MAAM;MACX6B,WAAW,EAAC;IAAoB;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eACFzB,OAAA;MAAK8B,OAAO,EAAGC,CAAC,IAAKyC,cAAc,CAAC,CAAE;MAACrD,SAAS,EAAC,gBAAgB;MAAAE,QAAA,eAC/DrB,OAAA;QACEmB,SAAS,EAAC,0DAA0D;QACpEqC,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAtC,QAAA,eAElCrB,OAAA;UACE4D,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAE,CAAE;UACfC,CAAC,EAAC;QAAsB;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyC,GAAA,CApEID,MAAM;AAAAU,GAAA,GAANV,MAAM;AAsEZ,MAAMW,uBAAuB,GAAIC,KAAK,IAAK;EACzC,oBACE7E,OAAA,CAACV,QAAQ;IAAA+B,QAAA,gBACPrB,OAAA,CAACK,YAAY;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBzB,OAAA,CAACkC,UAAU;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdzB,OAAA,CAACiE,MAAM;MAAA3C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACqD,GAAA,GARIF,uBAAuB;AAU7B,eAAeA,uBAAuB;AAAC,IAAA3C,EAAA,EAAA+B,GAAA,EAAAW,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}