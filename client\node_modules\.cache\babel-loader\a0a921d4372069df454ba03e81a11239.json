{"ast": null, "code": "import ownerDocument from '../ownerDocument';\nexport default function ownerWindow(node) {\n  const doc = ownerDocument(node);\n  return doc.defaultView || window;\n}", "map": {"version": 3, "names": ["ownerDocument", "ownerWindow", "node", "doc", "defaultView", "window"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/utils/esm/ownerWindow/ownerWindow.js"], "sourcesContent": ["import ownerDocument from '../ownerDocument';\nexport default function ownerWindow(node) {\n  const doc = ownerDocument(node);\n  return doc.defaultView || window;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,MAAMC,GAAG,GAAGH,aAAa,CAACE,IAAI,CAAC;EAC/B,OAAOC,GAAG,CAACC,WAAW,IAAIC,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}