{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get order by ID - Updated to match backend endpoint\nexport const getOrderById = async orderId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get all orders, optionally filtered by status\nexport const getAllOrders = async status => {\n  try {\n    let url = `${apiURL}/api/v1/order/all`;\n    if (status && status !== \"All\") {\n      url += `?status=${status}`;\n    }\n    let res = await axios.get(url);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Update order status - Updated to match backend endpoint\nexport const updateOrderStatus = async (orderId, orderStatus) => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/order/${orderId}/status`, null, {\n      params: {\n        orderStatus\n      },\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const deleteOrder = async orderId => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/order/${orderId}/delete`, {}, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getOrderById", "orderId", "res", "get", "data", "error", "console", "log", "getAllOrders", "status", "url", "updateOrderStatus", "orderStatus", "put", "params", "headers", "deleteOrder"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// Get order by ID - Updated to match backend endpoint\r\nexport const getOrderById = async (orderId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get all orders, optionally filtered by status\r\nexport const getAllOrders = async (status) => { \r\n  try {\r\n    let url = `${apiURL}/api/v1/order/all`;\r\n    if (status && status !== \"All\") {\r\n      url += `?status=${status}`;\r\n    }\r\n    let res = await axios.get(url);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Update order status - Updated to match backend endpoint\r\nexport const updateOrderStatus = async (orderId, orderStatus) => { \r\n  try {\r\n    let res = await axios.put(`${apiURL}/api/v1/order/${orderId}/status`, null, {\r\n      params: { orderStatus },\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport const deleteOrder = async (orderId) => {\r\n  try {\r\n    let res = await axios.put(`${apiURL}/api/v1/order/${orderId}/delete`, {}, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }   \r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,GAAGP,MAAM,iBAAiBK,OAAO,EAAE,CAAC;IAC9D,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,YAAY,GAAG,MAAOC,MAAM,IAAK;EAC5C,IAAI;IACF,IAAIC,GAAG,GAAG,GAAGd,MAAM,mBAAmB;IACtC,IAAIa,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;MAC9BC,GAAG,IAAI,WAAWD,MAAM,EAAE;IAC5B;IACA,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAACO,GAAG,CAAC;IAC9B,OAAOR,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,iBAAiB,GAAG,MAAAA,CAAOV,OAAO,EAAEW,WAAW,KAAK;EAC/D,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMP,KAAK,CAACkB,GAAG,CAAC,GAAGjB,MAAM,iBAAiBK,OAAO,SAAS,EAAE,IAAI,EAAE;MAC1Ea,MAAM,EAAE;QAAEF;MAAY,CAAC;MACvBG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOb,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMW,WAAW,GAAG,MAAOf,OAAO,IAAK;EAC5C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACkB,GAAG,CAAC,GAAGjB,MAAM,iBAAiBK,OAAO,SAAS,EAAE,CAAC,CAAC,EAAE;MACxEc,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOb,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}