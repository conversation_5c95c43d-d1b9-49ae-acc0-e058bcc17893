{"ast": null, "code": "function valueOf(obj) {\n  return obj.valueOf ? obj.valueOf() : Object.prototype.valueOf.call(obj);\n}\nfunction valueEqual(a, b) {\n  // Test for strict equality first.\n  if (a === b) return true;\n\n  // Otherwise, if either of them == null they are not equal.\n  if (a == null || b == null) return false;\n  if (Array.isArray(a)) {\n    return Array.isArray(b) && a.length === b.length && a.every(function (item, index) {\n      return valueEqual(item, b[index]);\n    });\n  }\n  if (typeof a === 'object' || typeof b === 'object') {\n    var aValue = valueOf(a);\n    var bValue = valueOf(b);\n    if (aValue !== a || bValue !== b) return valueEqual(aValue, bValue);\n    return Object.keys(Object.assign({}, a, b)).every(function (key) {\n      return valueEqual(a[key], b[key]);\n    });\n  }\n  return false;\n}\nexport default valueEqual;", "map": {"version": 3, "names": ["valueOf", "obj", "Object", "prototype", "call", "valueEqual", "a", "b", "Array", "isArray", "length", "every", "item", "index", "aValue", "bValue", "keys", "assign", "key"], "sources": ["D:/ITSS_Reference/client/node_modules/value-equal/esm/value-equal.js"], "sourcesContent": ["function valueOf(obj) {\n  return obj.valueOf ? obj.valueOf() : Object.prototype.valueOf.call(obj);\n}\n\nfunction valueEqual(a, b) {\n  // Test for strict equality first.\n  if (a === b) return true;\n\n  // Otherwise, if either of them == null they are not equal.\n  if (a == null || b == null) return false;\n\n  if (Array.isArray(a)) {\n    return (\n      Array.isArray(b) &&\n      a.length === b.length &&\n      a.every(function(item, index) {\n        return valueEqual(item, b[index]);\n      })\n    );\n  }\n\n  if (typeof a === 'object' || typeof b === 'object') {\n    var aValue = valueOf(a);\n    var bValue = valueOf(b);\n\n    if (aValue !== a || bValue !== b) return valueEqual(aValue, bValue);\n\n    return Object.keys(Object.assign({}, a, b)).every(function(key) {\n      return valueEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n\nexport default valueEqual;\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOA,GAAG,CAACD,OAAO,GAAGC,GAAG,CAACD,OAAO,CAAC,CAAC,GAAGE,MAAM,CAACC,SAAS,CAACH,OAAO,CAACI,IAAI,CAACH,GAAG,CAAC;AACzE;AAEA,SAASI,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,IAAI;;EAExB;EACA,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE,OAAO,KAAK;EAExC,IAAIC,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,EAAE;IACpB,OACEE,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAChBD,CAAC,CAACI,MAAM,KAAKH,CAAC,CAACG,MAAM,IACrBJ,CAAC,CAACK,KAAK,CAAC,UAASC,IAAI,EAAEC,KAAK,EAAE;MAC5B,OAAOR,UAAU,CAACO,IAAI,EAAEL,CAAC,CAACM,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC;EAEN;EAEA,IAAI,OAAOP,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,IAAIO,MAAM,GAAGd,OAAO,CAACM,CAAC,CAAC;IACvB,IAAIS,MAAM,GAAGf,OAAO,CAACO,CAAC,CAAC;IAEvB,IAAIO,MAAM,KAAKR,CAAC,IAAIS,MAAM,KAAKR,CAAC,EAAE,OAAOF,UAAU,CAACS,MAAM,EAAEC,MAAM,CAAC;IAEnE,OAAOb,MAAM,CAACc,IAAI,CAACd,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC,EAAEX,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,UAASO,GAAG,EAAE;MAC9D,OAAOb,UAAU,CAACC,CAAC,CAACY,GAAG,CAAC,EAAEX,CAAC,CAACW,GAAG,CAAC,CAAC;IACnC,CAAC,CAAC;EACJ;EAEA,OAAO,KAAK;AACd;AAEA,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}