{"ast": null, "code": "'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\nvar defaults = {\n  adapter: getDefaultAdapter(),\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data)) {\n      setContentTypeIfUnset(headers, 'application/json;charset=utf-8');\n      return JSON.stringify(data);\n    }\n    return data;\n  }],\n  transformResponse: [function transformResponse(data) {\n    /*eslint no-param-reassign:0*/\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data);\n      } catch (e) {/* Ignore */}\n    }\n    return data;\n  }],\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n  maxContentLength: -1,\n  maxBodyLength: -1,\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  }\n};\ndefaults.headers = {\n  common: {\n    'Accept': 'application/json, text/plain, */*'\n  }\n};\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\nmodule.exports = defaults;", "map": {"version": 3, "names": ["utils", "require", "normalizeHeaderName", "DEFAULT_CONTENT_TYPE", "setContentTypeIfUnset", "headers", "value", "isUndefined", "getDefaultAdapter", "adapter", "XMLHttpRequest", "process", "Object", "prototype", "toString", "call", "defaults", "transformRequest", "data", "isFormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "isObject", "JSON", "stringify", "transformResponse", "parse", "e", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateStatus", "status", "common", "for<PERSON>ach", "forEachMethodNoData", "method", "forEachMethodWithData", "merge", "module", "exports"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/defaults.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\n\nvar defaults = {\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data)) {\n      setContentTypeIfUnset(headers, 'application/json;charset=utf-8');\n      return JSON.stringify(data);\n    }\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    /*eslint no-param-reassign:0*/\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data);\n      } catch (e) { /* Ignore */ }\n    }\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  }\n};\n\ndefaults.headers = {\n  common: {\n    'Accept': 'application/json, text/plain, */*'\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAElE,IAAIE,oBAAoB,GAAG;EACzB,cAAc,EAAE;AAClB,CAAC;AAED,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC7C,IAAI,CAACN,KAAK,CAACO,WAAW,CAACF,OAAO,CAAC,IAAIL,KAAK,CAACO,WAAW,CAACF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE;IAC7EA,OAAO,CAAC,cAAc,CAAC,GAAGC,KAAK;EACjC;AACF;AAEA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,IAAIC,OAAO;EACX,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;IACzC;IACAD,OAAO,GAAGR,OAAO,CAAC,gBAAgB,CAAC;EACrC,CAAC,MAAM,IAAI,OAAOU,OAAO,KAAK,WAAW,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,KAAK,kBAAkB,EAAE;IAC3G;IACAF,OAAO,GAAGR,OAAO,CAAC,iBAAiB,CAAC;EACtC;EACA,OAAOQ,OAAO;AAChB;AAEA,IAAIO,QAAQ,GAAG;EACbP,OAAO,EAAED,iBAAiB,CAAC,CAAC;EAE5BS,gBAAgB,EAAE,CAAC,SAASA,gBAAgBA,CAACC,IAAI,EAAEb,OAAO,EAAE;IAC1DH,mBAAmB,CAACG,OAAO,EAAE,QAAQ,CAAC;IACtCH,mBAAmB,CAACG,OAAO,EAAE,cAAc,CAAC;IAC5C,IAAIL,KAAK,CAACmB,UAAU,CAACD,IAAI,CAAC,IACxBlB,KAAK,CAACoB,aAAa,CAACF,IAAI,CAAC,IACzBlB,KAAK,CAACqB,QAAQ,CAACH,IAAI,CAAC,IACpBlB,KAAK,CAACsB,QAAQ,CAACJ,IAAI,CAAC,IACpBlB,KAAK,CAACuB,MAAM,CAACL,IAAI,CAAC,IAClBlB,KAAK,CAACwB,MAAM,CAACN,IAAI,CAAC,EAClB;MACA,OAAOA,IAAI;IACb;IACA,IAAIlB,KAAK,CAACyB,iBAAiB,CAACP,IAAI,CAAC,EAAE;MACjC,OAAOA,IAAI,CAACQ,MAAM;IACpB;IACA,IAAI1B,KAAK,CAAC2B,iBAAiB,CAACT,IAAI,CAAC,EAAE;MACjCd,qBAAqB,CAACC,OAAO,EAAE,iDAAiD,CAAC;MACjF,OAAOa,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACxB;IACA,IAAId,KAAK,CAAC4B,QAAQ,CAACV,IAAI,CAAC,EAAE;MACxBd,qBAAqB,CAACC,OAAO,EAAE,gCAAgC,CAAC;MAChE,OAAOwB,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC;IAC7B;IACA,OAAOA,IAAI;EACb,CAAC,CAAC;EAEFa,iBAAiB,EAAE,CAAC,SAASA,iBAAiBA,CAACb,IAAI,EAAE;IACnD;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI;QACFA,IAAI,GAAGW,IAAI,CAACG,KAAK,CAACd,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOe,CAAC,EAAE,CAAE;IAChB;IACA,OAAOf,IAAI;EACb,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEgB,OAAO,EAAE,CAAC;EAEVC,cAAc,EAAE,YAAY;EAC5BC,cAAc,EAAE,cAAc;EAE9BC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,aAAa,EAAE,CAAC,CAAC;EAEjBC,cAAc,EAAE,SAASA,cAAcA,CAACC,MAAM,EAAE;IAC9C,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACtC;AACF,CAAC;AAEDxB,QAAQ,CAACX,OAAO,GAAG;EACjBoC,MAAM,EAAE;IACN,QAAQ,EAAE;EACZ;AACF,CAAC;AAEDzC,KAAK,CAAC0C,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC5E5B,QAAQ,CAACX,OAAO,CAACuC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF5C,KAAK,CAAC0C,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASG,qBAAqBA,CAACD,MAAM,EAAE;EAC7E5B,QAAQ,CAACX,OAAO,CAACuC,MAAM,CAAC,GAAG5C,KAAK,CAAC8C,KAAK,CAAC3C,oBAAoB,CAAC;AAC9D,CAAC,CAAC;AAEF4C,MAAM,CAACC,OAAO,GAAGhC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}