{"ast": null, "code": "'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n  if (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') {\n    return false;\n  }\n  if (typeof Symbol.iterator === 'symbol') {\n    return true;\n  }\n\n  /** @type {{ [k in symbol]?: unknown }} */\n  var obj = {};\n  var sym = Symbol('test');\n  var symObj = Object(sym);\n  if (typeof sym === 'string') {\n    return false;\n  }\n  if (Object.prototype.toString.call(sym) !== '[object Symbol]') {\n    return false;\n  }\n  if (Object.prototype.toString.call(symObj) !== '[object Symbol]') {\n    return false;\n  }\n\n  // temp disabled per https://github.com/ljharb/object.assign/issues/17\n  // if (sym instanceof Symbol) { return false; }\n  // temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n  // if (!(symObj instanceof Symbol)) { return false; }\n\n  // if (typeof Symbol.prototype.toString !== 'function') { return false; }\n  // if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n  var symVal = 42;\n  obj[sym] = symVal;\n  for (var _ in obj) {\n    return false;\n  } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n  if (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) {\n    return false;\n  }\n  var syms = Object.getOwnPropertySymbols(obj);\n  if (syms.length !== 1 || syms[0] !== sym) {\n    return false;\n  }\n  if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyDescriptor === 'function') {\n    // eslint-disable-next-line no-extra-parens\n    var descriptor = /** @type {PropertyDescriptor} */Object.getOwnPropertyDescriptor(obj, sym);\n    if (descriptor.value !== symVal || descriptor.enumerable !== true) {\n      return false;\n    }\n  }\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "hasSymbols", "Symbol", "Object", "getOwnPropertySymbols", "iterator", "obj", "sym", "symObj", "prototype", "toString", "call", "symVal", "_", "keys", "length", "getOwnPropertyNames", "syms", "propertyIsEnumerable", "getOwnPropertyDescriptor", "descriptor", "value", "enumerable"], "sources": ["D:/ITSS_Reference/client/node_modules/has-symbols/shams.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\t/** @type {{ [k in symbol]?: unknown }} */\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (var _ in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {PropertyDescriptor} */ (Object.getOwnPropertyDescriptor(obj, sym));\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAAA,EAAG;EACtC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOC,MAAM,CAACC,qBAAqB,KAAK,UAAU,EAAE;IAAE,OAAO,KAAK;EAAE;EACxG,IAAI,OAAOF,MAAM,CAACG,QAAQ,KAAK,QAAQ,EAAE;IAAE,OAAO,IAAI;EAAE;;EAExD;EACA,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,GAAG,GAAGL,MAAM,CAAC,MAAM,CAAC;EACxB,IAAIM,MAAM,GAAGL,MAAM,CAACI,GAAG,CAAC;EACxB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7C,IAAIJ,MAAM,CAACM,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,KAAK,iBAAiB,EAAE;IAAE,OAAO,KAAK;EAAE;EAC/E,IAAIJ,MAAM,CAACM,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAAE,OAAO,KAAK;EAAE;;EAElF;EACA;EACA;EACA;;EAEA;EACA;;EAEA,IAAII,MAAM,GAAG,EAAE;EACfN,GAAG,CAACC,GAAG,CAAC,GAAGK,MAAM;EACjB,KAAK,IAAIC,CAAC,IAAIP,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE,CAAC,CAAC;EACrC,IAAI,OAAOH,MAAM,CAACW,IAAI,KAAK,UAAU,IAAIX,MAAM,CAACW,IAAI,CAACR,GAAG,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAExF,IAAI,OAAOZ,MAAM,CAACa,mBAAmB,KAAK,UAAU,IAAIb,MAAM,CAACa,mBAAmB,CAACV,GAAG,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEtH,IAAIE,IAAI,GAAGd,MAAM,CAACC,qBAAqB,CAACE,GAAG,CAAC;EAC5C,IAAIW,IAAI,CAACF,MAAM,KAAK,CAAC,IAAIE,IAAI,CAAC,CAAC,CAAC,KAAKV,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE;EAE1D,IAAI,CAACJ,MAAM,CAACM,SAAS,CAACS,oBAAoB,CAACP,IAAI,CAACL,GAAG,EAAEC,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE3E,IAAI,OAAOJ,MAAM,CAACgB,wBAAwB,KAAK,UAAU,EAAE;IAC1D;IACA,IAAIC,UAAU,GAAG,iCAAmCjB,MAAM,CAACgB,wBAAwB,CAACb,GAAG,EAAEC,GAAG,CAAE;IAC9F,IAAIa,UAAU,CAACC,KAAK,KAAKT,MAAM,IAAIQ,UAAU,CAACE,UAAU,KAAK,IAAI,EAAE;MAAE,OAAO,KAAK;IAAE;EACpF;EAEA,OAAO,IAAI;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}