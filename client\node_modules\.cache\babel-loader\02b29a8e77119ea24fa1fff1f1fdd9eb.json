{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\wishlist\\\\index.js\";\nimport React, { Fragment } from \"react\";\nimport Layout from \"../layout\";\nimport SingleWishProduct from \"./SingleWishProduct\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WishList = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(SingleWishProduct, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = WishList;\nexport default WishList;\nvar _c;\n$RefreshReg$(_c, \"WishList\");", "map": {"version": 3, "names": ["React", "Fragment", "Layout", "SingleWishProduct", "jsxDEV", "_jsxDEV", "WishList", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/wishlist/index.js"], "sourcesContent": ["import React, { Fragment } from \"react\";\r\nimport Layout from \"../layout\";\r\nimport SingleWishProduct from \"./SingleWishProduct\";\r\n\r\nconst WishList = () => {\r\n  return (\r\n    <Fragment>\r\n      <Layout children={<SingleWishProduct />} />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default WishList;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA,CAACJ,QAAQ;IAAAM,QAAA,eACPF,OAAA,CAACH,MAAM;MAACK,QAAQ,eAAEF,OAAA,CAACF,iBAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnC,CAAC;AAEf,CAAC;AAACC,EAAA,GANIN,QAAQ;AAQd,eAAeA,QAAQ;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}