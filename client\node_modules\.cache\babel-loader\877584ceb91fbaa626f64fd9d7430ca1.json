{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getUserById = async uId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/user/signle-user`, {\n      uId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const updatePersonalInformationFetch = async userData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/user/edit-user`, userData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const getOrderByUser = async uId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/order/order-by-user`, {\n      uId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const updatePassword = async formData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/user/change-password`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getUserById", "uId", "res", "post", "data", "error", "console", "log", "updatePersonalInformationFetch", "userData", "getOrderByUser", "updatePassword", "formData"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getUserById = async (uId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/user/signle-user`, { uId });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const updatePersonalInformationFetch = async (userData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/user/edit-user`, userData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getOrderByUser = async (uId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/order-by-user`, { uId });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const updatePassword = async (formData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/user/change-password`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,WAAW,GAAG,MAAOC,GAAG,IAAK;EACxC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,uBAAuB,EAAE;MAAEK;IAAI,CAAC,CAAC;IACrE,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,8BAA8B,GAAG,MAAOC,QAAQ,IAAK;EAChE,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,qBAAqB,EAAEa,QAAQ,CAAC;IACpE,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,cAAc,GAAG,MAAOT,GAAG,IAAK;EAC3C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,0BAA0B,EAAE;MAAEK;IAAI,CAAC,CAAC;IACxE,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMM,cAAc,GAAG,MAAOC,QAAQ,IAAK;EAChD,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,2BAA2B,EAAEgB,QAAQ,CAAC;IAC1E,OAAOV,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}