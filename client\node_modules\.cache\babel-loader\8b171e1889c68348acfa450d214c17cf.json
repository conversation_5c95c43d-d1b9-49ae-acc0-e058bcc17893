{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const cartListProduct = async () => {\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\n  let productArray = [];\n  if (carts) {\n    for (const cart of carts) {\n      productArray.push(cart.id);\n    }\n  }\n  try {\n    let res = await axios.post(`${apiURL}/api/product/cart-product`, {\n      productArray\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "cartListProduct", "carts", "JSON", "parse", "localStorage", "getItem", "productArray", "cart", "push", "id", "res", "post", "data", "error", "console", "log"], "sources": ["D:/ITSS_Reference/client/src/components/shop/partials/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const cartListProduct = async () => {\r\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\r\n  let productArray = [];\r\n  if (carts) {\r\n    for (const cart of carts) {\r\n      productArray.push(cart.id);\r\n    }\r\n  }\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/cart-product`, {\r\n      productArray,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;EACzC,IAAIC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACpD,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIL,KAAK,EAAE;IACT,KAAK,MAAMM,IAAI,IAAIN,KAAK,EAAE;MACxBK,YAAY,CAACE,IAAI,CAACD,IAAI,CAACE,EAAE,CAAC;IAC5B;EACF;EACA,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,GAAGf,MAAM,2BAA2B,EAAE;MAC/DU;IACF,CAAC,CAAC;IACF,OAAOI,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}