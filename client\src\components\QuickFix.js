import React, { useEffect, useState } from 'react';

const QuickFix = () => {
  const [overlayElements, setOverlayElements] = useState([]);
  const [fixApplied, setFixApplied] = useState(false);

  useEffect(() => {
    // Tìm tất cả elements có thể gây vấn đề
    const findProblematicElements = () => {
      const elements = [];
      
      // Tìm tất cả fixed/absolute elements
      document.querySelectorAll('.fixed, .absolute').forEach(el => {
        const style = getComputedStyle(el);
        const zIndex = parseInt(style.zIndex) || 0;
        const display = style.display;
        const visibility = style.visibility;
        const pointerEvents = style.pointerEvents;
        
        if (zIndex > 10 && display !== 'none' && visibility !== 'hidden') {
          elements.push({
            element: el,
            zIndex,
            className: el.className,
            pointerEvents,
            rect: el.getBoundingClientRect()
          });
        }
      });
      
      setOverlayElements(elements);
    };

    findProblematicElements();
    
    // Re-check every 2 seconds
    const interval = setInterval(findProblematicElements, 2000);
    return () => clearInterval(interval);
  }, []);

  const applyQuickFix = () => {
    // Fix 1: Remove problematic overlays
    document.querySelectorAll('.fixed').forEach(el => {
      const style = getComputedStyle(el);
      const zIndex = parseInt(style.zIndex) || 0;
      
      // Hide high z-index elements that might be blocking
      if (zIndex > 30 && !el.textContent.includes('Debug') && !el.textContent.includes('Quick Fix')) {
        el.style.display = 'none';
        console.log('🔧 Hidden problematic overlay:', el);
      }
    });

    // Fix 2: Ensure pointer events work
    document.body.style.pointerEvents = 'auto';
    
    // Fix 3: Remove any invisible overlays
    document.querySelectorAll('div').forEach(el => {
      const style = getComputedStyle(el);
      if (style.position === 'fixed' && 
          style.backgroundColor === 'rgba(0, 0, 0, 0)' && 
          (el.offsetWidth > window.innerWidth * 0.8 || el.offsetHeight > window.innerHeight * 0.8)) {
        el.style.display = 'none';
        console.log('🔧 Hidden invisible overlay:', el);
      }
    });

    setFixApplied(true);
    
    // Test if fix worked
    setTimeout(() => {
      const testButton = document.createElement('button');
      testButton.textContent = 'Test Click';
      testButton.style.position = 'fixed';
      testButton.style.top = '50px';
      testButton.style.right = '50px';
      testButton.style.zIndex = '9999';
      testButton.style.padding = '10px';
      testButton.style.backgroundColor = 'green';
      testButton.style.color = 'white';
      testButton.onclick = () => {
        alert('✅ Click is working!');
        testButton.remove();
      };
      document.body.appendChild(testButton);
      
      setTimeout(() => testButton.remove(), 5000);
    }, 1000);
  };

  const resetPage = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.reload();
  };

  return (
    <div className="fixed bottom-4 left-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <h3 className="font-bold mb-2">🚨 Quick Fix Panel</h3>
      
      <div className="text-sm mb-3">
        <div>Overlays found: {overlayElements.length}</div>
        <div>Fix applied: {fixApplied ? '✅' : '❌'}</div>
      </div>

      {overlayElements.length > 0 && (
        <div className="mb-3 text-xs">
          <div className="font-semibold">Problematic elements:</div>
          {overlayElements.slice(0, 3).map((item, index) => (
            <div key={index} className="truncate">
              z-index: {item.zIndex}, class: {item.className.substring(0, 20)}...
            </div>
          ))}
        </div>
      )}

      <div className="space-y-2">
        <button 
          onClick={applyQuickFix}
          className="w-full px-3 py-1 bg-yellow-500 text-black rounded text-sm hover:bg-yellow-400"
        >
          🔧 Apply Quick Fix
        </button>
        
        <button 
          onClick={resetPage}
          className="w-full px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-400"
        >
          🔄 Reset Page
        </button>
        
        <button 
          onClick={() => window.location.href = '/simple-test'}
          className="w-full px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-400"
        >
          🧪 Simple Test
        </button>
      </div>

      <div className="text-xs mt-2 opacity-75">
        If buttons don't work, check console (F12)
      </div>
    </div>
  );
};

export default QuickFix;
