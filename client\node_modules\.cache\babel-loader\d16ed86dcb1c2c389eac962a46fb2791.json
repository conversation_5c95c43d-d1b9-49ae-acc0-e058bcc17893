{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _braintreeWebDropIn = _interopRequireDefault(require(\"braintree-web-drop-in\"));\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        (0, _defineProperty2[\"default\"])(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = (0, _getPrototypeOf2[\"default\"])(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = (0, _getPrototypeOf2[\"default\"])(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return (0, _possibleConstructorReturn2[\"default\"])(this, result);\n  };\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar DropIn = /*#__PURE__*/function (_React$Component) {\n  (0, _inherits2[\"default\"])(DropIn, _React$Component);\n  var _super = _createSuper(DropIn);\n  function DropIn() {\n    var _this;\n    (0, _classCallCheck2[\"default\"])(this, DropIn);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"wrapper\", void 0);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"instance\", void 0);\n    return _this;\n  }\n  (0, _createClass2[\"default\"])(DropIn, [{\n    key: \"componentDidMount\",\n    value: function () {\n      var _componentDidMount = (0, _asyncToGenerator2[\"default\"])(/*#__PURE__*/_regenerator[\"default\"].mark(function _callee() {\n        var _this2 = this;\n        return _regenerator[\"default\"].wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.prev = 0;\n                _context.next = 3;\n                return _braintreeWebDropIn[\"default\"].create(_objectSpread({\n                  container: _reactDom[\"default\"].findDOMNode(this.wrapper),\n                  preselectVaultedPaymentMethod: this.props.preselectVaultedPaymentMethod\n                }, this.props.options));\n              case 3:\n                this.instance = _context.sent;\n                this.instance.on(\"noPaymentMethodRequestable\", function () {\n                  if (_this2.props.onNoPaymentMethodRequestable) {\n                    var _this2$props;\n                    (_this2$props = _this2.props).onNoPaymentMethodRequestable.apply(_this2$props, arguments);\n                  }\n                });\n                this.instance.on(\"paymentMethodRequestable\", function () {\n                  if (_this2.props.onPaymentMethodRequestable) {\n                    var _this2$props2;\n                    (_this2$props2 = _this2.props).onPaymentMethodRequestable.apply(_this2$props2, arguments);\n                  }\n                });\n                this.instance.on(\"paymentOptionSelected\", function () {\n                  if (_this2.props.onPaymentOptionSelected) {\n                    var _this2$props3;\n                    (_this2$props3 = _this2.props).onPaymentOptionSelected.apply(_this2$props3, arguments);\n                  }\n                });\n                if (this.props.onInstance) {\n                  this.props.onInstance(this.instance);\n                }\n                _context.next = 13;\n                break;\n              case 10:\n                _context.prev = 10;\n                _context.t0 = _context[\"catch\"](0);\n                if (this.props.onError) {\n                  this.props.onError(_context.t0);\n                }\n              case 13:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[0, 10]]);\n      }));\n      function componentDidMount() {\n        return _componentDidMount.apply(this, arguments);\n      }\n      return componentDidMount;\n    }()\n  }, {\n    key: \"componentWillUnmount\",\n    value: function () {\n      var _componentWillUnmount = (0, _asyncToGenerator2[\"default\"])(/*#__PURE__*/_regenerator[\"default\"].mark(function _callee2() {\n        return _regenerator[\"default\"].wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!this.instance) {\n                  _context2.next = 3;\n                  break;\n                }\n                _context2.next = 3;\n                return this.instance.teardown();\n              case 3:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n      function componentWillUnmount() {\n        return _componentWillUnmount.apply(this, arguments);\n      }\n      return componentWillUnmount;\n    }()\n  }, {\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate() {\n      // Static\n      return false;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n        ref: function ref(_ref) {\n          return _this3.wrapper = _ref;\n        }\n      });\n    }\n  }]);\n  return DropIn;\n}(_react[\"default\"].Component);\nexports[\"default\"] = DropIn;\n(0, _defineProperty2[\"default\"])(DropIn, \"displayName\", \"BraintreeWebDropIn\");\n(0, _defineProperty2[\"default\"])(DropIn, \"propTypes\", {\n  options: _propTypes[\"default\"].object.isRequired,\n  // @deprecated: Include inside options\n  preselectVaultedPaymentMethod: _propTypes[\"default\"].bool,\n  onInstance: _propTypes[\"default\"].func,\n  onError: _propTypes[\"default\"].func,\n  onNoPaymentMethodRequestable: _propTypes[\"default\"].func,\n  onPaymentMethodRequestable: _propTypes[\"default\"].func,\n  onPaymentOptionSelected: _propTypes[\"default\"].func\n});\n(0, _defineProperty2[\"default\"])(DropIn, \"defaultProps\", {\n  preselectVaultedPaymentMethod: true\n});", "map": {"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactDom", "_propTypes", "_braintreeWebDropIn", "DropIn", "create", "_objectSpread", "container", "findDOMNode", "wrapper", "preselectVaultedPaymentMethod", "props", "options", "instance", "on", "_this2", "onNoPaymentMethodRequestable", "_this2$props", "apply", "arguments", "onPaymentMethodRequestable", "_this2$props2", "onPaymentOptionSelected", "_this2$props3", "onInstance", "onError", "_context", "t0", "teardown", "_this3", "createElement", "ref", "_ref", "Component", "object", "isRequired", "bool", "func"], "sources": ["../src/index.js"], "sourcesContent": ["import React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport PropTypes from \"prop-types\";\nimport BraintreeWebDropIn from \"braintree-web-drop-in\";\n\nexport default class DropIn extends React.Component {\n  static displayName = \"BraintreeWebDropIn\";\n\n  static propTypes = {\n    options: PropTypes.object.isRequired,\n    // @deprecated: Include inside options\n    preselectVaultedPaymentMethod: PropTypes.bool,\n\n    onInstance: PropTypes.func,\n    onError: PropTypes.func,\n\n    onNoPaymentMethodRequestable: PropTypes.func,\n    onPaymentMethodRequestable: PropTypes.func,\n    onPaymentOptionSelected: PropTypes.func,\n  };\n\n  static defaultProps = {\n    preselectVaultedPaymentMethod: true,\n  };\n\n  wrapper;\n  instance;\n\n  async componentDidMount() {\n    try {\n      this.instance = await BraintreeWebDropIn.create({\n        container: ReactDOM.findDOMNode(this.wrapper),\n        preselectVaultedPaymentMethod: this.props.preselectVaultedPaymentMethod,\n        ...this.props.options,\n      });\n\n      this.instance.on(\"noPaymentMethodRequestable\", (...args) => {\n        if (this.props.onNoPaymentMethodRequestable) {\n          this.props.onNoPaymentMethodRequestable(...args);\n        }\n      });\n      this.instance.on(\"paymentMethodRequestable\", (...args) => {\n        if (this.props.onPaymentMethodRequestable) {\n          this.props.onPaymentMethodRequestable(...args);\n        }\n      });\n      this.instance.on(\"paymentOptionSelected\", (...args) => {\n        if (this.props.onPaymentOptionSelected) {\n          this.props.onPaymentOptionSelected(...args);\n        }\n      });\n\n      if (this.props.onInstance) {\n        this.props.onInstance(this.instance);\n      }\n    } catch (error) {\n      if (this.props.onError) {\n        this.props.onError(error);\n      }\n    }\n  }\n\n  async componentWillUnmount() {\n    if (this.instance) {\n      await this.instance.teardown();\n    }\n  }\n\n  shouldComponentUpdate() {\n    // Static\n    return false;\n  }\n\n  render() {\n    return <div ref={(ref) => (this.wrapper = ref)} />;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,mBAAA,GAAAJ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAEqBI,M;;;;;;;;;;;;;;;;;;;;;;;;;uBAyBOD,mBAAA,YAAmBE,MAAnB,CAAAC,aAAA;kBACpBC,SAAS,EAAEN,SAAA,YAASO,WAAT,CAAqB,KAAKC,OAA1B,CADS;kBAEpBC,6BAA6B,EAAE,KAAKC,KAAL,CAAWD;gBAFtB,GAGjB,KAAKC,KAAL,CAAWC,OAHM,E;;gBAAtB,KAAKC,Q;gBAML,KAAKA,QAAL,CAAcC,EAAd,CAAiB,4BAAjB,EAA+C,YAAa;kBAC1D,IAAIC,MAAI,CAACJ,KAAL,CAAWK,4BAAf,EAA6C;oBAAA,IAAAC,YAAA;oBAC3C,CAAAA,YAAA,GAAAF,MAAI,CAACJ,KAAL,EAAWK,4BAAX,CAAAE,KAAA,CAAAD,YAAA,EAAAE,SAAA;kBACD;gBACF,CAJD;gBAKA,KAAKN,QAAL,CAAcC,EAAd,CAAiB,0BAAjB,EAA6C,YAAa;kBACxD,IAAIC,MAAI,CAACJ,KAAL,CAAWS,0BAAf,EAA2C;oBAAA,IAAAC,aAAA;oBACzC,CAAAA,aAAA,GAAAN,MAAI,CAACJ,KAAL,EAAWS,0BAAX,CAAAF,KAAA,CAAAG,aAAA,EAAAF,SAAA;kBACD;gBACF,CAJD;gBAKA,KAAKN,QAAL,CAAcC,EAAd,CAAiB,uBAAjB,EAA0C,YAAa;kBACrD,IAAIC,MAAI,CAACJ,KAAL,CAAWW,uBAAf,EAAwC;oBAAA,IAAAC,aAAA;oBACtC,CAAAA,aAAA,GAAAR,MAAI,CAACJ,KAAL,EAAWW,uBAAX,CAAAJ,KAAA,CAAAK,aAAA,EAAAJ,SAAA;kBACD;gBACF,CAJD;gBAMA,IAAI,KAAKR,KAAL,CAAWa,UAAf,EAA2B;kBACzB,KAAKb,KAAL,CAAWa,UAAX,CAAsB,KAAKX,QAA3B;gBACD;;;;;;gBAED,IAAI,KAAKF,KAAL,CAAWc,OAAf,EAAwB;kBACtB,KAAKd,KAAL,CAAWc,OAAX,CAAAC,QAAA,CAAAC,EAAA;gBACD;;;;;;;;;;;;;;;;;;;;;qBAKC,KAAKd,Q;;;;;uBACD,KAAKA,QAAL,CAAce,QAAd,E;;;;;;;;;;;;;;;4CAIc;MACtB;MACA,OAAO,KAAP;IACD;;;6BAEQ;MAAA,IAAAC,MAAA;MACP,oBAAO/B,MAAA,YAAAgC,aAAA;QAAKC,GAAG,EAAE,SAAAA,IAACC,IAAD;UAAA,OAAUH,MAAI,CAACpB,OAAL,GAAeuB,IAAzB;QAAA;MAAV,EAAP;IACD;;;EAtEiClC,MAAA,YAAMmC,S;;iCAArB7B,M,iBACE,oB;iCADFA,M,eAGA;EACjBQ,OAAO,EAAEV,UAAA,YAAUgC,MAAV,CAAiBC,UADT;EAEjB;EACAzB,6BAA6B,EAAER,UAAA,YAAUkC,IAHxB;EAKjBZ,UAAU,EAAEtB,UAAA,YAAUmC,IALL;EAMjBZ,OAAO,EAAEvB,UAAA,YAAUmC,IANF;EAQjBrB,4BAA4B,EAAEd,UAAA,YAAUmC,IARvB;EASjBjB,0BAA0B,EAAElB,UAAA,YAAUmC,IATrB;EAUjBf,uBAAuB,EAAEpB,UAAA,YAAUmC;AAVlB,C;iCAHAjC,M,kBAgBG;EACpBM,6BAA6B,EAAE;AADX,C", "ignoreList": []}, "metadata": {}, "sourceType": "script"}