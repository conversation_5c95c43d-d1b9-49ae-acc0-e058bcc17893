import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const cartListProduct = async (userId = 1) => {
  try {
    // Sử dụng API cart mới thay vì localStorage
    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);

    // Convert cart data to match old format for compatibility
    if (res.data && res.data.cartItems) {
      const products = res.data.cartItems.map(item => ({
        _id: item.productId,
        pName: item.productName,
        pPrice: item.price,
        pImages: item.images || [],
        quantity: item.quantity,
        pCategory: { cName: item.category || 'Unknown' }
      }));

      return { Products: products };
    }

    return { Products: [] };
  } catch (error) {
    console.log("⚠️ cartListProduct: Cart empty or error:", error.message);
    return { Products: [] };
  }
};
