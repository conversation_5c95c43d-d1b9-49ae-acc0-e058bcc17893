{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport AdminLayout from \"../layout\";\nimport OrderMenu from \"./OrderMenu\";\nimport AllOrders from \"./AllOrders\";\nimport { orderState, orderReducer } from \"./OrderContext\";\n\n/* This context manage all of the orders component's data */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const OrderContext = /*#__PURE__*/createContext();\nconst OrderComponent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 space-y-4 p-4\",\n    children: [/*#__PURE__*/_jsxDEV(OrderMenu, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AllOrders, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = OrderComponent;\nconst Orders = props => {\n  _s();\n  const [data, dispatch] = useReducer(orderReducer, orderState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(OrderContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n        children: /*#__PURE__*/_jsxDEV(OrderComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(Orders, \"/iRhnmdXAu9uoHkRGYbHhpNJ6Pc=\");\n_c2 = Orders;\nexport default Orders;\nvar _c, _c2;\n$RefreshReg$(_c, \"OrderComponent\");\n$RefreshReg$(_c2, \"Orders\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "AdminLayout", "OrderMenu", "AllOrders", "orderState", "orderReducer", "jsxDEV", "_jsxDEV", "OrderContext", "OrderComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Orders", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/index.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport AdminLayout from \"../layout\";\r\nimport OrderMenu from \"./OrderMenu\";\r\nimport AllOrders from \"./AllOrders\";\r\nimport { orderState, orderReducer } from \"./OrderContext\";\r\n\r\n/* This context manage all of the orders component's data */\r\nexport const OrderContext = createContext();\r\n\r\nconst OrderComponent = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 space-y-4 p-4\">\r\n      <OrderMenu />\r\n      <AllOrders />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Orders = (props) => {\r\n  const [data, dispatch] = useReducer(orderReducer, orderState);\r\n  return (\r\n    <Fragment>\r\n      <OrderContext.Provider value={{ data, dispatch }}>\r\n        <AdminLayout children={<OrderComponent />} />\r\n      </OrderContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Orders;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,UAAU,EAAEC,YAAY,QAAQ,gBAAgB;;AAEzD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,YAAY,gBAAGT,aAAa,CAAC,CAAC;AAE3C,MAAMU,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACEF,OAAA;IAAKG,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7CJ,OAAA,CAACL,SAAS;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbR,OAAA,CAACJ,SAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIP,cAAc;AASpB,MAAMQ,MAAM,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACxB,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGrB,UAAU,CAACK,YAAY,EAAED,UAAU,CAAC;EAC7D,oBACEG,OAAA,CAACT,QAAQ;IAAAa,QAAA,eACPJ,OAAA,CAACC,YAAY,CAACc,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAV,QAAA,eAC/CJ,OAAA,CAACN,WAAW;QAACU,QAAQ,eAAEJ,OAAA,CAACE,cAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEf,CAAC;AAACI,EAAA,CATIF,MAAM;AAAAO,GAAA,GAANP,MAAM;AAWZ,eAAeA,MAAM;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}