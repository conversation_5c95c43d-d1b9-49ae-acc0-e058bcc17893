{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllProduct = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const getAllProductForManager = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createPorductImage = async ({\n  pImage\n}) => {\n  /* Most important part for uploading multiple image  */\n  let formData = new FormData();\n  for (const file of pImage) {\n    formData.append(\"pImage\", file);\n  }\n  /* Most important part for uploading multiple image  */\n};\nexport const createProduct = async ({\n  pName,\n  pDescription,\n  pImage,\n  pStatus,\n  pCategory,\n  pQuantity,\n  pPrice,\n  pOffer\n}) => {\n  /* Most important part for uploading multiple image  */\n  let formData = new FormData();\n  for (const file of pImage) {\n    formData.append(\"pImage\", file);\n  }\n  /* Most important part for uploading multiple image  */\n  formData.append(\"pName\", pName);\n  formData.append(\"pDescription\", pDescription);\n  formData.append(\"pStatus\", pStatus);\n  formData.append(\"pCategory\", pCategory);\n  formData.append(\"pQuantity\", pQuantity);\n  formData.append(\"pPrice\", pPrice);\n  formData.append(\"pOffer\", pOffer);\n  try {\n    let res = await axios.post(`${apiURL}/api/product/add-product`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const editProduct = async product => {\n  console.log(product);\n  /* Most important part for updating multiple image  */\n  let formData = new FormData();\n  if (product.pEditImages) {\n    for (const file of product.pEditImages) {\n      formData.append(\"pEditImages\", file);\n    }\n  }\n  /* Most important part for updating multiple image  */\n  formData.append(\"pId\", product.pId);\n  formData.append(\"pName\", product.pName);\n  formData.append(\"pDescription\", product.pDescription);\n  formData.append(\"pStatus\", product.pStatus);\n  formData.append(\"pCategory\", product.pCategory._id);\n  formData.append(\"pQuantity\", product.pQuantity);\n  formData.append(\"pPrice\", product.pPrice);\n  formData.append(\"pOffer\", product.pOffer);\n  formData.append(\"pImages\", product.pImages);\n  try {\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteProduct = async pId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/delete-product`, {\n      pId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const productByCategory = async catId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\n      catId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const productByPrice = async price => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\n      price\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllProduct", "res", "get", "data", "error", "console", "log", "getAllProductForManager", "createPorductImage", "pImage", "formData", "FormData", "file", "append", "createProduct", "pName", "pDescription", "pStatus", "pCategory", "pQuantity", "pPrice", "pOffer", "post", "editProduct", "product", "pEditImages", "pId", "_id", "pImages", "deleteProduct", "productByCategory", "catId", "productByPrice", "price"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllProduct = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getAllProductForManager = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createPorductImage = async ({ pImage }) => {\r\n  /* Most important part for uploading multiple image  */\r\n  let formData = new FormData();\r\n  for (const file of pImage) {\r\n    formData.append(\"pImage\", file);\r\n  }\r\n  /* Most important part for uploading multiple image  */\r\n};\r\n\r\nexport const createProduct = async ({\r\n  pName,\r\n  pDescription,\r\n  pImage,\r\n  pStatus,\r\n  pCategory,\r\n  pQuantity,\r\n  pPrice,\r\n  pOffer,\r\n}) => {\r\n  /* Most important part for uploading multiple image  */\r\n  let formData = new FormData();\r\n  for (const file of pImage) {\r\n    formData.append(\"pImage\", file);\r\n  }\r\n  /* Most important part for uploading multiple image  */\r\n  formData.append(\"pName\", pName);\r\n  formData.append(\"pDescription\", pDescription);\r\n  formData.append(\"pStatus\", pStatus);\r\n  formData.append(\"pCategory\", pCategory);\r\n  formData.append(\"pQuantity\", pQuantity);\r\n  formData.append(\"pPrice\", pPrice);\r\n  formData.append(\"pOffer\", pOffer);\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/add-product`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const editProduct = async (product) => {\r\n  console.log(product);\r\n  /* Most important part for updating multiple image  */\r\n  let formData = new FormData();\r\n  if (product.pEditImages) {\r\n    for (const file of product.pEditImages) {\r\n      formData.append(\"pEditImages\", file);\r\n    }\r\n  }\r\n  /* Most important part for updating multiple image  */\r\n  formData.append(\"pId\", product.pId);\r\n  formData.append(\"pName\", product.pName);\r\n  formData.append(\"pDescription\", product.pDescription);\r\n  formData.append(\"pStatus\", product.pStatus);\r\n  formData.append(\"pCategory\", product.pCategory._id);\r\n  formData.append(\"pQuantity\", product.pQuantity);\r\n  formData.append(\"pPrice\", product.pPrice);\r\n  formData.append(\"pOffer\", product.pOffer);\r\n  formData.append(\"pImages\", product.pImages);\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteProduct = async (pId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/delete-product`, { pId });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const productByCategory = async (catId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\r\n      catId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const productByPrice = async (price) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\r\n      price,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,2BAA2B,CAAC;IAC/D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,uBAAuB,GAAG,MAAAA,CAAA,KAAY;EACjD,IAAI;IACF,IAAIN,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,0BAA0B,CAAC;IAC9D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAG,MAAAA,CAAO;EAAEC;AAAO,CAAC,KAAK;EACtD;EACA,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;EACjC;EACA;AACF,CAAC;AAED,OAAO,MAAME,aAAa,GAAG,MAAAA,CAAO;EAClCC,KAAK;EACLC,YAAY;EACZP,MAAM;EACNQ,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ;EACA,IAAIX,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;EACjC;EACA;EACAF,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEE,KAAK,CAAC;EAC/BL,QAAQ,CAACG,MAAM,CAAC,cAAc,EAAEG,YAAY,CAAC;EAC7CN,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEI,OAAO,CAAC;EACnCP,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEK,SAAS,CAAC;EACvCR,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEM,SAAS,CAAC;EACvCT,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEO,MAAM,CAAC;EACjCV,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEQ,MAAM,CAAC;EAEjC,IAAI;IACF,IAAIpB,GAAG,GAAG,MAAMN,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,MAAM,0BAA0B,EAAEc,QAAQ,CAAC;IACzE,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMmB,WAAW,GAAG,MAAOC,OAAO,IAAK;EAC5CnB,OAAO,CAACC,GAAG,CAACkB,OAAO,CAAC;EACpB;EACA,IAAId,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,IAAIa,OAAO,CAACC,WAAW,EAAE;IACvB,KAAK,MAAMb,IAAI,IAAIY,OAAO,CAACC,WAAW,EAAE;MACtCf,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAED,IAAI,CAAC;IACtC;EACF;EACA;EACAF,QAAQ,CAACG,MAAM,CAAC,KAAK,EAAEW,OAAO,CAACE,GAAG,CAAC;EACnChB,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEW,OAAO,CAACT,KAAK,CAAC;EACvCL,QAAQ,CAACG,MAAM,CAAC,cAAc,EAAEW,OAAO,CAACR,YAAY,CAAC;EACrDN,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEW,OAAO,CAACP,OAAO,CAAC;EAC3CP,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEW,OAAO,CAACN,SAAS,CAACS,GAAG,CAAC;EACnDjB,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEW,OAAO,CAACL,SAAS,CAAC;EAC/CT,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEW,OAAO,CAACJ,MAAM,CAAC;EACzCV,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEW,OAAO,CAACH,MAAM,CAAC;EACzCX,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEW,OAAO,CAACI,OAAO,CAAC;EAE3C,IAAI;IACF,IAAI3B,GAAG,GAAG,MAAMN,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,MAAM,2BAA2B,EAAEc,QAAQ,CAAC;IAC1E,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMyB,aAAa,GAAG,MAAOH,GAAG,IAAK;EAC1C,IAAI;IACF,IAAIzB,GAAG,GAAG,MAAMN,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,MAAM,6BAA6B,EAAE;MAAE8B;IAAI,CAAC,CAAC;IAC3E,OAAOzB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAM0B,iBAAiB,GAAG,MAAOC,KAAK,IAAK;EAChD,IAAI;IACF,IAAI9B,GAAG,GAAG,MAAMN,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,MAAM,kCAAkC,EAAE;MACtEmC;IACF,CAAC,CAAC;IACF,OAAO9B,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAM4B,cAAc,GAAG,MAAOC,KAAK,IAAK;EAC7C,IAAI;IACF,IAAIhC,GAAG,GAAG,MAAMN,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,MAAM,+BAA+B,EAAE;MACnEqC;IACF,CAAC,CAAC;IACF,OAAOhC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}