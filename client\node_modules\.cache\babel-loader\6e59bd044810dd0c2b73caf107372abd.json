{"ast": null, "code": "import { getUserById, updatePersonalInformationFetch, getOrderByUser, updatePassword } from \"./FetchApi\";\nexport const logout = () => {\n  localStorage.removeItem(\"jwt\");\n  localStorage.removeItem(\"cart\");\n  localStorage.removeItem(\"wishList\");\n  window.location.href = \"/\";\n};\nexport const fetchData = async dispatch => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  let userId = JSON.parse(localStorage.getItem(\"jwt\")) ? JSON.parse(localStorage.getItem(\"jwt\")).user._id : \"\";\n  try {\n    let responseData = await getUserById(userId);\n    setTimeout(() => {\n      if (responseData && responseData.User) {\n        dispatch({\n          type: \"userDetails\",\n          payload: responseData.User\n        });\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n      }\n    }, 500);\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const fetchOrderByUser = async dispatch => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  let userId = JSON.parse(localStorage.getItem(\"jwt\")) ? JSON.parse(localStorage.getItem(\"jwt\")).user._id : \"\";\n  try {\n    let responseData = await getOrderByUser(userId);\n    setTimeout(() => {\n      if (responseData && responseData.Order) {\n        console.log(responseData);\n        dispatch({\n          type: \"OrderByUser\",\n          payload: responseData.Order\n        });\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n      }\n    }, 500);\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const updatePersonalInformationAction = async (dispatch, fData) => {\n  const formData = {\n    uId: fData.id,\n    name: fData.name,\n    phoneNumber: fData.phone\n  };\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  try {\n    let responseData = await updatePersonalInformationFetch(formData);\n    setTimeout(() => {\n      if (responseData && responseData.success) {\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n        fetchData(dispatch);\n      }\n    }, 500);\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const handleChangePassword = async (fData, setFdata, dispatch) => {\n  if (!fData.newPassword || !fData.oldPassword || !fData.confirmPassword) {\n    setFdata({\n      ...fData,\n      error: \"Please provide your all password and a new password\"\n    });\n  } else if (fData.newPassword !== fData.confirmPassword) {\n    setFdata({\n      ...fData,\n      error: \"Password does't match\"\n    });\n  } else {\n    const formData = {\n      uId: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\n      oldPassword: fData.oldPassword,\n      newPassword: fData.newPassword\n    };\n    dispatch({\n      type: \"loading\",\n      payload: true\n    });\n    try {\n      let responseData = await updatePassword(formData);\n      if (responseData && responseData.success) {\n        setFdata({\n          ...fData,\n          success: responseData.success,\n          error: \"\",\n          oldPassword: \"\",\n          newPassword: \"\",\n          confirmPassword: \"\"\n        });\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n      } else if (responseData.error) {\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n        setFdata({\n          ...fData,\n          error: responseData.error,\n          success: \"\",\n          oldPassword: \"\",\n          newPassword: \"\",\n          confirmPassword: \"\"\n        });\n      }\n    } catch (error) {\n      console.log(error);\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserById", "updatePersonalInformationFetch", "getOrderByUser", "updatePassword", "logout", "localStorage", "removeItem", "window", "location", "href", "fetchData", "dispatch", "type", "payload", "userId", "JSON", "parse", "getItem", "user", "_id", "responseData", "setTimeout", "User", "error", "console", "log", "fetchOrderByUser", "Order", "updatePersonalInformationAction", "fData", "formData", "uId", "id", "name", "phoneNumber", "phone", "success", "handleChangePassword", "setFdata", "newPassword", "oldPassword", "confirmPassword"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/Action.js"], "sourcesContent": ["import {\r\n  getUserById,\r\n  updatePersonalInformationFetch,\r\n  getOrderByUser,\r\n  updatePassword,\r\n} from \"./FetchApi\";\r\n\r\nexport const logout = () => {\r\n  localStorage.removeItem(\"jwt\");\r\n  localStorage.removeItem(\"cart\");\r\n  localStorage.removeItem(\"wishList\");\r\n  window.location.href = \"/\";\r\n};\r\n\r\nexport const fetchData = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let userId = JSON.parse(localStorage.getItem(\"jwt\"))\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).user._id\r\n    : \"\";\r\n  try {\r\n    let responseData = await getUserById(userId);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.User) {\r\n        dispatch({ type: \"userDetails\", payload: responseData.User });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const fetchOrderByUser = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let userId = JSON.parse(localStorage.getItem(\"jwt\"))\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).user._id\r\n    : \"\";\r\n  try {\r\n    let responseData = await getOrderByUser(userId);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.Order) {\r\n        console.log(responseData);\r\n        dispatch({ type: \"OrderByUser\", payload: responseData.Order });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const updatePersonalInformationAction = async (dispatch, fData) => {\r\n  const formData = {\r\n    uId: fData.id,\r\n    name: fData.name,\r\n    phoneNumber: fData.phone,\r\n  };\r\n  dispatch({ type: \"loading\", payload: true });\r\n  try {\r\n    let responseData = await updatePersonalInformationFetch(formData);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.success) {\r\n        dispatch({ type: \"loading\", payload: false });\r\n        fetchData(dispatch);\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const handleChangePassword = async (fData, setFdata, dispatch) => {\r\n  if (!fData.newPassword || !fData.oldPassword || !fData.confirmPassword) {\r\n    setFdata({\r\n      ...fData,\r\n      error: \"Please provide your all password and a new password\",\r\n    });\r\n  } else if (fData.newPassword !== fData.confirmPassword) {\r\n    setFdata({ ...fData, error: \"Password does't match\" });\r\n  } else {\r\n    const formData = {\r\n      uId: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\r\n      oldPassword: fData.oldPassword,\r\n      newPassword: fData.newPassword,\r\n    };\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      let responseData = await updatePassword(formData);\r\n      if (responseData && responseData.success) {\r\n        setFdata({\r\n          ...fData,\r\n          success: responseData.success,\r\n          error: \"\",\r\n          oldPassword: \"\",\r\n          newPassword: \"\",\r\n          confirmPassword: \"\",\r\n        });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      } else if (responseData.error) {\r\n        dispatch({ type: \"loading\", payload: false });\r\n        setFdata({\r\n          ...fData,\r\n          error: responseData.error,\r\n          success: \"\",\r\n          oldPassword: \"\",\r\n          newPassword: \"\",\r\n          confirmPassword: \"\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  }\r\n};\r\n"], "mappings": "AAAA,SACEA,WAAW,EACXC,8BAA8B,EAC9BC,cAAc,EACdC,cAAc,QACT,YAAY;AAEnB,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAC1BC,YAAY,CAACC,UAAU,CAAC,KAAK,CAAC;EAC9BD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;EAC/BD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;EACnCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;AAC5B,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG,MAAOC,QAAQ,IAAK;EAC3CA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACX,YAAY,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC,GAChDF,IAAI,CAACC,KAAK,CAACX,YAAY,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,GAChD,EAAE;EACN,IAAI;IACF,IAAIC,YAAY,GAAG,MAAMpB,WAAW,CAACc,MAAM,CAAC;IAC5CO,UAAU,CAAC,MAAM;MACf,IAAID,YAAY,IAAIA,YAAY,CAACE,IAAI,EAAE;QACrCX,QAAQ,CAAC;UAAEC,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEO,YAAY,CAACE;QAAK,CAAC,CAAC;QAC7DX,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC/C;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC,OAAOU,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAG,MAAOf,QAAQ,IAAK;EAClDA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACX,YAAY,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC,GAChDF,IAAI,CAACC,KAAK,CAACX,YAAY,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,GAChD,EAAE;EACN,IAAI;IACF,IAAIC,YAAY,GAAG,MAAMlB,cAAc,CAACY,MAAM,CAAC;IAC/CO,UAAU,CAAC,MAAM;MACf,IAAID,YAAY,IAAIA,YAAY,CAACO,KAAK,EAAE;QACtCH,OAAO,CAACC,GAAG,CAACL,YAAY,CAAC;QACzBT,QAAQ,CAAC;UAAEC,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEO,YAAY,CAACO;QAAM,CAAC,CAAC;QAC9DhB,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC/C;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC,OAAOU,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,+BAA+B,GAAG,MAAAA,CAAOjB,QAAQ,EAAEkB,KAAK,KAAK;EACxE,MAAMC,QAAQ,GAAG;IACfC,GAAG,EAAEF,KAAK,CAACG,EAAE;IACbC,IAAI,EAAEJ,KAAK,CAACI,IAAI;IAChBC,WAAW,EAAEL,KAAK,CAACM;EACrB,CAAC;EACDxB,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAI;IACF,IAAIO,YAAY,GAAG,MAAMnB,8BAA8B,CAAC6B,QAAQ,CAAC;IACjET,UAAU,CAAC,MAAM;MACf,IAAID,YAAY,IAAIA,YAAY,CAACgB,OAAO,EAAE;QACxCzB,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAC7CH,SAAS,CAACC,QAAQ,CAAC;MACrB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMc,oBAAoB,GAAG,MAAAA,CAAOR,KAAK,EAAES,QAAQ,EAAE3B,QAAQ,KAAK;EACvE,IAAI,CAACkB,KAAK,CAACU,WAAW,IAAI,CAACV,KAAK,CAACW,WAAW,IAAI,CAACX,KAAK,CAACY,eAAe,EAAE;IACtEH,QAAQ,CAAC;MACP,GAAGT,KAAK;MACRN,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIM,KAAK,CAACU,WAAW,KAAKV,KAAK,CAACY,eAAe,EAAE;IACtDH,QAAQ,CAAC;MAAE,GAAGT,KAAK;MAAEN,KAAK,EAAE;IAAwB,CAAC,CAAC;EACxD,CAAC,MAAM;IACL,MAAMO,QAAQ,GAAG;MACfC,GAAG,EAAEhB,IAAI,CAACC,KAAK,CAACX,YAAY,CAACY,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG;MACrDqB,WAAW,EAAEX,KAAK,CAACW,WAAW;MAC9BD,WAAW,EAAEV,KAAK,CAACU;IACrB,CAAC;IACD5B,QAAQ,CAAC;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5C,IAAI;MACF,IAAIO,YAAY,GAAG,MAAMjB,cAAc,CAAC2B,QAAQ,CAAC;MACjD,IAAIV,YAAY,IAAIA,YAAY,CAACgB,OAAO,EAAE;QACxCE,QAAQ,CAAC;UACP,GAAGT,KAAK;UACRO,OAAO,EAAEhB,YAAY,CAACgB,OAAO;UAC7Bb,KAAK,EAAE,EAAE;UACTiB,WAAW,EAAE,EAAE;UACfD,WAAW,EAAE,EAAE;UACfE,eAAe,EAAE;QACnB,CAAC,CAAC;QACF9B,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIO,YAAY,CAACG,KAAK,EAAE;QAC7BZ,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAC7CyB,QAAQ,CAAC;UACP,GAAGT,KAAK;UACRN,KAAK,EAAEH,YAAY,CAACG,KAAK;UACzBa,OAAO,EAAE,EAAE;UACXI,WAAW,EAAE,EAAE;UACfD,WAAW,EAAE,EAAE;UACfE,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}