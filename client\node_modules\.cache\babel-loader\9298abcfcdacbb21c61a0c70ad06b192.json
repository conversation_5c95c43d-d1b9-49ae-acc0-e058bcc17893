{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\home\\\\Slider.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useEffect, useContext, useState } from \"react\";\nimport OrderSuccessMessage from \"./OrderSuccessMessage\";\nimport { HomeContext } from \"./\";\nimport { sliderImages } from \"../../admin/dashboardAdmin/Action\";\nimport { prevSlide, nextSlide } from \"./Mixins\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst Slider = props => {\n  _s();\n  var _data$sliderImages;\n  const {\n    data,\n    dispatch\n  } = useContext(HomeContext);\n  const [slide, setSlide] = useState(0);\n  useEffect(() => {\n    sliderImages(dispatch);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative mt-16 bg-gray-100 border-2\",\n      children: [data.sliderImages.length > 0 ? /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"w-full\",\n        src: `${apiURL}/uploads/customize/${data.sliderImages[slide].slideImage}`,\n        alt: \"sliderImage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this) : \"\", (data === null || data === void 0 ? void 0 : (_data$sliderImages = data.sliderImages) === null || _data$sliderImages === void 0 ? void 0 : _data$sliderImages.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: e => prevSlide(data.sliderImages.length, slide, setSlide),\n          className: `z-10 absolute top-0 left-0 mt-64 flex justify-end items-center box-border flex justify-center w-12 h-12 text-gray-700  cursor-pointer hover:text-yellow-700`,\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: e => nextSlide(data.sliderImages.length, slide, setSlide),\n          className: `z-10 absolute top-0 right-0 mt-64 flex justify-start items-center box-border flex justify-center w-12 h-12 text-gray-700 cursor-pointer hover:text-yellow-700`,\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 5l7 7-7 7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#shop\",\n            style: {\n              background: \"#303031\"\n            },\n            className: \"cursor-pointer box-border text-2xl text-white px-4 py-2 rounded\",\n            children: \"Shop Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OrderSuccessMessage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(Slider, \"aRwCCc4efmh+qXDvFDTn4gkFYtU=\");\n_c = Slider;\nexport default Slider;\nvar _c;\n$RefreshReg$(_c, \"Slider\");", "map": {"version": 3, "names": ["React", "Fragment", "useEffect", "useContext", "useState", "OrderSuccessMessage", "HomeContext", "sliderImages", "prevSlide", "nextSlide", "jsxDEV", "_jsxDEV", "_Fragment", "apiURL", "process", "env", "REACT_APP_API_URL", "Slide<PERSON>", "props", "_s", "_data$sliderImages", "data", "dispatch", "slide", "setSlide", "children", "className", "length", "src", "slideImage", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "e", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "href", "style", "background", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["import React, { Fragment, useEffect, useContext, useState } from \"react\";\r\nimport OrderSuccessMessage from \"./OrderSuccessMessage\";\r\nimport { HomeContext } from \"./\";\r\nimport { sliderImages } from \"../../admin/dashboardAdmin/Action\";\r\nimport { prevSlide, nextSlide } from \"./Mixins\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst Slider = (props) => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const [slide, setSlide] = useState(0);\r\n\r\n  useEffect(() => {\r\n    sliderImages(dispatch);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  return (\r\n    <Fragment>\r\n      <div className=\"relative mt-16 bg-gray-100 border-2\">\r\n        {data.sliderImages.length > 0 ? (\r\n          <img\r\n            className=\"w-full\"\r\n            src={`${apiURL}/uploads/customize/${data.sliderImages[slide].slideImage}`}\r\n            alt=\"sliderImage\"\r\n          />\r\n        ) : (\r\n          \"\"\r\n        )}\r\n\r\n        {data?.sliderImages?.length > 0 ? (\r\n          <>\r\n            <svg\r\n              onClick={(e) =>\r\n                prevSlide(data.sliderImages.length, slide, setSlide)\r\n              }\r\n              className={`z-10 absolute top-0 left-0 mt-64 flex justify-end items-center box-border flex justify-center w-12 h-12 text-gray-700  cursor-pointer hover:text-yellow-700`}\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M15 19l-7-7 7-7\"\r\n              />\r\n            </svg>\r\n            <svg\r\n              onClick={(e) =>\r\n                nextSlide(data.sliderImages.length, slide, setSlide)\r\n              }\r\n              className={`z-10 absolute top-0 right-0 mt-64 flex justify-start items-center box-border flex justify-center w-12 h-12 text-gray-700 cursor-pointer hover:text-yellow-700`}\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M9 5l7 7-7 7\"\r\n              />\r\n            </svg>\r\n            <div className=\"absolute inset-0 flex items-center justify-center\">\r\n              <a\r\n                href=\"#shop\"\r\n                style={{ background: \"#303031\" }}\r\n                className=\"cursor-pointer box-border text-2xl text-white px-4 py-2 rounded\"\r\n              >\r\n                Shop Now\r\n              </a>\r\n            </div>\r\n          </>\r\n        ) : null}\r\n      </div>\r\n      <OrderSuccessMessage />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Slider;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACxE,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,WAAW,QAAQ,IAAI;AAChC,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,SAAS,EAAEC,SAAS,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAV,QAAA,IAAAW,SAAA;AAEhD,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,MAAM,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGnB,UAAU,CAACG,WAAW,CAAC;EAClD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAErCF,SAAS,CAAC,MAAM;IACdK,YAAY,CAACe,QAAQ,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEX,OAAA,CAACV,QAAQ;IAAAwB,QAAA,gBACPd,OAAA;MAAKe,SAAS,EAAC,qCAAqC;MAAAD,QAAA,GACjDJ,IAAI,CAACd,YAAY,CAACoB,MAAM,GAAG,CAAC,gBAC3BhB,OAAA;QACEe,SAAS,EAAC,QAAQ;QAClBE,GAAG,EAAE,GAAGf,MAAM,sBAAsBQ,IAAI,CAACd,YAAY,CAACgB,KAAK,CAAC,CAACM,UAAU,EAAG;QAC1EC,GAAG,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,GAEF,EACD,EAEA,CAAAb,IAAI,aAAJA,IAAI,wBAAAD,kBAAA,GAAJC,IAAI,CAAEd,YAAY,cAAAa,kBAAA,uBAAlBA,kBAAA,CAAoBO,MAAM,IAAG,CAAC,gBAC7BhB,OAAA,CAAAC,SAAA;QAAAa,QAAA,gBACEd,OAAA;UACEwB,OAAO,EAAGC,CAAC,IACT5B,SAAS,CAACa,IAAI,CAACd,YAAY,CAACoB,MAAM,EAAEJ,KAAK,EAAEC,QAAQ,CACpD;UACDE,SAAS,EAAE,6JAA8J;UACzKW,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,cAAc;UACrBC,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,4BAA4B;UAAAf,QAAA,eAElCd,OAAA;YACE8B,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBC,WAAW,EAAE,CAAE;YACfC,CAAC,EAAC;UAAiB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvB,OAAA;UACEwB,OAAO,EAAGC,CAAC,IACT3B,SAAS,CAACY,IAAI,CAACd,YAAY,CAACoB,MAAM,EAAEJ,KAAK,EAAEC,QAAQ,CACpD;UACDE,SAAS,EAAE,+JAAgK;UAC3KW,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,cAAc;UACrBC,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,4BAA4B;UAAAf,QAAA,eAElCd,OAAA;YACE8B,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBC,WAAW,EAAE,CAAE;YACfC,CAAC,EAAC;UAAc;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvB,OAAA;UAAKe,SAAS,EAAC,mDAAmD;UAAAD,QAAA,eAChEd,OAAA;YACEkC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCrB,SAAS,EAAC,iEAAiE;YAAAD,QAAA,EAC5E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,eACN,CAAC,GACD,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNvB,OAAA,CAACN,mBAAmB;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEf,CAAC;AAACf,EAAA,CAzEIF,MAAM;AAAA+B,EAAA,GAAN/B,MAAM;AA2EZ,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}