{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\DebugProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getAllProduct } from './admin/products/FetchApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DebugProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    console.log('🚀 DebugProducts: Starting fetch...');\n    setLoading(true);\n    setError(null);\n    try {\n      console.log('📡 DebugProducts: Calling getAllProduct...');\n      const data = await getAllProduct();\n      console.log('✅ DebugProducts: Got data:', data);\n      setProducts(data || []);\n    } catch (err) {\n      console.error('❌ DebugProducts: Error:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-2xl font-bold mb-4\",\n      children: \"\\uD83D\\uDD27 Debug Products Component\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchProducts,\n        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n        disabled: loading,\n        children: loading ? 'Loading...' : 'Refresh Products'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-4 bg-gray-100 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"font-semibold\",\n        children: \"Debug Info:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"API URL:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 12\n        }, this), \" \", process.env.REACT_APP_API_URL]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Products Count:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 12\n        }, this), \" \", products.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Loading:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 12\n        }, this), \" \", loading ? 'Yes' : 'No']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Error:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 12\n        }, this), \" \", error || 'None']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-semibold\",\n        children: \"Error:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded\",\n      children: \"Loading products...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n      children: products.map((product, index) => {\n        var _product$price;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-lg p-4 shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-lg mb-2\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-2\",\n            children: product.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-600 font-semibold mb-2\",\n            children: [(_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toLocaleString('vi-VN'), \" VND\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), product.images && product.images.length > 0 && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: product.images[0],\n            alt: product.name,\n            className: \"w-full h-32 object-cover mt-2 rounded\",\n            onError: e => {\n              e.target.src = '/placeholder-image.jpg';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)]\n        }, product.productId || index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), products.length === 0 && !loading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-500 py-8\",\n      children: \"No products found. Check console for debug logs.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugProducts, \"uC3AVzNvroqWig/cDvwe3iDm/Dc=\");\n_c = DebugProducts;\nexport default DebugProducts;\nvar _c;\n$RefreshReg$(_c, \"DebugProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getAllProduct", "jsxDEV", "_jsxDEV", "DebugProducts", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "console", "log", "data", "err", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "process", "env", "REACT_APP_API_URL", "length", "map", "product", "index", "_product$price", "name", "category", "price", "toLocaleString", "description", "images", "src", "alt", "onError", "e", "target", "productId", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/DebugProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getAllProduct } from './admin/products/FetchApi';\n\nconst DebugProducts = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    console.log('🚀 DebugProducts: Starting fetch...');\n    setLoading(true);\n    setError(null);\n    \n    try {\n      console.log('📡 DebugProducts: Calling getAllProduct...');\n      const data = await getAllProduct();\n      console.log('✅ DebugProducts: Got data:', data);\n      setProducts(data || []);\n    } catch (err) {\n      console.error('❌ DebugProducts: Error:', err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-4\">🔧 Debug Products Component</h1>\n      \n      <div className=\"mb-4\">\n        <button \n          onClick={fetchProducts}\n          className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          disabled={loading}\n        >\n          {loading ? 'Loading...' : 'Refresh Products'}\n        </button>\n      </div>\n\n      <div className=\"mb-4 p-4 bg-gray-100 rounded\">\n        <h2 className=\"font-semibold\">Debug Info:</h2>\n        <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL}</p>\n        <p><strong>Products Count:</strong> {products.length}</p>\n        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>\n        <p><strong>Error:</strong> {error || 'None'}</p>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded\">\n          <h3 className=\"font-semibold\">Error:</h3>\n          <p>{error}</p>\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded\">\n          Loading products...\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {products.map((product, index) => (\n          <div key={product.productId || index} className=\"border rounded-lg p-4 shadow\">\n            <h3 className=\"font-semibold text-lg mb-2\">{product.name}</h3>\n            <p className=\"text-gray-600 mb-2\">{product.category}</p>\n            <p className=\"text-green-600 font-semibold mb-2\">\n              {product.price?.toLocaleString('vi-VN')} VND\n            </p>\n            <p className=\"text-sm text-gray-500\">{product.description}</p>\n            {product.images && product.images.length > 0 && (\n              <img \n                src={product.images[0]} \n                alt={product.name}\n                className=\"w-full h-32 object-cover mt-2 rounded\"\n                onError={(e) => {\n                  e.target.src = '/placeholder-image.jpg';\n                }}\n              />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {products.length === 0 && !loading && !error && (\n        <div className=\"text-center text-gray-500 py-8\">\n          No products found. Check console for debug logs.\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DebugProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,MAAMC,IAAI,GAAG,MAAMd,aAAa,CAAC,CAAC;MAClCY,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,IAAI,CAAC;MAC/CR,WAAW,CAACQ,IAAI,IAAI,EAAE,CAAC;IACzB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZH,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAEM,GAAG,CAAC;MAC7CL,QAAQ,CAACK,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA;IAAKe,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpChB,OAAA;MAAIe,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAExEpB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhB,OAAA;QACEqB,OAAO,EAAEZ,aAAc;QACvBM,SAAS,EAAC,4DAA4D;QACtEO,QAAQ,EAAEjB,OAAQ;QAAAW,QAAA,EAEjBX,OAAO,GAAG,YAAY,GAAG;MAAkB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3ChB,OAAA;QAAIe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9CpB,OAAA;QAAAgB,QAAA,gBAAGhB,OAAA;UAAAgB,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACG,OAAO,CAACC,GAAG,CAACC,iBAAiB;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChEpB,OAAA;QAAAgB,QAAA,gBAAGhB,OAAA;UAAAgB,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACjB,QAAQ,CAACuB,MAAM;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDpB,OAAA;QAAAgB,QAAA,gBAAGhB,OAAA;UAAAgB,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACf,OAAO,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDpB,OAAA;QAAAgB,QAAA,gBAAGhB,OAAA;UAAAgB,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACb,KAAK,IAAI,MAAM;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,EAELb,KAAK,iBACJP,OAAA;MAAKe,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7EhB,OAAA;QAAIe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzCpB,OAAA;QAAAgB,QAAA,EAAIT;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,EAEAf,OAAO,iBACNL,OAAA;MAAKe,SAAS,EAAC,mEAAmE;MAAAC,QAAA,EAAC;IAEnF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDpB,OAAA;MAAKe,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEb,QAAQ,CAACwB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;QAAA,IAAAC,cAAA;QAAA,oBAC3B9B,OAAA;UAAsCe,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC5EhB,OAAA;YAAIe,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEY,OAAO,CAACG;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9DpB,OAAA;YAAGe,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEY,OAAO,CAACI;UAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDpB,OAAA;YAAGe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,IAAAc,cAAA,GAC7CF,OAAO,CAACK,KAAK,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,cAAc,CAAC,OAAO,CAAC,EAAC,MAC1C;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpB,OAAA;YAAGe,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEY,OAAO,CAACO;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7DQ,OAAO,CAACQ,MAAM,IAAIR,OAAO,CAACQ,MAAM,CAACV,MAAM,GAAG,CAAC,iBAC1C1B,OAAA;YACEqC,GAAG,EAAET,OAAO,CAACQ,MAAM,CAAC,CAAC,CAAE;YACvBE,GAAG,EAAEV,OAAO,CAACG,IAAK;YAClBhB,SAAS,EAAC,uCAAuC;YACjDwB,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,wBAAwB;YACzC;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA,GAhBOQ,OAAO,CAACc,SAAS,IAAIb,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiB/B,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELjB,QAAQ,CAACuB,MAAM,KAAK,CAAC,IAAI,CAACrB,OAAO,IAAI,CAACE,KAAK,iBAC1CP,OAAA;MAAKe,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CA5FID,aAAa;AAAA0C,EAAA,GAAb1C,aAAa;AA8FnB,eAAeA,aAAa;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}