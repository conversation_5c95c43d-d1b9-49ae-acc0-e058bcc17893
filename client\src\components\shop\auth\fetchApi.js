import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const isAuthenticate = () =>
  localStorage.getItem("jwt") ? JSON.parse(localStorage.getItem("jwt")) : false;

export const isAdmin = () =>
  localStorage.getItem("jwt")
    ? JSON.parse(localStorage.getItem("jwt")).user.role === 1
    : false;

export const loginReq = async ({ email, password }) => {
  const data = { email, password };
  try {
    let res = await axios.post(`${apiURL}/api/v1/auth/login`, data);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const signupReq = async ({ name, email, password, cPassword }) => {
  const data = {
    fullName: name,
    email,
    password,
    confirmPassword: cPassword,
    role: "CUSTOMER" // Default role
  };
  try {
    let res = await axios.post(`${apiURL}/api/v1/auth/register`, data);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
