{"ast": null, "code": "import React__default, { forwardRef, useRef, useCallback, useLayoutEffect, useEffect, createElement, useState, Component, useContext } from 'react';\nimport { createPortal } from 'react-dom';\nimport clsx from 'clsx';\nimport Slide from '@mui/material/Slide';\nimport { styled, emphasize } from '@mui/material/styles';\nimport Collapse from '@mui/material/Collapse';\nimport SvgIcon from '@mui/material/SvgIcon';\nimport ClickAwayListener from '@mui/material/ClickAwayListener';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nvar SnackbarContext = /*#__PURE__*/React__default.createContext();\nvar allClasses = {\n  mui: {\n    root: {},\n    anchorOriginTopCenter: {},\n    anchorOriginBottomCenter: {},\n    anchorOriginTopRight: {},\n    anchorOriginBottomRight: {},\n    anchorOriginTopLeft: {},\n    anchorOriginBottomLeft: {}\n  },\n  container: {\n    containerRoot: {},\n    containerAnchorOriginTopCenter: {},\n    containerAnchorOriginBottomCenter: {},\n    containerAnchorOriginTopRight: {},\n    containerAnchorOriginBottomRight: {},\n    containerAnchorOriginTopLeft: {},\n    containerAnchorOriginBottomLeft: {}\n  }\n};\nvar MESSAGES = {\n  NO_PERSIST_ALL: 'WARNING - notistack: Reached maxSnack while all enqueued snackbars have \\'persist\\' flag. Notistack will dismiss the oldest snackbar anyway to allow other ones in the queue to be presented.'\n};\nvar SNACKBAR_INDENTS = {\n  view: {\n    \"default\": 20,\n    dense: 4\n  },\n  snackbar: {\n    \"default\": 6,\n    dense: 2\n  }\n};\nvar DEFAULTS = {\n  maxSnack: 3,\n  dense: false,\n  hideIconVariant: false,\n  variant: 'default',\n  autoHideDuration: 5000,\n  anchorOrigin: {\n    vertical: 'bottom',\n    horizontal: 'left'\n  },\n  TransitionComponent: Slide,\n  transitionDuration: {\n    enter: 225,\n    exit: 195\n  }\n};\nvar capitalise = function capitalise(text) {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n};\nvar originKeyExtractor = function originKeyExtractor(anchor) {\n  return \"\" + capitalise(anchor.vertical) + capitalise(anchor.horizontal);\n};\n/**\r\n * Omit SnackbarContainer class keys that are not needed for SnackbarItem\r\n */\n\nvar omitContainerKeys = function omitContainerKeys(classes) {\n  return (\n    // @ts-ignore\n    Object.keys(classes).filter(function (key) {\n      return !allClasses.container[key];\n    }).reduce(function (obj, key) {\n      var _extends2;\n      return _extends({}, obj, (_extends2 = {}, _extends2[key] = classes[key], _extends2));\n    }, {})\n  );\n};\nvar REASONS = {\n  TIMEOUT: 'timeout',\n  CLICKAWAY: 'clickaway',\n  MAXSNACK: 'maxsnack',\n  INSTRUCTED: 'instructed'\n};\n/** Tranforms classes name */\n\nvar transformer = {\n  toContainerAnchorOrigin: function toContainerAnchorOrigin(origin) {\n    return \"containerAnchorOrigin\" + origin;\n  },\n  toAnchorOrigin: function toAnchorOrigin(_ref) {\n    var vertical = _ref.vertical,\n      horizontal = _ref.horizontal;\n    return \"anchorOrigin\" + capitalise(vertical) + capitalise(horizontal);\n  },\n  toVariant: function toVariant(variant) {\n    return \"variant\" + capitalise(variant);\n  }\n};\nvar isDefined = function isDefined(value) {\n  return !!value || value === 0;\n};\nvar numberOrNull = function numberOrNull(numberish) {\n  return typeof numberish === 'number' || numberish === null;\n}; // @ts-ignore\n\nvar merge = function merge(options, props, defaults) {\n  return function (name) {\n    if (name === 'autoHideDuration') {\n      if (numberOrNull(options.autoHideDuration)) return options.autoHideDuration;\n      if (numberOrNull(props.autoHideDuration)) return props.autoHideDuration;\n      return DEFAULTS.autoHideDuration;\n    }\n    return options[name] || props[name] || defaults[name];\n  };\n};\nfunction objectMerge(options, props, defaults) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (props === void 0) {\n    props = {};\n  }\n  if (defaults === void 0) {\n    defaults = {};\n  }\n  return _extends({}, defaults, {}, props, {}, options);\n}\nvar componentName = 'SnackbarContent';\nvar classes = {\n  root: componentName + \"-root\"\n};\nvar Root = /*#__PURE__*/styled('div')(function (_ref) {\n  var _ref2, _ref3;\n  var theme = _ref.theme;\n  return _ref3 = {}, _ref3[\"&.\" + classes.root] = (_ref2 = {\n    display: 'flex',\n    flexWrap: 'wrap',\n    flexGrow: 1\n  }, _ref2[theme.breakpoints.up('sm')] = {\n    flexGrow: 'initial',\n    minWidth: 288\n  }, _ref2), _ref3;\n});\nvar SnackbarContent = /*#__PURE__*/forwardRef(function (_ref4, ref) {\n  var className = _ref4.className,\n    props = _objectWithoutPropertiesLoose(_ref4, [\"className\"]);\n  return React__default.createElement(Root, Object.assign({\n    ref: ref,\n    className: clsx(classes.root, className)\n  }, props));\n});\nvar DIRECTION = {\n  right: 'left',\n  left: 'right',\n  bottom: 'up',\n  top: 'down'\n};\nvar getTransitionDirection = function getTransitionDirection(anchorOrigin) {\n  if (anchorOrigin.horizontal !== 'center') {\n    return DIRECTION[anchorOrigin.horizontal];\n  }\n  return DIRECTION[anchorOrigin.vertical];\n};\nvar CheckIcon = function CheckIcon(props) {\n  return React__default.createElement(SvgIcon, Object.assign({}, props), React__default.createElement(\"path\", {\n    d: \"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z\"\n  }));\n};\nvar WarningIcon = function WarningIcon(props) {\n  return React__default.createElement(SvgIcon, Object.assign({}, props), React__default.createElement(\"path\", {\n    d: \"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z\"\n  }));\n};\nvar ErrorIcon = function ErrorIcon(props) {\n  return React__default.createElement(SvgIcon, Object.assign({}, props), React__default.createElement(\"path\", {\n    d: \"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z\"\n  }));\n};\nvar InfoIcon = function InfoIcon(props) {\n  return React__default.createElement(SvgIcon, Object.assign({}, props), React__default.createElement(\"path\", {\n    d: \"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\\n        0 22,12A10,10 0 0,0 12,2Z\"\n  }));\n};\nvar iconStyles = {\n  fontSize: 20,\n  marginInlineEnd: 8\n};\nvar defaultIconVariants = {\n  \"default\": undefined,\n  success: /*#__PURE__*/React__default.createElement(CheckIcon, {\n    style: iconStyles\n  }),\n  warning: /*#__PURE__*/React__default.createElement(WarningIcon, {\n    style: iconStyles\n  }),\n  error: /*#__PURE__*/React__default.createElement(ErrorIcon, {\n    style: iconStyles\n  }),\n  info: /*#__PURE__*/React__default.createElement(InfoIcon, {\n    style: iconStyles\n  })\n};\n\n/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/createChainedFunction.js\n */\nfunction createChainedFunction(funcs, extraArg) {\n  return funcs.reduce(function (acc, func) {\n    if (func == null) return acc;\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof func !== 'function') {\n        // eslint-disable-next-line no-console\n        console.error('Invalid Argument Type. must only provide functions, undefined, or null.');\n      }\n    }\n    return function chainedFunction() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var argums = [].concat(args);\n      if (extraArg && argums.indexOf(extraArg) === -1) {\n        argums.push(extraArg);\n      }\n      acc.apply(this, argums);\n      func.apply(this, argums);\n    };\n  }, function () {});\n}\n\n/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/useEventCallback.js\n */\nvar useEnhancedEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\nfunction useEventCallback(fn) {\n  var ref = useRef(fn);\n  useEnhancedEffect(function () {\n    ref.current = fn;\n  });\n  return useCallback(function () {\n    return ref.current.apply(void 0, arguments);\n  }, []);\n}\nvar Snackbar = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    autoHideDuration = props.autoHideDuration,\n    ClickAwayListenerProps = props.ClickAwayListenerProps,\n    _props$disableWindowB = props.disableWindowBlurListener,\n    disableWindowBlurListener = _props$disableWindowB === void 0 ? false : _props$disableWindowB,\n    onClose = props.onClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    open = props.open,\n    resumeHideDuration = props.resumeHideDuration,\n    other = _objectWithoutPropertiesLoose(props, [\"children\", \"autoHideDuration\", \"ClickAwayListenerProps\", \"disableWindowBlurListener\", \"onClose\", \"onMouseEnter\", \"onMouseLeave\", \"open\", \"resumeHideDuration\"]);\n  var timerAutoHide = useRef();\n  var handleClose = useEventCallback(function () {\n    if (onClose) {\n      onClose.apply(void 0, arguments);\n    }\n  });\n  var setAutoHideTimer = useEventCallback(function (autoHideDurationParam) {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    clearTimeout(timerAutoHide.current);\n    timerAutoHide.current = setTimeout(function () {\n      handleClose(null, REASONS.TIMEOUT);\n    }, autoHideDurationParam);\n  });\n  useEffect(function () {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return function () {\n      clearTimeout(timerAutoHide.current);\n    };\n  }, [open, autoHideDuration, setAutoHideTimer]);\n  /**\n   * Pause the timer when the user is interacting with the Snackbar\n   * or when the user hide the window.\n   */\n\n  var handlePause = function handlePause() {\n    clearTimeout(timerAutoHide.current);\n  };\n  /**\n   * Restart the timer when the user is no longer interacting with the Snackbar\n   * or when the window is shown back.\n   */\n\n  var handleResume = useCallback(function () {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  var handleMouseEnter = function handleMouseEnter(event) {\n    if (onMouseEnter) {\n      onMouseEnter(event);\n    }\n    handlePause();\n  };\n  var handleMouseLeave = function handleMouseLeave(event) {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    handleResume();\n  };\n  var handleClickAway = function handleClickAway(event) {\n    if (onClose) {\n      onClose(event, REASONS.CLICKAWAY);\n    }\n  };\n  useEffect(function () {\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return function () {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, handleResume, open]);\n  return createElement(ClickAwayListener, _extends({\n    onClickAway: handleClickAway\n  }, ClickAwayListenerProps), createElement(\"div\", _extends({\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    ref: ref\n  }, other), children));\n});\nvar componentName$1 = 'SnackbarItem';\nvar classes$1 = {\n  contentRoot: componentName$1 + \"-contentRoot\",\n  lessPadding: componentName$1 + \"-lessPadding\",\n  variantSuccess: componentName$1 + \"-variantSuccess\",\n  variantError: componentName$1 + \"-variantError\",\n  variantInfo: componentName$1 + \"-variantInfo\",\n  variantWarning: componentName$1 + \"-variantWarning\",\n  message: componentName$1 + \"-message\",\n  action: componentName$1 + \"-action\",\n  wrappedRoot: componentName$1 + \"-wrappedRoot\"\n};\nvar StyledSnackbar = /*#__PURE__*/styled(Snackbar)(function (_ref) {\n  var _ref2;\n  var theme = _ref.theme;\n  var mode = theme.palette.mode || theme.palette.type;\n  var backgroundColor = emphasize(theme.palette.background[\"default\"], mode === 'light' ? 0.8 : 0.98);\n  return _ref2 = {}, _ref2[\"&.\" + classes$1.wrappedRoot] = {\n    position: 'relative',\n    transform: 'translateX(0)',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, _ref2[\".\" + classes$1.contentRoot] = _extends({}, theme.typography.body2, {\n    backgroundColor: backgroundColor,\n    color: theme.palette.getContrastText(backgroundColor),\n    alignItems: 'center',\n    padding: '6px 16px',\n    borderRadius: '4px',\n    boxShadow: '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)'\n  }), _ref2[\".\" + classes$1.lessPadding] = {\n    paddingLeft: 8 * 2.5\n  }, _ref2[\".\" + classes$1.variantSuccess] = {\n    backgroundColor: '#43a047',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantError] = {\n    backgroundColor: '#d32f2f',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantInfo] = {\n    backgroundColor: '#2196f3',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.variantWarning] = {\n    backgroundColor: '#ff9800',\n    color: '#fff'\n  }, _ref2[\".\" + classes$1.message] = {\n    display: 'flex',\n    alignItems: 'center',\n    padding: '8px 0'\n  }, _ref2[\".\" + classes$1.action] = {\n    display: 'flex',\n    alignItems: 'center',\n    marginLeft: 'auto',\n    paddingLeft: 16,\n    marginRight: -8\n  }, _ref2;\n});\nvar SnackbarItem = function SnackbarItem(_ref3) {\n  var propClasses = _ref3.classes,\n    props = _objectWithoutPropertiesLoose(_ref3, [\"classes\"]);\n  var timeout = useRef();\n  var _useState = useState(true),\n    collapsed = _useState[0],\n    setCollapsed = _useState[1];\n  useEffect(function () {\n    return function () {\n      if (timeout.current) {\n        clearTimeout(timeout.current);\n      }\n    };\n  }, []);\n  var handleClose = createChainedFunction([props.snack.onClose, props.onClose], props.snack.key);\n  var handleEntered = function handleEntered() {\n    if (props.snack.requestClose) {\n      handleClose(null, REASONS.INSTRCUTED);\n    }\n  };\n  var handleExitedScreen = function handleExitedScreen() {\n    timeout.current = setTimeout(function () {\n      setCollapsed(!collapsed);\n    }, 125);\n  };\n  var style = props.style,\n    otherAriaAttributes = props.ariaAttributes,\n    otherClassName = props.className,\n    hideIconVariant = props.hideIconVariant,\n    iconVariant = props.iconVariant,\n    snack = props.snack,\n    otherAction = props.action,\n    otherContent = props.content,\n    otherTranComponent = props.TransitionComponent,\n    otherTranProps = props.TransitionProps,\n    otherTranDuration = props.transitionDuration,\n    other = _objectWithoutPropertiesLoose(props, [\"style\", \"dense\", \"ariaAttributes\", \"className\", \"hideIconVariant\", \"iconVariant\", \"snack\", \"action\", \"content\", \"TransitionComponent\", \"TransitionProps\", \"transitionDuration\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\"]);\n  var key = snack.key,\n    open = snack.open,\n    singleClassName = snack.className,\n    variant = snack.variant,\n    singleContent = snack.content,\n    singleAction = snack.action,\n    singleAriaAttributes = snack.ariaAttributes,\n    anchorOrigin = snack.anchorOrigin,\n    snackMessage = snack.message,\n    singleTranComponent = snack.TransitionComponent,\n    singleTranProps = snack.TransitionProps,\n    singleTranDuration = snack.transitionDuration,\n    singleSnackProps = _objectWithoutPropertiesLoose(snack, [\"persist\", \"key\", \"open\", \"entered\", \"requestClose\", \"className\", \"variant\", \"content\", \"action\", \"ariaAttributes\", \"anchorOrigin\", \"message\", \"TransitionComponent\", \"TransitionProps\", \"transitionDuration\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\"]);\n  var icon = _extends({}, defaultIconVariants, {}, iconVariant)[variant];\n  var ariaAttributes = _extends({\n    'aria-describedby': 'notistack-snackbar'\n  }, objectMerge(singleAriaAttributes, otherAriaAttributes));\n  var TransitionComponent = singleTranComponent || otherTranComponent || DEFAULTS.TransitionComponent;\n  var transitionDuration = objectMerge(singleTranDuration, otherTranDuration, DEFAULTS.transitionDuration);\n  var transitionProps = _extends({\n    direction: getTransitionDirection(anchorOrigin)\n  }, objectMerge(singleTranProps, otherTranProps));\n  var action = singleAction || otherAction;\n  if (typeof action === 'function') {\n    action = action(key);\n  }\n  var content = singleContent || otherContent;\n  if (typeof content === 'function') {\n    content = content(key, snack.message);\n  } // eslint-disable-next-line operator-linebreak\n\n  var callbacks = ['onEnter', 'onEntering', 'onEntered', 'onExit', 'onExiting', 'onExited'].reduce(function (acc, cbName) {\n    var _extends2;\n    return _extends({}, acc, (_extends2 = {}, _extends2[cbName] = createChainedFunction([props.snack[cbName], props[cbName]], props.snack.key), _extends2));\n  }, {});\n  return React__default.createElement(Collapse, {\n    unmountOnExit: true,\n    timeout: 175,\n    \"in\": collapsed,\n    onExited: callbacks.onExited\n  }, React__default.createElement(StyledSnackbar, Object.assign({}, other, singleSnackProps, {\n    open: open,\n    className: clsx(propClasses.root, classes$1.wrappedRoot, propClasses[transformer.toAnchorOrigin(anchorOrigin)]),\n    onClose: handleClose\n  }), React__default.createElement(TransitionComponent, Object.assign({\n    appear: true,\n    \"in\": open,\n    timeout: transitionDuration\n  }, transitionProps, {\n    onExit: callbacks.onExit,\n    onExiting: callbacks.onExiting,\n    onExited: handleExitedScreen,\n    onEnter: callbacks.onEnter,\n    onEntering: callbacks.onEntering,\n    // order matters. first callbacks.onEntered to set entered: true,\n    // then handleEntered to check if there's a request for closing\n    onEntered: createChainedFunction([callbacks.onEntered, handleEntered])\n  }), content || React__default.createElement(SnackbarContent, Object.assign({}, ariaAttributes, {\n    role: \"alert\",\n    style: style,\n    className: clsx(classes$1.contentRoot, classes$1[transformer.toVariant(variant)], propClasses[transformer.toVariant(variant)], otherClassName, singleClassName, !hideIconVariant && icon && classes$1.lessPadding)\n  }), React__default.createElement(\"div\", {\n    id: ariaAttributes['aria-describedby'],\n    className: classes$1.message\n  }, !hideIconVariant ? icon : null, snackMessage), action && React__default.createElement(\"div\", {\n    className: classes$1.action\n  }, action)))));\n};\nvar collapse = {\n  // Material-UI 4.12.x and above uses MuiCollapse-root; earlier versions use\n  // Mui-Collapse-container.  https://github.com/mui-org/material-ui/pull/24084\n  container: '& > .MuiCollapse-container, & > .MuiCollapse-root',\n  wrapper: '& > .MuiCollapse-container > .MuiCollapse-wrapper, & > .MuiCollapse-root > .MuiCollapse-wrapper'\n};\nvar xsWidthMargin = 16;\nvar componentName$2 = 'SnackbarContainer';\nvar classes$2 = {\n  root: componentName$2 + \"-root\",\n  rootDense: componentName$2 + \"-rootDense\",\n  top: componentName$2 + \"-top\",\n  bottom: componentName$2 + \"-bottom\",\n  left: componentName$2 + \"-left\",\n  right: componentName$2 + \"-right\",\n  center: componentName$2 + \"-center\"\n};\nvar Root$1 = /*#__PURE__*/styled('div')(function (_ref) {\n  var _ref2, _ref3, _ref4, _ref5, _ref6, _ref7;\n  var theme = _ref.theme;\n  return _ref7 = {}, _ref7[\"&.\" + classes$2.root] = (_ref2 = {\n    boxSizing: 'border-box',\n    display: 'flex',\n    maxHeight: '100%',\n    position: 'fixed',\n    zIndex: theme.zIndex.snackbar,\n    height: 'auto',\n    width: 'auto',\n    transition: 'top 300ms ease 0ms, right 300ms ease 0ms, bottom 300ms ease 0ms, left 300ms ease 0ms, margin 300ms ease 0ms, max-width 300ms ease 0ms',\n    // container itself is invisible and should not block clicks, clicks should be passed to its children\n    pointerEvents: 'none'\n  }, _ref2[collapse.container] = {\n    pointerEvents: 'all'\n  }, _ref2[collapse.wrapper] = {\n    padding: SNACKBAR_INDENTS.snackbar[\"default\"] + \"px 0px\",\n    transition: 'padding 300ms ease 0ms'\n  }, _ref2.maxWidth = \"calc(100% - \" + SNACKBAR_INDENTS.view[\"default\"] * 2 + \"px)\", _ref2[theme.breakpoints.down('sm')] = {\n    width: '100%',\n    maxWidth: \"calc(100% - \" + xsWidthMargin * 2 + \"px)\"\n  }, _ref2), _ref7[\"&.\" + classes$2.rootDense] = (_ref3 = {}, _ref3[collapse.wrapper] = {\n    padding: SNACKBAR_INDENTS.snackbar.dense + \"px 0px\"\n  }, _ref3), _ref7[\"&.\" + classes$2.top] = {\n    top: SNACKBAR_INDENTS.view[\"default\"] - SNACKBAR_INDENTS.snackbar[\"default\"],\n    flexDirection: 'column'\n  }, _ref7[\"&.\" + classes$2.bottom] = {\n    bottom: SNACKBAR_INDENTS.view[\"default\"] - SNACKBAR_INDENTS.snackbar[\"default\"],\n    flexDirection: 'column-reverse'\n  }, _ref7[\"&.\" + classes$2.left] = (_ref4 = {\n    left: SNACKBAR_INDENTS.view[\"default\"]\n  }, _ref4[theme.breakpoints.up('sm')] = {\n    alignItems: 'flex-start'\n  }, _ref4[theme.breakpoints.down('sm')] = {\n    left: xsWidthMargin + \"px\"\n  }, _ref4), _ref7[\"&.\" + classes$2.right] = (_ref5 = {\n    right: SNACKBAR_INDENTS.view[\"default\"]\n  }, _ref5[theme.breakpoints.up('sm')] = {\n    alignItems: 'flex-end'\n  }, _ref5[theme.breakpoints.down('sm')] = {\n    right: xsWidthMargin + \"px\"\n  }, _ref5), _ref7[\"&.\" + classes$2.center] = (_ref6 = {\n    left: '50%',\n    transform: 'translateX(-50%)'\n  }, _ref6[theme.breakpoints.up('sm')] = {\n    alignItems: 'center'\n  }, _ref6), _ref7;\n});\nvar SnackbarContainer = function SnackbarContainer(props) {\n  var className = props.className,\n    anchorOrigin = props.anchorOrigin,\n    dense = props.dense,\n    other = _objectWithoutPropertiesLoose(props, [\"className\", \"anchorOrigin\", \"dense\"]);\n  var combinedClassname = clsx(classes$2[anchorOrigin.vertical], classes$2[anchorOrigin.horizontal], classes$2.root,\n  // root should come after others to override maxWidth\n  className, dense && classes$2.rootDense);\n  return React__default.createElement(Root$1, Object.assign({\n    className: combinedClassname\n  }, other));\n};\nvar SnackbarContainer$1 = /*#__PURE__*/React__default.memo(SnackbarContainer);\n\n/* eslint-disable */\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar warning = function (message) {\n  if (!__DEV__) return;\n  if (typeof console !== 'undefined') {\n    console.error(message);\n  }\n  try {\n    throw new Error(message);\n  } catch (x) {}\n};\nvar SnackbarProvider = /*#__PURE__*/function (_Component) {\n  _inheritsLoose(SnackbarProvider, _Component);\n  function SnackbarProvider(props) {\n    var _this;\n    _this = _Component.call(this, props) || this;\n    /**\r\n     * Adds a new snackbar to the queue to be presented.\r\n     * Returns generated or user defined key referencing the new snackbar or null\r\n     */\n\n    _this.enqueueSnackbar = function (message, opts) {\n      if (opts === void 0) {\n        opts = {};\n      }\n      var _opts = opts,\n        key = _opts.key,\n        preventDuplicate = _opts.preventDuplicate,\n        options = _objectWithoutPropertiesLoose(_opts, [\"key\", \"preventDuplicate\"]);\n      var hasSpecifiedKey = isDefined(key);\n      var id = hasSpecifiedKey ? key : new Date().getTime() + Math.random();\n      var merger = merge(options, _this.props, DEFAULTS);\n      var snack = _extends({\n        key: id\n      }, options, {\n        message: message,\n        open: true,\n        entered: false,\n        requestClose: false,\n        variant: merger('variant'),\n        anchorOrigin: merger('anchorOrigin'),\n        autoHideDuration: merger('autoHideDuration')\n      });\n      if (options.persist) {\n        snack.autoHideDuration = undefined;\n      }\n      _this.setState(function (state) {\n        if (preventDuplicate === undefined && _this.props.preventDuplicate || preventDuplicate) {\n          var compareFunction = function compareFunction(item) {\n            return hasSpecifiedKey ? item.key === key : item.message === message;\n          };\n          var inQueue = state.queue.findIndex(compareFunction) > -1;\n          var inView = state.snacks.findIndex(compareFunction) > -1;\n          if (inQueue || inView) {\n            return state;\n          }\n        }\n        return _this.handleDisplaySnack(_extends({}, state, {\n          queue: [].concat(state.queue, [snack])\n        }));\n      });\n      return id;\n    };\n    /**\r\n     * Reducer: Display snack if there's space for it. Otherwise, immediately\r\n     * begin dismissing the oldest message to start showing the new one.\r\n     */\n\n    _this.handleDisplaySnack = function (state) {\n      var snacks = state.snacks;\n      if (snacks.length >= _this.maxSnack) {\n        return _this.handleDismissOldest(state);\n      }\n      return _this.processQueue(state);\n    };\n    /**\r\n     * Reducer: Display items (notifications) in the queue if there's space for them.\r\n     */\n\n    _this.processQueue = function (state) {\n      var queue = state.queue,\n        snacks = state.snacks;\n      if (queue.length > 0) {\n        return _extends({}, state, {\n          snacks: [].concat(snacks, [queue[0]]),\n          queue: queue.slice(1, queue.length)\n        });\n      }\n      return state;\n    };\n    /**\r\n     * Reducer: Hide oldest snackbar on the screen because there exists a new one which we have to display.\r\n     * (ignoring the one with 'persist' flag. i.e. explicitly told by user not to get dismissed).\r\n     *\r\n     * Note 1: If there is already a message leaving the screen, no new messages are dismissed.\r\n     * Note 2: If the oldest message has not yet entered the screen, only a request to close the\r\n     *         snackbar is made. Once it entered the screen, it will be immediately dismissed.\r\n     */\n\n    _this.handleDismissOldest = function (state) {\n      if (state.snacks.some(function (item) {\n        return !item.open || item.requestClose;\n      })) {\n        return state;\n      }\n      var popped = false;\n      var ignore = false;\n      var persistentCount = state.snacks.reduce(function (acc, current) {\n        return acc + (current.open && current.persist ? 1 : 0);\n      }, 0);\n      if (persistentCount === _this.maxSnack) {\n        process.env.NODE_ENV !== \"production\" ? warning(MESSAGES.NO_PERSIST_ALL) : void 0;\n        ignore = true;\n      }\n      var snacks = state.snacks.map(function (item) {\n        if (!popped && (!item.persist || ignore)) {\n          popped = true;\n          if (!item.entered) {\n            return _extends({}, item, {\n              requestClose: true\n            });\n          }\n          if (item.onClose) item.onClose(null, REASONS.MAXSNACK, item.key);\n          if (_this.props.onClose) _this.props.onClose(null, REASONS.MAXSNACK, item.key);\n          return _extends({}, item, {\n            open: false\n          });\n        }\n        return _extends({}, item);\n      });\n      return _extends({}, state, {\n        snacks: snacks\n      });\n    };\n    /**\r\n     * Set the entered state of the snackbar with the given key.\r\n     */\n\n    _this.handleEnteredSnack = function (node, isAppearing, key) {\n      if (!isDefined(key)) {\n        throw new Error('handleEnteredSnack Cannot be called with undefined key');\n      }\n      _this.setState(function (_ref) {\n        var snacks = _ref.snacks;\n        return {\n          snacks: snacks.map(function (item) {\n            return item.key === key ? _extends({}, item, {\n              entered: true\n            }) : _extends({}, item);\n          })\n        };\n      });\n    };\n    /**\r\n     * Hide a snackbar after its timeout.\r\n     */\n\n    _this.handleCloseSnack = function (event, reason, key) {\n      // should not use createChainedFunction for onClose.\n      // because this.closeSnackbar called this function\n      if (_this.props.onClose) {\n        _this.props.onClose(event, reason, key);\n      }\n      if (reason === REASONS.CLICKAWAY) return;\n      var shouldCloseAll = key === undefined;\n      _this.setState(function (_ref2) {\n        var snacks = _ref2.snacks,\n          queue = _ref2.queue;\n        return {\n          snacks: snacks.map(function (item) {\n            if (!shouldCloseAll && item.key !== key) {\n              return _extends({}, item);\n            }\n            return item.entered ? _extends({}, item, {\n              open: false\n            }) : _extends({}, item, {\n              requestClose: true\n            });\n          }),\n          queue: queue.filter(function (item) {\n            return item.key !== key;\n          })\n        };\n      });\n    };\n    /**\r\n     * Close snackbar with the given key\r\n     */\n\n    _this.closeSnackbar = function (key) {\n      // call individual snackbar onClose callback passed through options parameter\n      var toBeClosed = _this.state.snacks.find(function (item) {\n        return item.key === key;\n      });\n      if (isDefined(key) && toBeClosed && toBeClosed.onClose) {\n        toBeClosed.onClose(null, REASONS.INSTRUCTED, key);\n      }\n      _this.handleCloseSnack(null, REASONS.INSTRUCTED, key);\n    };\n    /**\r\n     * When we set open attribute of a snackbar to false (i.e. after we hide a snackbar),\r\n     * it leaves the screen and immediately after leaving animation is done, this method\r\n     * gets called. We remove the hidden snackbar from state and then display notifications\r\n     * waiting in the queue (if any). If after this process the queue is not empty, the\r\n     * oldest message is dismissed.\r\n     */\n    // @ts-ignore\n\n    _this.handleExitedSnack = function (event, key1, key2) {\n      var key = key1 || key2;\n      if (!isDefined(key)) {\n        throw new Error('handleExitedSnack Cannot be called with undefined key');\n      }\n      _this.setState(function (state) {\n        var newState = _this.processQueue(_extends({}, state, {\n          snacks: state.snacks.filter(function (item) {\n            return item.key !== key;\n          })\n        }));\n        if (newState.queue.length === 0) {\n          return newState;\n        }\n        return _this.handleDismissOldest(newState);\n      });\n    };\n    _this.state = {\n      snacks: [],\n      queue: [],\n      contextValue: {\n        enqueueSnackbar: _this.enqueueSnackbar.bind(_assertThisInitialized(_this)),\n        closeSnackbar: _this.closeSnackbar.bind(_assertThisInitialized(_this))\n      }\n    };\n    return _this;\n  }\n  var _proto = SnackbarProvider.prototype;\n  _proto.render = function render() {\n    var _this2 = this;\n    var contextValue = this.state.contextValue;\n    var _this$props = this.props,\n      iconVariant = _this$props.iconVariant,\n      _this$props$dense = _this$props.dense,\n      dense = _this$props$dense === void 0 ? DEFAULTS.dense : _this$props$dense,\n      _this$props$hideIconV = _this$props.hideIconVariant,\n      hideIconVariant = _this$props$hideIconV === void 0 ? DEFAULTS.hideIconVariant : _this$props$hideIconV,\n      domRoot = _this$props.domRoot,\n      children = _this$props.children,\n      _this$props$classes = _this$props.classes,\n      classes = _this$props$classes === void 0 ? {} : _this$props$classes,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"maxSnack\", \"preventDuplicate\", \"variant\", \"anchorOrigin\", \"iconVariant\", \"dense\", \"hideIconVariant\", \"domRoot\", \"children\", \"classes\"]);\n    var categ = this.state.snacks.reduce(function (acc, current) {\n      var _extends2;\n      var category = originKeyExtractor(current.anchorOrigin);\n      var existingOfCategory = acc[category] || [];\n      return _extends({}, acc, (_extends2 = {}, _extends2[category] = [].concat(existingOfCategory, [current]), _extends2));\n    }, {});\n    var snackbars = Object.keys(categ).map(function (origin) {\n      var snacks = categ[origin];\n      return React__default.createElement(SnackbarContainer$1, {\n        key: origin,\n        dense: dense,\n        anchorOrigin: snacks[0].anchorOrigin,\n        className: clsx(classes.containerRoot, classes[transformer.toContainerAnchorOrigin(origin)])\n      }, snacks.map(function (snack) {\n        return React__default.createElement(SnackbarItem, Object.assign({}, props, {\n          key: snack.key,\n          snack: snack,\n          dense: dense,\n          iconVariant: iconVariant,\n          hideIconVariant: hideIconVariant,\n          classes: omitContainerKeys(classes),\n          onClose: _this2.handleCloseSnack,\n          onExited: createChainedFunction([_this2.handleExitedSnack, _this2.props.onExited]),\n          onEntered: createChainedFunction([_this2.handleEnteredSnack, _this2.props.onEntered])\n        }));\n      }));\n    });\n    return React__default.createElement(SnackbarContext.Provider, {\n      value: contextValue\n    }, children, domRoot ? createPortal(snackbars, domRoot) : snackbars);\n  };\n  _createClass(SnackbarProvider, [{\n    key: \"maxSnack\",\n    get: function get() {\n      return this.props.maxSnack || DEFAULTS.maxSnack;\n    }\n  }]);\n  return SnackbarProvider;\n}(Component);\n\n// https://github.com/JamesMGreene/Function.name/blob/58b314d4a983110c3682f1228f845d39ccca1817/Function.name.js#L3\nvar fnNameMatchRegex = /^\\s*function(?:\\s|\\s*\\/\\*.*\\*\\/\\s*)+([^(\\s/]*)\\s*/;\nvar getFunctionName = function getFunctionName(fn) {\n  var match = (\"\" + fn).match(fnNameMatchRegex);\n  var name = match && match[1];\n  return name || '';\n};\n/**\n * @param {function} Component\n * @param {string} fallback\n * @returns {string | undefined}\n */\n\nvar getFunctionComponentName = function getFunctionComponentName(Component, fallback) {\n  if (fallback === void 0) {\n    fallback = '';\n  }\n  return Component.displayName || Component.name || getFunctionName(Component) || fallback;\n};\nvar getWrappedName = function getWrappedName(outerType, innerType, wrapperName) {\n  var functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n};\n/** \n * From react-is\n * @link https://github.com/facebook/react/blob/master/packages/shared/ReactSymbols.js\n */\n\nvar ForwardRef = function ForwardRef() {\n  var symbolFor = typeof Symbol === 'function' && Symbol[\"for\"];\n  return symbolFor ? symbolFor('react.forward_ref') : 0xead0;\n};\n/**\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n *\n * @param {React.ReactType} Component\n * @returns {string | undefined}\n */\n\nvar getDisplayName = function (Component) {\n  if (Component == null) {\n    return undefined;\n  }\n  if (typeof Component === 'string') {\n    return Component;\n  }\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef():\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n};\nvar withSnackbar = function withSnackbar(Component) {\n  var WrappedComponent = React__default.forwardRef(function (props, ref) {\n    return React__default.createElement(SnackbarContext.Consumer, null, function (context) {\n      return React__default.createElement(Component, _extends({}, props, {\n        ref: ref,\n        enqueueSnackbar: context.enqueueSnackbar,\n        closeSnackbar: context.closeSnackbar\n      }));\n    });\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    WrappedComponent.displayName = \"WithSnackbar(\" + getDisplayName(Component) + \")\";\n  }\n  hoistNonReactStatics(WrappedComponent, Component);\n  return WrappedComponent;\n};\nvar useSnackbar = function () {\n  return useContext(SnackbarContext);\n};\nexport { SnackbarContent, SnackbarProvider, useSnackbar, withSnackbar };", "map": {"version": 3, "names": ["SnackbarContext", "React__default", "createContext", "allClasses", "mui", "root", "anchorOriginTopCenter", "anchorOriginBottomCenter", "anchorOriginTopRight", "anchorOriginBottomRight", "anchorOriginTopLeft", "anchorOriginBottomLeft", "container", "containerRoot", "containerAnchorOriginTopCenter", "containerAnchorOriginBottomCenter", "containerAnchorOriginTopRight", "containerAnchorOriginBottomRight", "containerAnchorOriginTopLeft", "containerAnchorOriginBottomLeft", "MESSAGES", "NO_PERSIST_ALL", "SNACKBAR_INDENTS", "view", "dense", "snackbar", "DEFAULTS", "maxSnack", "hideIconVariant", "variant", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "TransitionComponent", "Slide", "transitionDuration", "enter", "exit", "capitalise", "text", "char<PERSON>t", "toUpperCase", "slice", "originKeyExtractor", "anchor", "omitC<PERSON>r<PERSON><PERSON><PERSON>", "classes", "Object", "keys", "filter", "key", "reduce", "obj", "_extends2", "_extends", "REASONS", "TIMEOUT", "CLICKAWAY", "MAXSNACK", "INSTRUCTED", "transformer", "toContainerAnchor<PERSON><PERSON><PERSON>", "origin", "toAnchor<PERSON><PERSON>in", "_ref", "to<PERSON><PERSON><PERSON>", "isDefined", "value", "numberOrNull", "numberish", "merge", "options", "props", "defaults", "name", "objectMerge", "componentName", "Root", "styled", "_ref2", "_ref3", "theme", "display", "flexWrap", "flexGrow", "breakpoints", "up", "min<PERSON><PERSON><PERSON>", "SnackbarContent", "forwardRef", "_ref4", "ref", "className", "_objectWithoutPropertiesLoose", "createElement", "assign", "clsx", "DIRECTION", "right", "left", "bottom", "top", "getTransitionDirection", "CheckIcon", "SvgIcon", "d", "WarningIcon", "ErrorIcon", "InfoIcon", "iconStyles", "fontSize", "marginInlineEnd", "defaultIconVariants", "undefined", "success", "style", "warning", "error", "info", "createChainedFunction", "funcs", "extraArg", "acc", "func", "process", "env", "NODE_ENV", "console", "chainedFunction", "_len", "arguments", "length", "args", "Array", "_key", "argums", "concat", "indexOf", "push", "apply", "useEnhancedEffect", "window", "useLayoutEffect", "useEffect", "useEventCallback", "fn", "useRef", "current", "useCallback", "Snackbar", "children", "ClickAwayListenerProps", "_props$disableWindowB", "disableWindowBlurListener", "onClose", "onMouseEnter", "onMouseLeave", "open", "resumeHideDuration", "other", "timerAutoHide", "handleClose", "setAutoHideTimer", "autoHideDurationParam", "clearTimeout", "setTimeout", "handlePause", "handleResume", "handleMouseEnter", "event", "handleMouseLeave", "handleClickAway", "addEventListener", "removeEventListener", "ClickAwayListener", "onClickAway", "componentName$1", "classes$1", "contentRoot", "lessPadding", "variantSuccess", "variantError", "variantInfo", "variantWarning", "message", "action", "wrappedRoot", "StyledSnackbar", "mode", "palette", "type", "backgroundColor", "emphasize", "background", "position", "transform", "typography", "body2", "color", "getContrastText", "alignItems", "padding", "borderRadius", "boxShadow", "paddingLeft", "marginLeft", "marginRight", "SnackbarItem", "propClasses", "timeout", "useState", "collapsed", "_useState", "setCollapsed", "snack", "handleEntered", "requestClose", "INSTRCUTED", "handleExitedScreen", "otherAriaAttributes", "ariaAttributes", "otherClassName", "icon<PERSON><PERSON><PERSON>", "otherAction", "otherContent", "content", "otherTranComponent", "otherTranProps", "TransitionProps", "otherTranDuration", "singleClassName", "singleContent", "singleAction", "singleAriaAttributes", "snackMessage", "singleTranComponent", "singleTranProps", "singleTranDuration", "singleSnackProps", "icon", "transitionProps", "direction", "callbacks", "cbName", "Collapse", "unmountOnExit", "onExited", "appear", "onExit", "onExiting", "onEnter", "onEntering", "onEntered", "role", "id", "collapse", "wrapper", "xsWidthMargin", "componentName$2", "classes$2", "rootDense", "center", "Root$1", "_ref5", "_ref6", "_ref7", "boxSizing", "maxHeight", "zIndex", "height", "width", "transition", "pointerEvents", "max<PERSON><PERSON><PERSON>", "down", "flexDirection", "SnackbarContainer", "combinedClassname", "SnackbarContainer$1", "memo", "__DEV__", "Error", "x", "SnackbarProvider", "_Component", "_this", "call", "enqueueSnackbar", "opts", "_opts", "preventDuplicate", "hasSpecifiedKey", "Date", "getTime", "Math", "random", "merger", "entered", "persist", "setState", "state", "compareFunction", "item", "inQueue", "queue", "findIndex", "inView", "snacks", "handleDisplaySnack", "handleDismissOldest", "processQueue", "some", "popped", "ignore", "persistentCount", "map", "handleEnteredSnack", "node", "isAppearing", "handleCloseSnack", "reason", "shouldCloseAll", "closeSnackbar", "toBeClosed", "find", "handleExitedSnack", "key1", "key2", "newState", "contextValue", "bind", "_assertThisInitialized", "render", "_this$props", "_this$props$dense", "_this$props$hideIconV", "domRoot", "_this$props$classes", "categ", "category", "existingOfCategory", "snackbars", "_this2", "Provider", "createPortal", "Component", "fnNameMatchRegex", "getFunctionName", "match", "getFunctionComponentName", "fallback", "displayName", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "ForwardRef", "symbolFor", "Symbol", "getDisplayName", "$$typeof", "withSnackbar", "WrappedComponent", "Consumer", "context", "hoistNonReactStatics", "useSnackbar", "useContext"], "sources": ["../src/SnackbarContext.ts", "../src/utils/constants.ts", "../src/SnackbarContent/SnackbarContent.tsx", "../src/SnackbarItem/SnackbarItem.util.ts", "../src/utils/defaultIconVariants.tsx", "../src/utils/createChainedFunction.js", "../src/utils/useEventCallback.js", "../src/SnackbarItem/Snackbar.js", "../src/SnackbarItem/SnackbarItem.tsx", "../src/SnackbarContainer.tsx", "../src/utils/warning.ts", "../src/SnackbarProvider.tsx", "../src/utils/getDisplayName.js", "../src/withSnackbar.js", "../src/useSnackbar.ts"], "sourcesContent": ["import React from 'react';\nimport { ProviderContext } from '.';\n\n// @ts-ignore\nexport default React.createContext<ProviderContext>();\n", "import Slide from '@mui/material/Slide';\nimport { SnackbarClassKey } from '@mui/material/Snackbar';\nimport { CloseReason, ContainerClassKey, SnackbarProviderProps, VariantType, SnackbarOrigin, VariantClassKey } from '../index';\nimport { SnackbarItemProps } from '../SnackbarItem';\nimport { Snack } from '../SnackbarProvider';\n\nexport const allClasses: {\n    mui: Record<SnackbarClassKey, {}>;\n    container: Record<ContainerClassKey, {}>;\n} = {\n    mui: {\n        root: {},\n        anchorOriginTopCenter: {},\n        anchorOriginBottomCenter: {},\n        anchorOriginTopRight: {},\n        anchorOriginBottomRight: {},\n        anchorOriginTopLeft: {},\n        anchorOriginBottomLeft: {},\n    },\n    container: {\n        containerRoot: {},\n        containerAnchorOriginTopCenter: {},\n        containerAnchorOriginBottomCenter: {},\n        containerAnchorOriginTopRight: {},\n        containerAnchorOriginBottomRight: {},\n        containerAnchorOriginTopLeft: {},\n        containerAnchorOriginBottomLeft: {},\n    },\n};\n\nexport const MESSAGES = {\n    NO_PERSIST_ALL: 'WARNING - notistack: Reached maxSnack while all enqueued snackbars have \\'persist\\' flag. Notistack will dismiss the oldest snackbar anyway to allow other ones in the queue to be presented.',\n};\n\nexport const SNACKBAR_INDENTS = {\n    view: { default: 20, dense: 4 },\n    snackbar: { default: 6, dense: 2 },\n};\n\nexport const DEFAULTS = {\n    maxSnack: 3,\n    dense: false,\n    hideIconVariant: false,\n    variant: 'default' as VariantType,\n    autoHideDuration: 5000,\n    anchorOrigin: { vertical: 'bottom', horizontal: 'left' } as SnackbarOrigin,\n    TransitionComponent: Slide,\n    transitionDuration: {\n        enter: 225,\n        exit: 195,\n    },\n};\n\nexport const capitalise = (text: string): string => text.charAt(0).toUpperCase() + text.slice(1);\n\nexport const originKeyExtractor = (anchor: Snack['anchorOrigin']): string => (\n    `${capitalise(anchor.vertical)}${capitalise(anchor.horizontal)}`\n);\n\n/**\n * Omit SnackbarContainer class keys that are not needed for SnackbarItem\n */\nexport const omitContainerKeys = (classes: SnackbarProviderProps['classes']): SnackbarItemProps['classes'] => (\n    // @ts-ignore\n    Object.keys(classes).filter(key => !allClasses.container[key]).reduce((obj, key) => ({ ...obj, [key]: classes[key] }), {})\n);\n\nexport const REASONS: { [key: string]: CloseReason } = {\n    TIMEOUT: 'timeout',\n    CLICKAWAY: 'clickaway',\n    MAXSNACK: 'maxsnack',\n    INSTRUCTED: 'instructed',\n};\n\n/** Tranforms classes name */\nexport const transformer = {\n    toContainerAnchorOrigin: (origin: string) => `containerAnchorOrigin${origin}` as ContainerClassKey,\n    toAnchorOrigin: ({ vertical, horizontal }: SnackbarOrigin) => (\n        `anchorOrigin${capitalise(vertical)}${capitalise(horizontal)}` as SnackbarClassKey\n    ),\n    toVariant: (variant: VariantType) => `variant${capitalise(variant)}` as VariantClassKey,\n};\n\nexport const isDefined = (value: string | null | undefined | number): boolean => (!!value || value === 0);\n\nconst numberOrNull = (numberish?: number | null) => (\n    typeof numberish === 'number' || numberish === null\n);\n\n// @ts-ignore\nexport const merge = (options, props, defaults) => (name: keyof Snack): any => {\n    if (name === 'autoHideDuration') {\n        if (numberOrNull(options.autoHideDuration)) return options.autoHideDuration;\n        if (numberOrNull(props.autoHideDuration)) return props.autoHideDuration;\n        return DEFAULTS.autoHideDuration;\n    }\n\n    return options[name] || props[name] || defaults[name];\n};\n\nexport function objectMerge(options = {}, props = {}, defaults = {}) {\n    return {\n        ...defaults,\n        ...props,\n        ...options,\n    };\n}\n", "import React, { forwardRef } from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { SnackbarContentProps } from '../index';\n\nconst componentName = 'SnackbarContent';\n\nconst classes = {\n    root: `${componentName}-root`,\n};\n\nconst Root = styled('div')(({ theme }) => ({\n    [`&.${classes.root}`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        flexGrow: 1,\n        [theme.breakpoints.up('sm')]: {\n            flexGrow: 'initial',\n            minWidth: 288,\n        },\n    },\n}));\n\nconst SnackbarContent = forwardRef<HTMLDivElement, SnackbarContentProps>(({ className, ...props }, ref) => (\n    <Root ref={ref} className={clsx(classes.root, className)} {...props} />\n));\n\nexport default SnackbarContent;\n", "import { Snack } from '../SnackbarProvider';\n\nconst DIRECTION = {\n    right: 'left',\n    left: 'right',\n    bottom: 'up',\n    top: 'down',\n} as const;\nexport type DirectionType = typeof DIRECTION[keyof typeof DIRECTION]\n\nexport const getTransitionDirection = (anchorOrigin: Snack['anchorOrigin']): DirectionType => {\n    if (anchorOrigin.horizontal !== 'center') {\n        return DIRECTION[anchorOrigin.horizontal];\n    }\n    return DIRECTION[anchorOrigin.vertical];\n};\n", "import React from 'react';\nimport SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';\nimport { IconVariant } from '../index';\n\ntype Icon = (props: SvgIconProps) => JSX.Element;\n\nconst CheckIcon: Icon = props => (\n    <SvgIcon {...props}>\n        <path d=\"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z\" />\n    </SvgIcon>\n);\n\nconst WarningIcon: Icon = props => (\n    <SvgIcon {...props}>\n        <path d=\"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z\" />\n    </SvgIcon>\n);\n\nconst ErrorIcon: Icon = props => (\n    <SvgIcon {...props}>\n        <path d=\"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z\" />\n    </SvgIcon>\n);\n\nconst InfoIcon: Icon = props => (\n    <SvgIcon {...props}>\n        <path d=\"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z\" />\n    </SvgIcon>\n);\n\nconst iconStyles = {\n    fontSize: 20,\n    marginInlineEnd: 8,\n};\n\nconst defaultIconVariants: IconVariant = {\n    default: undefined,\n    success: <CheckIcon style={iconStyles} />,\n    warning: <WarningIcon style={iconStyles} />,\n    error: <ErrorIcon style={iconStyles} />,\n    info: <InfoIcon style={iconStyles} />,\n};\n\nexport default defaultIconVariants;\n", "/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/createChainedFunction.js\n */\nexport default function createChainedFunction(funcs, extraArg) {\n    return funcs.reduce((acc, func) => {\n        if (func == null) return acc;\n\n        if (process.env.NODE_ENV !== 'production') {\n            if (typeof func !== 'function') {\n                // eslint-disable-next-line no-console\n                console.error('Invalid Argument Type. must only provide functions, undefined, or null.');\n            }\n        }\n\n        return function chainedFunction(...args) {\n            const argums = [...args];\n            if (extraArg && argums.indexOf(extraArg) === -1) {\n                argums.push(extraArg);\n            }\n            acc.apply(this, argums);\n            func.apply(this, argums);\n        };\n    }, () => { });\n}\n", "/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/utils/useEventCallback.js\n */\nimport * as React from 'react';\n\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\nexport default function useEventCallback(fn) {\n    const ref = React.useRef(fn);\n    useEnhancedEffect(() => {\n        ref.current = fn;\n    });\n    return React.useCallback((...args) => (0, ref.current)(...args), []);\n}\n", "/**\n * @link https://github.com/mui-org/material-ui/blob/master/packages/material-ui/src/Snackbar/Snackbar.js\n */\nimport * as React from 'react';\nimport ClickAwayListener from '@mui/material/ClickAwayListener';\nimport { REASONS } from '../utils/constants';\nimport useEventCallback from '../utils/useEventCallback';\n\nconst Snackbar = React.forwardRef((props, ref) => {\n    const {\n        children,\n        autoHideDuration,\n        ClickAwayListenerProps,\n        disableWindowBlurListener = false,\n        onClose,\n        onMouseEnter,\n        onMouseLeave,\n        open,\n        resumeHideDuration,\n        ...other\n    } = props;\n\n    const timerAutoHide = React.useRef();\n\n    const handleClose = useEventCallback((...args) => {\n        if (onClose) {\n            onClose(...args);\n        }\n    });\n\n    const setAutoHideTimer = useEventCallback((autoHideDurationParam) => {\n        if (!onClose || autoHideDurationParam == null) {\n            return;\n        }\n\n        clearTimeout(timerAutoHide.current);\n        timerAutoHide.current = setTimeout(() => {\n            handleClose(null, REASONS.TIMEOUT);\n        }, autoHideDurationParam);\n    });\n\n    React.useEffect(() => {\n        if (open) {\n            setAutoHideTimer(autoHideDuration);\n        }\n\n        return () => {\n            clearTimeout(timerAutoHide.current);\n        };\n    }, [open, autoHideDuration, setAutoHideTimer]);\n\n    /**\n     * Pause the timer when the user is interacting with the Snackbar\n     * or when the user hide the window.\n     */\n    const handlePause = () => {\n        clearTimeout(timerAutoHide.current);\n    };\n\n    /**\n     * Restart the timer when the user is no longer interacting with the Snackbar\n     * or when the window is shown back.\n     */\n    const handleResume = React.useCallback(() => {\n        if (autoHideDuration != null) {\n            setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n        }\n    }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n\n    const handleMouseEnter = (event) => {\n        if (onMouseEnter) {\n            onMouseEnter(event);\n        }\n        handlePause();\n    };\n\n    const handleMouseLeave = (event) => {\n        if (onMouseLeave) {\n            onMouseLeave(event);\n        }\n        handleResume();\n    };\n\n    const handleClickAway = (event) => {\n        if (onClose) {\n            onClose(event, REASONS.CLICKAWAY);\n        }\n    };\n\n    React.useEffect(() => {\n        if (!disableWindowBlurListener && open) {\n            window.addEventListener('focus', handleResume);\n            window.addEventListener('blur', handlePause);\n\n            return () => {\n                window.removeEventListener('focus', handleResume);\n                window.removeEventListener('blur', handlePause);\n            };\n        }\n\n        return undefined;\n    }, [disableWindowBlurListener, handleResume, open]);\n\n    return (\n        <ClickAwayListener onClickAway={handleClickAway} {...ClickAwayListenerProps}>\n            <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave} ref={ref} {...other}>\n                {children}\n            </div>\n        </ClickAwayListener>\n    );\n});\n\nexport default Snackbar;\n", "import React, { useState, useEffect, useRef } from 'react';\nimport clsx from 'clsx';\nimport { emphasize, styled } from '@mui/material/styles';\nimport Collapse from '@mui/material/Collapse';\nimport type { SnackbarClassKey } from '@mui/material';\nimport SnackbarContent from '../SnackbarContent';\nimport { getTransitionDirection } from './SnackbarItem.util';\nimport { REASONS, objectMerge, DEFAULTS, transformer } from '../utils/constants';\nimport { SharedProps, RequiredBy, TransitionHandlerProps, SnackbarProviderProps as ProviderProps, ClassNameMap } from '../index';\nimport defaultIconVariants from '../utils/defaultIconVariants';\nimport createChainedFunction from '../utils/createChainedFunction';\nimport { Snack } from '../SnackbarProvider';\nimport Snackbar from './Snackbar';\n\nconst componentName = 'SnackbarItem';\n\nconst classes = {\n    contentRoot: `${componentName}-contentRoot`,\n    lessPadding: `${componentName}-lessPadding`,\n    variantSuccess: `${componentName}-variantSuccess`,\n    variantError: `${componentName}-variantError`,\n    variantInfo: `${componentName}-variantInfo`,\n    variantWarning: `${componentName}-variantWarning`,\n    message: `${componentName}-message`,\n    action: `${componentName}-action`,\n    wrappedRoot: `${componentName}-wrappedRoot`,\n};\n\nconst StyledSnackbar = styled(Snackbar)(({ theme }) => {\n    const mode = theme.palette.mode || theme.palette.type;\n    const backgroundColor = emphasize(theme.palette.background.default, mode === 'light' ? 0.8 : 0.98);\n\n    return {\n        [`&.${classes.wrappedRoot}`]: {\n            position: 'relative',\n            transform: 'translateX(0)',\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n        },\n        [`.${classes.contentRoot}`]: {\n            ...theme.typography.body2,\n            backgroundColor,\n            color: theme.palette.getContrastText(backgroundColor),\n            alignItems: 'center',\n            padding: '6px 16px',\n            borderRadius: '4px',\n            boxShadow: '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)',\n        },\n        [`.${classes.lessPadding}`]: {\n            paddingLeft: 8 * 2.5,\n        },\n        [`.${classes.variantSuccess}`]: {\n            backgroundColor: '#43a047', // green\n            color: '#fff',\n        },\n        [`.${classes.variantError}`]: {\n            backgroundColor: '#d32f2f', // dark red\n            color: '#fff',\n        },\n        [`.${classes.variantInfo}`]: {\n            backgroundColor: '#2196f3', // nice blue\n            color: '#fff',\n        },\n        [`.${classes.variantWarning}`]: {\n            backgroundColor: '#ff9800', // amber\n            color: '#fff',\n        },\n        [`.${classes.message}`]: {\n            display: 'flex',\n            alignItems: 'center',\n            padding: '8px 0',\n        },\n        [`.${classes.action}`]: {\n            display: 'flex',\n            alignItems: 'center',\n            marginLeft: 'auto',\n            paddingLeft: 16,\n            marginRight: -8,\n        },\n    };\n});\n\ntype RemovedProps =\n    | 'variant' // the one received from Provider is processed and passed to snack prop\n    | 'anchorOrigin' // same as above\n    | 'autoHideDuration' // same as above\n    | 'preventDuplicate' // the one recevied from enqueueSnackbar is processed in provider, therefore shouldn't be passed to SnackbarItem */\n\nexport interface SnackbarItemProps extends RequiredBy<Omit<SharedProps, RemovedProps>, 'onEntered' | 'onExited' | 'onClose'> {\n    snack: Snack;\n    dense: ProviderProps['dense'];\n    iconVariant: ProviderProps['iconVariant'];\n    hideIconVariant: ProviderProps['hideIconVariant'];\n    classes: Partial<ClassNameMap<SnackbarClassKey>>;\n}\n\nconst SnackbarItem: React.FC<SnackbarItemProps> = ({ classes: propClasses, ...props }) => {\n    const timeout = useRef<ReturnType<typeof setTimeout>>();\n    const [collapsed, setCollapsed] = useState(true);\n\n    useEffect(() => (): void => {\n        if (timeout.current) {\n            clearTimeout(timeout.current);\n        }\n    }, []);\n\n    const handleClose = createChainedFunction([props.snack.onClose, props.onClose], props.snack.key);\n\n    const handleEntered: TransitionHandlerProps['onEntered'] = () => {\n        if (props.snack.requestClose) {\n            handleClose(null, REASONS.INSTRCUTED);\n        }\n    };\n\n    const handleExitedScreen = (): void => {\n        timeout.current = setTimeout(() => {\n            setCollapsed(!collapsed);\n        }, 125);\n    };\n\n    const {\n        style,\n        dense,\n        ariaAttributes: otherAriaAttributes,\n        className: otherClassName,\n        hideIconVariant,\n        iconVariant,\n        snack,\n        action: otherAction,\n        content: otherContent,\n        TransitionComponent: otherTranComponent,\n        TransitionProps: otherTranProps,\n        transitionDuration: otherTranDuration,\n        onEnter: ignoredOnEnter,\n        onEntered: ignoredOnEntered,\n        onEntering: ignoredOnEntering,\n        onExit: ignoredOnExit,\n        onExited: ignoredOnExited,\n        onExiting: ignoredOnExiting,\n        ...other\n    } = props;\n\n    const {\n        persist,\n        key,\n        open,\n        entered,\n        requestClose,\n        className: singleClassName,\n        variant,\n        content: singleContent,\n        action: singleAction,\n        ariaAttributes: singleAriaAttributes,\n        anchorOrigin,\n        message: snackMessage,\n        TransitionComponent: singleTranComponent,\n        TransitionProps: singleTranProps,\n        transitionDuration: singleTranDuration,\n        onEnter,\n        onEntered,\n        onEntering,\n        onExit,\n        onExited,\n        onExiting,\n        ...singleSnackProps\n    } = snack;\n\n    const icon = {\n        ...defaultIconVariants,\n        ...iconVariant,\n    }[variant];\n\n    const ariaAttributes = {\n        'aria-describedby': 'notistack-snackbar',\n        ...objectMerge(singleAriaAttributes, otherAriaAttributes),\n    };\n\n    const TransitionComponent = singleTranComponent || otherTranComponent || DEFAULTS.TransitionComponent;\n    const transitionDuration = objectMerge(singleTranDuration, otherTranDuration, DEFAULTS.transitionDuration);\n    const transitionProps = {\n        direction: getTransitionDirection(anchorOrigin),\n        ...objectMerge(singleTranProps, otherTranProps),\n    };\n\n    let action = singleAction || otherAction;\n    if (typeof action === 'function') {\n        action = action(key);\n    }\n\n    let content = singleContent || otherContent;\n    if (typeof content === 'function') {\n        content = content(key, snack.message);\n    }\n\n    // eslint-disable-next-line operator-linebreak\n    const callbacks: { [key in keyof TransitionHandlerProps]?: any } =\n        ['onEnter', 'onEntering', 'onEntered', 'onExit', 'onExiting', 'onExited'].reduce((acc, cbName) => ({\n            ...acc,\n            [cbName]: createChainedFunction([\n                props.snack[cbName as keyof Snack],\n                props[cbName as keyof SnackbarItemProps],\n            ], props.snack.key),\n        }), {});\n\n    return (\n        <Collapse\n            unmountOnExit\n            timeout={175}\n            in={collapsed}\n            onExited={callbacks.onExited}\n        >\n            <StyledSnackbar\n                {...other}\n                {...singleSnackProps}\n                open={open}\n                className={clsx(\n                    propClasses.root,\n                    classes.wrappedRoot,\n                    propClasses[transformer.toAnchorOrigin(anchorOrigin)],\n                )}\n                onClose={handleClose}\n            >\n                <TransitionComponent\n                    appear\n                    in={open}\n                    timeout={transitionDuration}\n                    {...transitionProps}\n                    onExit={callbacks.onExit}\n                    onExiting={callbacks.onExiting}\n                    onExited={handleExitedScreen}\n                    onEnter={callbacks.onEnter}\n                    onEntering={callbacks.onEntering}\n                    // order matters. first callbacks.onEntered to set entered: true,\n                    // then handleEntered to check if there's a request for closing\n                    onEntered={createChainedFunction([callbacks.onEntered, handleEntered])}\n                >\n                    {/* @ts-ignore */}\n                    {content || (\n                        <SnackbarContent\n                            {...ariaAttributes}\n                            role=\"alert\"\n                            style={style}\n                            className={clsx(\n                                classes.contentRoot,\n                                { [classes.lessPadding]: !hideIconVariant && icon },\n                                classes[transformer.toVariant(variant)],\n                                propClasses[transformer.toVariant(variant)],\n                                otherClassName,\n                                singleClassName,\n                            )}\n                        >\n                            <div id={ariaAttributes['aria-describedby']} className={classes.message}>\n                                {!hideIconVariant ? icon : null}\n                                {snackMessage}\n                            </div>\n                            {action && (\n                                <div className={classes.action}>{action}</div>\n                            )}\n                        </SnackbarContent>\n                    )}\n                </TransitionComponent>\n            </StyledSnackbar>\n        </Collapse>\n    );\n};\n\nexport default SnackbarItem;\n", "import React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { SNACKBAR_INDENTS } from './utils/constants';\nimport { SnackbarProviderProps } from '.';\n\nconst collapse = {\n    // Material-UI 4.12.x and above uses MuiCollapse-root; earlier versions use\n    // Mui-Collapse-container.  https://github.com/mui-org/material-ui/pull/24084\n    container: '& > .MuiCollapse-container, & > .MuiCollapse-root',\n    wrapper: '& > .MuiCollapse-container > .MuiCollapse-wrapper, & > .MuiCollapse-root > .MuiCollapse-wrapper',\n};\n\nconst xsWidthMargin = 16;\n\nconst componentName = 'SnackbarContainer';\n\nconst classes = {\n    root: `${componentName}-root`,\n    rootDense: `${componentName}-rootDense`,\n    top: `${componentName}-top`,\n    bottom: `${componentName}-bottom`,\n    left: `${componentName}-left`,\n    right: `${componentName}-right`,\n    center: `${componentName}-center`,\n};\n\nconst Root = styled('div')(({ theme }) => ({\n    [`&.${classes.root}`]: {\n        boxSizing: 'border-box',\n        display: 'flex',\n        maxHeight: '100%',\n        position: 'fixed',\n        zIndex: theme.zIndex.snackbar,\n        height: 'auto',\n        width: 'auto',\n        transition: 'top 300ms ease 0ms, right 300ms ease 0ms, bottom 300ms ease 0ms, left 300ms ease 0ms, margin 300ms ease 0ms, max-width 300ms ease 0ms',\n        // container itself is invisible and should not block clicks, clicks should be passed to its children\n        pointerEvents: 'none',\n        [collapse.container]: {\n            pointerEvents: 'all',\n        },\n        [collapse.wrapper]: {\n            padding: `${SNACKBAR_INDENTS.snackbar.default}px 0px`,\n            transition: 'padding 300ms ease 0ms',\n        },\n        maxWidth: `calc(100% - ${SNACKBAR_INDENTS.view.default * 2}px)`,\n        [theme.breakpoints.down('sm')]: {\n            width: '100%',\n            maxWidth: `calc(100% - ${xsWidthMargin * 2}px)`,\n        },\n    },\n    [`&.${classes.rootDense}`]: {\n        [collapse.wrapper]: {\n            padding: `${SNACKBAR_INDENTS.snackbar.dense}px 0px`,\n        },\n    },\n    [`&.${classes.top}`]: {\n        top: SNACKBAR_INDENTS.view.default - SNACKBAR_INDENTS.snackbar.default,\n        flexDirection: 'column',\n    },\n    [`&.${classes.bottom}`]: {\n        bottom: SNACKBAR_INDENTS.view.default - SNACKBAR_INDENTS.snackbar.default,\n        flexDirection: 'column-reverse',\n    },\n    [`&.${classes.left}`]: {\n        left: SNACKBAR_INDENTS.view.default,\n        [theme.breakpoints.up('sm')]: {\n            alignItems: 'flex-start',\n        },\n        [theme.breakpoints.down('sm')]: {\n            left: `${xsWidthMargin}px`,\n        },\n    },\n    [`&.${classes.right}`]: {\n        right: SNACKBAR_INDENTS.view.default,\n        [theme.breakpoints.up('sm')]: {\n            alignItems: 'flex-end',\n        },\n        [theme.breakpoints.down('sm')]: {\n            right: `${xsWidthMargin}px`,\n        },\n    },\n    [`&.${classes.center}`]: {\n        left: '50%',\n        transform: 'translateX(-50%)',\n        [theme.breakpoints.up('sm')]: {\n            alignItems: 'center',\n        },\n    },\n}));\n\ninterface SnackbarContainerProps {\n    children: JSX.Element | JSX.Element[];\n    className?: string;\n    dense: SnackbarProviderProps['dense'];\n    anchorOrigin: NonNullable<SnackbarProviderProps['anchorOrigin']>;\n}\n\nconst SnackbarContainer: React.FC<SnackbarContainerProps> = (props) => {\n    const { className, anchorOrigin, dense, ...other } = props;\n\n    const combinedClassname = clsx(\n        classes[anchorOrigin.vertical],\n        classes[anchorOrigin.horizontal],\n        { [classes.rootDense]: dense },\n        classes.root, // root should come after others to override maxWidth\n        className,\n    );\n\n    return (\n        <Root className={combinedClassname} {...other} />\n    );\n};\n\nexport default React.memo(SnackbarContainer);\n", "/* eslint-disable */\nconst __DEV__ = process.env.NODE_ENV !== 'production';\n\nexport default (message: string) => {\n    if (!__DEV__) return;\n\n    if (typeof console !== 'undefined') {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (x) { };\n};\n", "import React, { Component } from 'react';\nimport { createPortal } from 'react-dom';\nimport clsx from 'clsx';\nimport SnackbarContext from './SnackbarContext';\nimport { MESSAGES, REASONS, originKeyExtractor, omitContainerKeys, DEFAULTS, merge, transformer, isDefined } from './utils/constants';\nimport SnackbarItem from './SnackbarItem';\nimport SnackbarContainer from './SnackbarContainer';\nimport warning from './utils/warning';\nimport { SnackbarProviderProps, SnackbarKey, SnackbarMessage, OptionsObject, RequiredBy, ProviderContext, TransitionHandlerProps } from '.';\nimport createChainedFunction from './utils/createChainedFunction';\n\ntype Reducer = (state: State) => State;\ntype SnacksByPosition = { [key: string]: Snack[] };\n\nexport interface Snack extends RequiredBy<OptionsObject, 'key' | 'variant' | 'anchorOrigin'> {\n    message: SnackbarMessage;\n    open: boolean;\n    entered: boolean;\n    requestClose: boolean;\n}\n\ninterface State {\n    snacks: Snack[];\n    queue: Snack[];\n    contextValue: ProviderContext;\n}\n\nclass SnackbarProvider extends Component<SnackbarProviderProps, State> {\n    constructor(props: SnackbarProviderProps) {\n        super(props);\n        this.state = {\n            snacks: [],\n            queue: [], // eslint-disable-line react/no-unused-state\n            contextValue: {\n                enqueueSnackbar: this.enqueueSnackbar.bind(this),\n                closeSnackbar: this.closeSnackbar.bind(this),\n            },\n        };\n    }\n\n    get maxSnack(): number {\n        return this.props.maxSnack || DEFAULTS.maxSnack;\n    }\n\n    /**\n     * Adds a new snackbar to the queue to be presented.\n     * Returns generated or user defined key referencing the new snackbar or null\n     */\n    enqueueSnackbar = (message: SnackbarMessage, opts: OptionsObject = {}): SnackbarKey => {\n        const {\n            key,\n            preventDuplicate,\n            ...options\n        } = opts;\n\n        const hasSpecifiedKey = isDefined(key);\n        const id = hasSpecifiedKey ? (key as SnackbarKey) : new Date().getTime() + Math.random();\n\n        const merger = merge(options, this.props, DEFAULTS);\n        const snack: Snack = {\n            key: id,\n            ...options,\n            message,\n            open: true,\n            entered: false,\n            requestClose: false,\n            variant: merger('variant'),\n            anchorOrigin: merger('anchorOrigin'),\n            autoHideDuration: merger('autoHideDuration'),\n        };\n\n        if (options.persist) {\n            snack.autoHideDuration = undefined;\n        }\n\n        this.setState((state) => {\n            if ((preventDuplicate === undefined && this.props.preventDuplicate) || preventDuplicate) {\n                const compareFunction = (item: Snack): boolean => (\n                    hasSpecifiedKey ? item.key === key : item.message === message\n                );\n\n                const inQueue = state.queue.findIndex(compareFunction) > -1;\n                const inView = state.snacks.findIndex(compareFunction) > -1;\n                if (inQueue || inView) {\n                    return state;\n                }\n            }\n\n            return this.handleDisplaySnack({\n                ...state,\n                queue: [...state.queue, snack],\n            });\n        });\n\n        return id;\n    };\n\n    /**\n     * Reducer: Display snack if there's space for it. Otherwise, immediately\n     * begin dismissing the oldest message to start showing the new one.\n     */\n    handleDisplaySnack: Reducer = (state) => {\n        const { snacks } = state;\n        if (snacks.length >= this.maxSnack) {\n            return this.handleDismissOldest(state);\n        }\n        return this.processQueue(state);\n    };\n\n    /**\n     * Reducer: Display items (notifications) in the queue if there's space for them.\n     */\n    processQueue: Reducer = (state) => {\n        const { queue, snacks } = state;\n        if (queue.length > 0) {\n            return {\n                ...state,\n                snacks: [...snacks, queue[0]],\n                queue: queue.slice(1, queue.length),\n            };\n        }\n        return state;\n    };\n\n    /**\n     * Reducer: Hide oldest snackbar on the screen because there exists a new one which we have to display.\n     * (ignoring the one with 'persist' flag. i.e. explicitly told by user not to get dismissed).\n     *\n     * Note 1: If there is already a message leaving the screen, no new messages are dismissed.\n     * Note 2: If the oldest message has not yet entered the screen, only a request to close the\n     *         snackbar is made. Once it entered the screen, it will be immediately dismissed.\n     */\n    handleDismissOldest: Reducer = (state) => {\n        if (state.snacks.some(item => !item.open || item.requestClose)) {\n            return state;\n        }\n\n        let popped = false;\n        let ignore = false;\n\n        const persistentCount = state.snacks.reduce((acc, current) => (\n            acc + (current.open && current.persist ? 1 : 0)\n        ), 0);\n\n        if (persistentCount === this.maxSnack) {\n            warning(MESSAGES.NO_PERSIST_ALL);\n            ignore = true;\n        }\n\n        const snacks = state.snacks.map((item) => {\n            if (!popped && (!item.persist || ignore)) {\n                popped = true;\n\n                if (!item.entered) {\n                    return {\n                        ...item,\n                        requestClose: true,\n                    };\n                }\n\n                if (item.onClose) item.onClose(null, REASONS.MAXSNACK, item.key);\n                if (this.props.onClose) this.props.onClose(null, REASONS.MAXSNACK, item.key);\n\n                return {\n                    ...item,\n                    open: false,\n                };\n            }\n\n            return { ...item };\n        });\n\n        return { ...state, snacks };\n    };\n\n    /**\n     * Set the entered state of the snackbar with the given key.\n     */\n    handleEnteredSnack: TransitionHandlerProps['onEntered'] = (node, isAppearing, key) => {\n        if (!isDefined(key)) {\n            throw new Error('handleEnteredSnack Cannot be called with undefined key');\n        }\n\n        this.setState(({ snacks }) => ({\n            snacks: snacks.map(item => (\n                item.key === key ? { ...item, entered: true } : { ...item }\n            )),\n        }));\n    }\n\n    /**\n     * Hide a snackbar after its timeout.\n     */\n    handleCloseSnack: TransitionHandlerProps['onClose'] = (event, reason, key) => {\n        // should not use createChainedFunction for onClose.\n        // because this.closeSnackbar called this function\n        if (this.props.onClose) {\n            this.props.onClose(event, reason, key);\n        }\n\n        if (reason === REASONS.CLICKAWAY) return;\n        const shouldCloseAll = key === undefined;\n\n        this.setState(({ snacks, queue }) => ({\n            snacks: snacks.map((item) => {\n                if (!shouldCloseAll && item.key !== key) {\n                    return { ...item };\n                }\n\n                return item.entered\n                    ? { ...item, open: false }\n                    : { ...item, requestClose: true };\n            }),\n            queue: queue.filter(item => item.key !== key), // eslint-disable-line react/no-unused-state\n        }));\n    };\n\n    /**\n     * Close snackbar with the given key\n     */\n    closeSnackbar: ProviderContext['closeSnackbar'] = (key) => {\n        // call individual snackbar onClose callback passed through options parameter\n        const toBeClosed = this.state.snacks.find(item => item.key === key);\n        if (isDefined(key) && toBeClosed && toBeClosed.onClose) {\n            toBeClosed.onClose(null, REASONS.INSTRUCTED, key);\n        }\n\n        this.handleCloseSnack(null, REASONS.INSTRUCTED, key);\n    }\n\n    /**\n     * When we set open attribute of a snackbar to false (i.e. after we hide a snackbar),\n     * it leaves the screen and immediately after leaving animation is done, this method\n     * gets called. We remove the hidden snackbar from state and then display notifications\n     * waiting in the queue (if any). If after this process the queue is not empty, the\n     * oldest message is dismissed.\n     */\n    // @ts-ignore\n    handleExitedSnack: TransitionHandlerProps['onExited'] = (event, key1, key2) => {\n        const key = key1 || key2;\n        if (!isDefined(key)) {\n            throw new Error('handleExitedSnack Cannot be called with undefined key');\n        }\n\n        this.setState((state) => {\n            const newState = this.processQueue({\n                ...state,\n                snacks: state.snacks.filter(item => item.key !== key),\n            });\n\n            if (newState.queue.length === 0) {\n                return newState;\n            }\n\n            return this.handleDismissOldest(newState);\n        });\n    };\n\n    render(): JSX.Element {\n        const { contextValue } = this.state;\n        const {\n            maxSnack: dontspread1,\n            preventDuplicate: dontspread2,\n            variant: dontspread3,\n            anchorOrigin: dontspread4,\n            iconVariant,\n            dense = DEFAULTS.dense,\n            hideIconVariant = DEFAULTS.hideIconVariant,\n            domRoot,\n            children,\n            classes = {},\n            ...props\n        } = this.props;\n\n        const categ = this.state.snacks.reduce<SnacksByPosition>((acc, current) => {\n            const category = originKeyExtractor(current.anchorOrigin);\n            const existingOfCategory = acc[category] || [];\n            return {\n                ...acc,\n                [category]: [...existingOfCategory, current],\n            };\n        }, {});\n\n        const snackbars = Object.keys(categ).map((origin) => {\n            const snacks = categ[origin];\n            return (\n                <SnackbarContainer\n                    key={origin}\n                    dense={dense}\n                    anchorOrigin={snacks[0].anchorOrigin}\n                    className={clsx(\n                        classes.containerRoot,\n                        classes[transformer.toContainerAnchorOrigin(origin)],\n                    )}\n                >\n                    {snacks.map(snack => (\n                        <SnackbarItem\n                            {...props}\n                            key={snack.key}\n                            snack={snack}\n                            dense={dense}\n                            iconVariant={iconVariant}\n                            hideIconVariant={hideIconVariant}\n                            classes={omitContainerKeys(classes)}\n                            onClose={this.handleCloseSnack}\n                            onExited={createChainedFunction([this.handleExitedSnack, this.props.onExited])}\n                            onEntered={createChainedFunction([this.handleEnteredSnack, this.props.onEntered])}\n                        />\n                    ))}\n                </SnackbarContainer>\n            );\n        });\n\n        return (\n            <SnackbarContext.Provider value={contextValue}>\n                {children}\n                {domRoot ? createPortal(snackbars, domRoot) : snackbars}\n            </SnackbarContext.Provider>\n        );\n    }\n}\n\nexport default SnackbarProvider;\n", "// https://github.com/JamesMGreene/Function.name/blob/58b314d4a983110c3682f1228f845d39ccca1817/Function.name.js#L3\nconst fnNameMatchRegex = /^\\s*function(?:\\s|\\s*\\/\\*.*\\*\\/\\s*)+([^(\\s/]*)\\s*/;\nconst getFunctionName = (fn) => {\n    const match = `${fn}`.match(fnNameMatchRegex);\n    const name = match && match[1];\n    return name || '';\n};\n\n/**\n * @param {function} Component\n * @param {string} fallback\n * @returns {string | undefined}\n */\nconst getFunctionComponentName = (Component, fallback = '') => (\n    Component.displayName || Component.name || getFunctionName(Component) || fallback\n);\n\nconst getWrappedName = (outerType, innerType, wrapperName) => {\n    const functionName = getFunctionComponentName(innerType);\n    return (\n        outerType.displayName || (functionName !== '' ? `${wrapperName}(${functionName})` : wrapperName)\n    );\n};\n\n/** \n * From react-is\n * @link https://github.com/facebook/react/blob/master/packages/shared/ReactSymbols.js\n */\nconst ForwardRef = () => {\n    const symbolFor = typeof Symbol === 'function' && Symbol.for;\n    return symbolFor ? symbolFor('react.forward_ref') : 0xead0\n}\n\n/**\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n *\n * @param {React.ReactType} Component\n * @returns {string | undefined}\n */\nexport default (Component) => {\n    if (Component == null) {\n        return undefined;\n    }\n\n    if (typeof Component === 'string') {\n        return Component;\n    }\n\n    if (typeof Component === 'function') {\n        return getFunctionComponentName(Component, 'Component');\n    }\n\n    if (typeof Component === 'object') {\n        switch (Component.$$typeof) {\n            case ForwardRef():\n                return getWrappedName(Component, Component.render, 'ForwardRef');\n            default:\n                return undefined;\n        }\n    }\n\n    return undefined;\n};\n", "import React from 'react';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nimport getDisplayName from './utils/getDisplayName';\nimport SnackbarContext from './SnackbarContext';\n\nconst withSnackbar = (Component) => {\n    const WrappedComponent = React.forwardRef((props, ref) => (\n        <SnackbarContext.Consumer>\n            {context => (\n                <Component\n                    {...props}\n                    ref={ref}\n                    enqueueSnackbar={context.enqueueSnackbar}\n                    closeSnackbar={context.closeSnackbar}\n                />\n            )}\n        </SnackbarContext.Consumer>\n    ));\n\n    if (process.env.NODE_ENV !== 'production') {\n        WrappedComponent.displayName = `WithSnackbar(${getDisplayName(Component)})`;\n    }\n\n    hoistNonReactStatics(WrappedComponent, Component);\n\n    return WrappedComponent;\n};\n\nexport default withSnackbar;\n", "import { useContext } from 'react';\nimport SnackbarContext from './SnackbarContext';\nimport { ProviderContext } from '.';\n\nexport default (): ProviderContext => useContext(SnackbarContext);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAAA,eAAA,gBAAeC,cAAK,CAACC,aAAN,EAAf;ACEO,IAAMC,UAAU,GAGnB;EACAC,GAAG,EAAE;IACDC,IAAI,EAAE,EADL;IAEDC,qBAAqB,EAAE,EAFtB;IAGDC,wBAAwB,EAAE,EAHzB;IAIDC,oBAAoB,EAAE,EAJrB;IAKDC,uBAAuB,EAAE,EALxB;IAMDC,mBAAmB,EAAE,EANpB;IAODC,sBAAsB,EAAE;EAPvB,CADL;EAUAC,SAAS,EAAE;IACPC,aAAa,EAAE,EADR;IAEPC,8BAA8B,EAAE,EAFzB;IAGPC,iCAAiC,EAAE,EAH5B;IAIPC,6BAA6B,EAAE,EAJxB;IAKPC,gCAAgC,EAAE,EAL3B;IAMPC,4BAA4B,EAAE,EANvB;IAOPC,+BAA+B,EAAE;EAP1B;AAVX,CAHG;AAwBP,IAAaC,QAAQ,GAAG;EACpBC,cAAc,EAAE;AADI,CAAjB;AAIP,IAAaC,gBAAgB,GAAG;EAC5BC,IAAI,EAAE;IAAE,WAAS,EAAX;IAAeC,KAAK,EAAE;EAAtB,CADsB;EAE5BC,QAAQ,EAAE;IAAE,WAAS,CAAX;IAAcD,KAAK,EAAE;EAArB;AAFkB,CAAzB;AAKP,IAAaE,QAAQ,GAAG;EACpBC,QAAQ,EAAE,CADU;EAEpBH,KAAK,EAAE,KAFa;EAGpBI,eAAe,EAAE,KAHG;EAIpBC,OAAO,EAAE,SAJW;EAKpBC,gBAAgB,EAAE,IALE;EAMpBC,YAAY,EAAE;IAAEC,QAAQ,EAAE,QAAZ;IAAsBC,UAAU,EAAE;EAAlC,CANM;EAOpBC,mBAAmB,EAAEC,KAPD;EAQpBC,kBAAkB,EAAE;IAChBC,KAAK,EAAE,GADS;IAEhBC,IAAI,EAAE;EAFU;AARA,CAAjB;AAcP,IAAaC,UAAU,GAAG,SAAbA,UAAaA,CAACC,IAAD;EAAA,OAA0BA,IAAI,CAACC,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BF,IAAI,CAACG,KAAL,CAAW,CAAX,CAAzD;AAAA,CAAnB;AAEP,IAAaC,kBAAkB,GAAG,SAArBA,kBAAqBA,CAACC,MAAD;EAAA,YAC3BN,UAAU,CAACM,MAAM,CAACb,QAAR,CADiB,GACGO,UAAU,CAACM,MAAM,CAACZ,UAAR,CADb;AAAA,CAA3B;AAIP;;;;AAGA,IAAaa,iBAAiB,GAAG,SAApBA,iBAAoBA,CAACC,OAAD;EAAA;IAAA;IAE7BC,MAAM,CAACC,IAAP,CAAYF,OAAZ,EAAqBG,MAArB,CAA4B,UAAAC,GAAG;MAAA,OAAI,CAAChD,UAAU,CAACS,SAAX,CAAqBuC,GAArB,CAAL;IAAA,CAA/B,EAA+DC,MAA/D,CAAsE,UAACC,GAAD,EAAMF,GAAN;MAAA,IAAAG,SAAA;MAAA,OAAAC,QAAA,KAAoBF,GAApB,GAAAC,SAAA,OAAAA,SAAA,CAA0BH,GAA1B,IAAgCJ,OAAO,CAACI,GAAD,CAAvC,EAAAG,SAAA;IAAA,CAAtE,EAAuH,EAAvH;EAAA;AAF6B,CAA1B;AAKP,IAAaE,OAAO,GAAmC;EACnDC,OAAO,EAAE,SAD0C;EAEnDC,SAAS,EAAE,WAFwC;EAGnDC,QAAQ,EAAE,UAHyC;EAInDC,UAAU,EAAE;AAJuC,CAAhD;AAOP;;AACA,IAAaC,WAAW,GAAG;EACvBC,uBAAuB,EAAE,SAAAA,wBAACC,MAAD;IAAA,iCAA4CA,MAA5C;EAAA,CADF;EAEvBC,cAAc,EAAE,SAAAA,eAAAC,IAAA;IAAA,IAAGjC,QAAH,GAAAiC,IAAA,CAAGjC,QAAH;MAAaC,UAAb,GAAAgC,IAAA,CAAahC,UAAb;IAAA,wBACGM,UAAU,CAACP,QAAD,CADb,GAC0BO,UAAU,CAACN,UAAD,CADpC;EAAA,CAFO;EAKvBiC,SAAS,EAAE,SAAAA,UAACrC,OAAD;IAAA,mBAAoCU,UAAU,CAACV,OAAD,CAA9C;EAAA;AALY,CAApB;AAQP,IAAasC,SAAS,GAAG,SAAZA,SAAYA,CAACC,KAAD;EAAA,OAAyD,CAAC,CAACA,KAAF,IAAWA,KAAK,KAAK,CAA9E;AAAA,CAAlB;AAEP,IAAMC,YAAY,GAAG,SAAfA,YAAeA,CAACC,SAAD;EAAA,OACjB,OAAOA,SAAP,KAAqB,QAArB,IAAiCA,SAAS,KAAK,IAD9B;AAAA,CAArB;;AAKA,IAAaC,KAAK,GAAG,SAARA,KAAQA,CAACC,OAAD,EAAUC,KAAV,EAAiBC,QAAjB;EAAA,OAA8B,UAACC,IAAD;IAC/C,IAAIA,IAAI,KAAK,kBAAb,EAAiC;MAC7B,IAAIN,YAAY,CAACG,OAAO,CAAC1C,gBAAT,CAAhB,EAA4C,OAAO0C,OAAO,CAAC1C,gBAAf;MAC5C,IAAIuC,YAAY,CAACI,KAAK,CAAC3C,gBAAP,CAAhB,EAA0C,OAAO2C,KAAK,CAAC3C,gBAAb;MAC1C,OAAOJ,QAAQ,CAACI,gBAAhB;IACH;IAED,OAAO0C,OAAO,CAACG,IAAD,CAAP,IAAiBF,KAAK,CAACE,IAAD,CAAtB,IAAgCD,QAAQ,CAACC,IAAD,CAA/C;EACH,CARoB;AAAA,CAAd;AAUP,SAAgBC,YAAYJ,OAAA,EAAcC,KAAA,EAAYC,QAAA;MAA1BF,OAAA;IAAAA,OAAA,GAAU;;MAAIC,KAAA;IAAAA,KAAA,GAAQ;;MAAIC,QAAA;IAAAA,QAAA,GAAW;;EAC7D,OAAAnB,QAAA,KACOmB,QADP,MAEOD,KAFP,MAGOD,OAHP;AAKH;ACrGD,IAAMK,aAAa,GAAG,iBAAtB;AAEA,IAAM9B,OAAO,GAAG;EACZ1C,IAAI,EAAKwE,aAAL;AADQ,CAAhB;AAIA,IAAMC,IAAI,gBAAGC,MAAM,CAAC,KAAD,CAAN,CAAc,UAAAd,IAAA;EAAA,IAAAe,KAAA,EAAAC,KAAA;EAAA,IAAGC,KAAH,GAAAjB,IAAA,CAAGiB,KAAH;EAAA,OAAAD,KAAA,OAAAA,KAAA,QACjBlC,OAAO,CAAC1C,IADS,KAAA2E,KAAA;IAEnBG,OAAO,EAAE,MAFU;IAGnBC,QAAQ,EAAE,MAHS;IAInBC,QAAQ,EAAE;EAJS,GAAAL,KAAA,CAKlBE,KAAK,CAACI,WAAN,CAAkBC,EAAlB,CAAqB,IAArB,CALkB,IAKW;IAC1BF,QAAQ,EAAE,SADgB;IAE1BG,QAAQ,EAAE;EAFgB,CALX,EAAAR,KAAA,GAAAC,KAAA;AAAA,CAAd,CAAb;AAYA,IAAMQ,eAAe,gBAAGC,UAAU,CAAuC,UAAAC,KAAA,EAA0BC,GAA1B;EAAA,IAAGC,SAAH,GAAAF,KAAA,CAAGE,SAAH;IAAiBpB,KAAjB,GAAAqB,6BAAA,CAAAH,KAAA;EAAA,OACrE1F,cAAA,CAAA8F,aAAA,CAACjB,IAAD,EAAA9B,MAAA,CAAAgD,MAAA;IAAMJ,GAAG,EAAEA,GAAA;IAAKC,SAAS,EAAEI,IAAI,CAAClD,OAAO,CAAC1C,IAAT,EAAewF,SAAf;KAA+BpB,KAAA,CAA9D,CADqE;AAAA,CAAvC,CAAlC;ACrBA,IAAMyB,SAAS,GAAG;EACdC,KAAK,EAAE,MADO;EAEdC,IAAI,EAAE,OAFQ;EAGdC,MAAM,EAAE,IAHM;EAIdC,GAAG,EAAE;AAJS,CAAlB;AAQA,IAAaC,sBAAsB,GAAG,SAAzBA,sBAAyBA,CAACxE,YAAD;EAClC,IAAIA,YAAY,CAACE,UAAb,KAA4B,QAAhC,EAA0C;IACtC,OAAOiE,SAAS,CAACnE,YAAY,CAACE,UAAd,CAAhB;EACH;EACD,OAAOiE,SAAS,CAACnE,YAAY,CAACC,QAAd,CAAhB;AACH,CALM;ACJP,IAAMwE,SAAS,GAAS,SAAlBA,SAAkBA,CAAA/B,KAAK;EAAA,OACzBxE,cAAA,CAAA8F,aAAA,CAACU,OAAD,EAAAzD,MAAA,CAAAgD,MAAA,KAAavB,KAAA,CAAb,EACIxE,cAAA,CAAA8F,aAAA;IAAMW,CAAC,EAAC;GAAR,CADJ,CADyB;AAAA,CAA7B;AAOA,IAAMC,WAAW,GAAS,SAApBA,WAAoBA,CAAAlC,KAAK;EAAA,OAC3BxE,cAAA,CAAA8F,aAAA,CAACU,OAAD,EAAAzD,MAAA,CAAAgD,MAAA,KAAavB,KAAA,CAAb,EACIxE,cAAA,CAAA8F,aAAA;IAAMW,CAAC,EAAC;GAAR,CADJ,CAD2B;AAAA,CAA/B;AAMA,IAAME,SAAS,GAAS,SAAlBA,SAAkBA,CAAAnC,KAAK;EAAA,OACzBxE,cAAA,CAAA8F,aAAA,CAACU,OAAD,EAAAzD,MAAA,CAAAgD,MAAA,KAAavB,KAAA,CAAb,EACIxE,cAAA,CAAA8F,aAAA;IAAMW,CAAC,EAAC;GAAR,CADJ,CADyB;AAAA,CAA7B;AAQA,IAAMG,QAAQ,GAAS,SAAjBA,QAAiBA,CAAApC,KAAK;EAAA,OACxBxE,cAAA,CAAA8F,aAAA,CAACU,OAAD,EAAAzD,MAAA,CAAAgD,MAAA,KAAavB,KAAA,CAAb,EACIxE,cAAA,CAAA8F,aAAA;IAAMW,CAAC,EAAC;GAAR,CADJ,CADwB;AAAA,CAA5B;AAOA,IAAMI,UAAU,GAAG;EACfC,QAAQ,EAAE,EADK;EAEfC,eAAe,EAAE;AAFF,CAAnB;AAKA,IAAMC,mBAAmB,GAAgB;EACrC,WAASC,SAD4B;EAErCC,OAAO,eAAElH,cAAA,CAAA8F,aAAA,CAACS,SAAD;IAAWY,KAAK,EAAEN;GAAlB,CAF4B;EAGrCO,OAAO,eAAEpH,cAAA,CAAA8F,aAAA,CAACY,WAAD;IAAaS,KAAK,EAAEN;GAApB,CAH4B;EAIrCQ,KAAK,eAAErH,cAAA,CAAA8F,aAAA,CAACa,SAAD;IAAWQ,KAAK,EAAEN;GAAlB,CAJ8B;EAKrCS,IAAI,eAAEtH,cAAA,CAAA8F,aAAA,CAACc,QAAD;IAAUO,KAAK,EAAEN;GAAjB;AAL+B,CAAzC;;ACvCA;;;AAGA,SAAwBU,qBAATA,CAA+BC,KAA/B,EAAsCC,QAAtC,EAAgD;EAC3D,OAAOD,KAAK,CAACrE,MAAN,CAAa,UAACuE,GAAD,EAAMC,IAAN,EAAe;IAC/B,IAAIA,IAAI,IAAI,IAAZ,EAAkB,OAAOD,GAAP;IAElB,IAAIE,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACvC,IAAI,OAAOH,IAAP,KAAgB,UAApB,EAAgC;QAC5B;QACAI,OAAO,CAACV,KAAR,CAAc,yEAAd;MACH;IACJ;IAED,OAAO,SAASW,eAATA,CAAA,EAAkC;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAANF,IAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACrC,IAAMC,MAAM,MAAAC,MAAA,CAAOJ,IAAP,CAAZ;MACA,IAAIX,QAAQ,IAAIc,MAAM,CAACE,OAAP,CAAehB,QAAf,MAA6B,CAAC,CAA9C,EAAiD;QAC7Cc,MAAM,CAACG,IAAP,CAAYjB,QAAZ;MACH;MACDC,GAAG,CAACiB,KAAJ,CAAU,IAAV,EAAgBJ,MAAhB;MACAZ,IAAI,CAACgB,KAAL,CAAW,IAAX,EAAiBJ,MAAjB;IACH,CAPD;EAQH,CAlBM,EAkBJ,YAAM,EAlBF,CAAP;AAmBH;;ACvBD;;;AAGA,IAEMK,iBAAiB,GAAG,OAAOC,MAAP,KAAkB,WAAlB,GAAgCC,eAAhC,GAAwDC,SAAlF;AAEA,SAAwBC,gBAATA,CAA0BC,EAA1B,EAA8B;EACzC,IAAMtD,GAAG,GAAGuD,MAAA,CAAaD,EAAb,CAAZ;EACAL,iBAAiB,CAAC,YAAM;IACpBjD,GAAG,CAACwD,OAAJ,GAAcF,EAAd;EACH,CAFgB,CAAjB;EAGA,OAAOG,WAAA,CAAkB;IAAA,OAAczD,GAAM,CAACwD,OAAR,CAAAR,KAAA,SAAAT,SAAA,CAAb;EAAA,CAAlB,EAA0D,EAA1D,CAAP;AACH;ACLD,IAAMmB,QAAQ,gBAAG5D,UAAA,CAAiB,UAACjB,KAAD,EAAQmB,GAAR,EAAgB;EAAA,IAE1C2D,QAF0C,GAY1C9E,KAZ0C,CAE1C8E,QAF0C;IAG1CzH,gBAH0C,GAY1C2C,KAZ0C,CAG1C3C,gBAH0C;IAI1C0H,sBAJ0C,GAY1C/E,KAZ0C,CAI1C+E,sBAJ0C;IAAAC,qBAAA,GAY1ChF,KAZ0C,CAK1CiF,yBAL0C;IAK1CA,yBAL0C,GAAAD,qBAAA,cAKd,KALc,GAAAA,qBAAA;IAM1CE,OAN0C,GAY1ClF,KAZ0C,CAM1CkF,OAN0C;IAO1CC,YAP0C,GAY1CnF,KAZ0C,CAO1CmF,YAP0C;IAQ1CC,YAR0C,GAY1CpF,KAZ0C,CAQ1CoF,YAR0C;IAS1CC,IAT0C,GAY1CrF,KAZ0C,CAS1CqF,IAT0C;IAU1CC,kBAV0C,GAY1CtF,KAZ0C,CAU1CsF,kBAV0C;IAWvCC,KAXuC,GAAAlE,6BAAA,CAY1CrB,KAZ0C;EAc9C,IAAMwF,aAAa,GAAGd,MAAA,EAAtB;EAEA,IAAMe,WAAW,GAAGjB,gBAAgB,CAAC,YAAa;IAC9C,IAAIU,OAAJ,EAAa;MACTA,OAAO,CAAAf,KAAP,SAAAT,SAAA;IACH;EACJ,CAJmC,CAApC;EAMA,IAAMgC,gBAAgB,GAAGlB,gBAAgB,CAAC,UAACmB,qBAAD,EAA2B;IACjE,IAAI,CAACT,OAAD,IAAYS,qBAAqB,IAAI,IAAzC,EAA+C;MAC3C;IACH;IAEDC,YAAY,CAACJ,aAAa,CAACb,OAAf,CAAZ;IACAa,aAAa,CAACb,OAAd,GAAwBkB,UAAU,CAAC,YAAM;MACrCJ,WAAW,CAAC,IAAD,EAAO1G,OAAO,CAACC,OAAf,CAAX;IACH,CAFiC,EAE/B2G,qBAF+B,CAAlC;EAGH,CATwC,CAAzC;EAWApB,SAAA,CAAgB,YAAM;IAClB,IAAIc,IAAJ,EAAU;MACNK,gBAAgB,CAACrI,gBAAD,CAAhB;IACH;IAED,OAAO,YAAM;MACTuI,YAAY,CAACJ,aAAa,CAACb,OAAf,CAAZ;IACH,CAFD;EAGH,CARD,EAQG,CAACU,IAAD,EAAOhI,gBAAP,EAAyBqI,gBAAzB,CARH;EAUA;;;;;EAIA,IAAMI,WAAW,GAAG,SAAdA,WAAcA,CAAA,EAAM;IACtBF,YAAY,CAACJ,aAAa,CAACb,OAAf,CAAZ;EACH,CAFD;EAIA;;;;;EAIA,IAAMoB,YAAY,GAAGnB,WAAA,CAAkB,YAAM;IACzC,IAAIvH,gBAAgB,IAAI,IAAxB,EAA8B;MAC1BqI,gBAAgB,CAACJ,kBAAkB,IAAI,IAAtB,GAA6BA,kBAA7B,GAAkDjI,gBAAgB,GAAG,GAAtE,CAAhB;IACH;EACJ,CAJoB,EAIlB,CAACA,gBAAD,EAAmBiI,kBAAnB,EAAuCI,gBAAvC,CAJkB,CAArB;EAMA,IAAMM,gBAAgB,GAAG,SAAnBA,gBAAmBA,CAACC,KAAD,EAAW;IAChC,IAAId,YAAJ,EAAkB;MACdA,YAAY,CAACc,KAAD,CAAZ;IACH;IACDH,WAAW;EACd,CALD;EAOA,IAAMI,gBAAgB,GAAG,SAAnBA,gBAAmBA,CAACD,KAAD,EAAW;IAChC,IAAIb,YAAJ,EAAkB;MACdA,YAAY,CAACa,KAAD,CAAZ;IACH;IACDF,YAAY;EACf,CALD;EAOA,IAAMI,eAAe,GAAG,SAAlBA,eAAkBA,CAACF,KAAD,EAAW;IAC/B,IAAIf,OAAJ,EAAa;MACTA,OAAO,CAACe,KAAD,EAAQlH,OAAO,CAACE,SAAhB,CAAP;IACH;EACJ,CAJD;EAMAsF,SAAA,CAAgB,YAAM;IAClB,IAAI,CAACU,yBAAD,IAA8BI,IAAlC,EAAwC;MACpChB,MAAM,CAAC+B,gBAAP,CAAwB,OAAxB,EAAiCL,YAAjC;MACA1B,MAAM,CAAC+B,gBAAP,CAAwB,MAAxB,EAAgCN,WAAhC;MAEA,OAAO,YAAM;QACTzB,MAAM,CAACgC,mBAAP,CAA2B,OAA3B,EAAoCN,YAApC;QACA1B,MAAM,CAACgC,mBAAP,CAA2B,MAA3B,EAAmCP,WAAnC;MACH,CAHD;IAIH;IAED,OAAOrD,SAAP;EACH,CAZD,EAYG,CAACwC,yBAAD,EAA4Bc,YAA5B,EAA0CV,IAA1C,CAZH;EAcA,OACI/D,aAAA,CAACgF,iBAAD,EAAAxH,QAAA;IAAmByH,WAAW,EAAEJ;EAAhC,GAAqDpB,sBAArD,GACIzD,aAAA,QAAAxC,QAAA;IAAKqG,YAAY,EAAEa,gBAAnB;IAAqCZ,YAAY,EAAEc,gBAAnD;IAAqE/E,GAAG,EAAEA;EAA1E,GAAmFoE,KAAnF,GACKT,QADL,CADJ,CADJ;AAOH,CAtGgB,CAAjB;ACMA,IAAM0B,eAAa,GAAG,cAAtB;AAEA,IAAMC,SAAO,GAAG;EACZC,WAAW,EAAKF,eAAL,iBADC;EAEZG,WAAW,EAAKH,eAAL,iBAFC;EAGZI,cAAc,EAAKJ,eAAL,oBAHF;EAIZK,YAAY,EAAKL,eAAL,kBAJA;EAKZM,WAAW,EAAKN,eAAL,iBALC;EAMZO,cAAc,EAAKP,eAAL,oBANF;EAOZQ,OAAO,EAAKR,eAAL,aAPK;EAQZS,MAAM,EAAKT,eAAL,YARM;EASZU,WAAW,EAAKV,eAAL;AATC,CAAhB;AAYA,IAAMW,cAAc,gBAAG7G,MAAM,CAACuE,QAAD,CAAN,CAAiB,UAAArF,IAAA;;MAAGiB,KAAA,GAAAjB,IAAA,CAAAiB,KAAA;EACvC,IAAM2G,IAAI,GAAG3G,KAAK,CAAC4G,OAAN,CAAcD,IAAd,IAAsB3G,KAAK,CAAC4G,OAAN,CAAcC,IAAjD;EACA,IAAMC,eAAe,GAAGC,SAAS,CAAC/G,KAAK,CAAC4G,OAAN,CAAcI,UAAd,WAAD,EAAmCL,IAAI,KAAK,OAAT,GAAmB,GAAnB,GAAyB,IAA5D,CAAjC;EAEA,OAAA7G,KAAA,OAAAA,KAAA,QACUkG,SAAO,CAACS,WADlB,IACkC;IAC1BQ,QAAQ,EAAE,UADgB;IAE1BC,SAAS,EAAE,eAFe;IAG1B9F,GAAG,EAAE,CAHqB;IAI1BH,KAAK,EAAE,CAJmB;IAK1BE,MAAM,EAAE,CALkB;IAM1BD,IAAI,EAAE;EANoB,CADlC,EAAApB,KAAA,OASSkG,SAAO,CAACC,WATjB,IAAA5H,QAAA,KAUW2B,KAAK,CAACmH,UAAN,CAAiBC,KAV5B;IAWQN,eAAe,EAAfA,eAXR;IAYQO,KAAK,EAAErH,KAAK,CAAC4G,OAAN,CAAcU,eAAd,CAA8BR,eAA9B,CAZf;IAaQS,UAAU,EAAE,QAbpB;IAcQC,OAAO,EAAE,UAdjB;IAeQC,YAAY,EAAE,KAftB;IAgBQC,SAAS,EAAE;EAhBnB,IAAA5H,KAAA,OAkBSkG,SAAO,CAACE,WAlBjB,IAkBiC;IACzByB,WAAW,EAAE,IAAI;EADQ,CAlBjC,EAAA7H,KAAA,OAqBSkG,SAAO,CAACG,cArBjB,IAqBoC;IAC5BW,eAAe,EAAE,SADW;IAE5BO,KAAK,EAAE;EAFqB,CArBpC,EAAAvH,KAAA,OAyBSkG,SAAO,CAACI,YAzBjB,IAyBkC;IAC1BU,eAAe,EAAE,SADS;IAE1BO,KAAK,EAAE;EAFmB,CAzBlC,EAAAvH,KAAA,OA6BSkG,SAAO,CAACK,WA7BjB,IA6BiC;IACzBS,eAAe,EAAE,SADQ;IAEzBO,KAAK,EAAE;EAFkB,CA7BjC,EAAAvH,KAAA,OAiCSkG,SAAO,CAACM,cAjCjB,IAiCoC;IAC5BQ,eAAe,EAAE,SADW;IAE5BO,KAAK,EAAE;EAFqB,CAjCpC,EAAAvH,KAAA,OAqCSkG,SAAO,CAACO,OArCjB,IAqC6B;IACrBtG,OAAO,EAAE,MADY;IAErBsH,UAAU,EAAE,QAFS;IAGrBC,OAAO,EAAE;EAHY,CArC7B,EAAA1H,KAAA,OA0CSkG,SAAO,CAACQ,MA1CjB,IA0C4B;IACpBvG,OAAO,EAAE,MADW;IAEpBsH,UAAU,EAAE,QAFQ;IAGpBK,UAAU,EAAE,MAHQ;IAIpBD,WAAW,EAAE,EAJO;IAKpBE,WAAW,EAAE,CAAC;EALM,CA1C5B,EAAA/H,KAAA;AAkDH,CAtDsB,CAAvB;AAsEA,IAAMgI,YAAY,GAAgC,SAA5CA,YAA4CA,CAAA/H,KAAA;MAAYgI,WAAA,GAAAhI,KAAA,CAATlC,OAAA;IAAyB0B,KAAA,GAAAqB,6BAAA,CAAAb,KAAA;EAC1E,IAAMiI,OAAO,GAAG/D,MAAM,EAAtB;kBACkCgE,QAAQ,CAAC,IAAD;IAAnCC,SAAA,GAAAC,SAAA;IAAWC,YAAA,GAAAD,SAAA;EAElBrE,SAAS,CAAC;IAAA,OAAM;MACZ,IAAIkE,OAAO,CAAC9D,OAAZ,EAAqB;QACjBiB,YAAY,CAAC6C,OAAO,CAAC9D,OAAT,CAAZ;MACH;IACJ,CAJS;EAAA,CAAD,EAIN,EAJM,CAAT;EAMA,IAAMc,WAAW,GAAG1C,qBAAqB,CAAC,CAAC/C,KAAK,CAAC8I,KAAN,CAAY5D,OAAb,EAAsBlF,KAAK,CAACkF,OAA5B,CAAD,EAAuClF,KAAK,CAAC8I,KAAN,CAAYpK,GAAnD,CAAzC;EAEA,IAAMqK,aAAa,GAAwC,SAArDA,aAAqDA,CAAA;IACvD,IAAI/I,KAAK,CAAC8I,KAAN,CAAYE,YAAhB,EAA8B;MAC1BvD,WAAW,CAAC,IAAD,EAAO1G,OAAO,CAACkK,UAAf,CAAX;IACH;EACJ,CAJD;EAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqBA,CAAA;IACvBT,OAAO,CAAC9D,OAAR,GAAkBkB,UAAU,CAAC;MACzBgD,YAAY,CAAC,CAACF,SAAF,CAAZ;IACH,CAF2B,EAEzB,GAFyB,CAA5B;EAGH,CAJD;MAOIhG,KAAA,GAmBA3C,KAAA,CAnBA2C,KAAA;IACAwG,mBACgB,GAiBhBnJ,KAAA,CAjBAoJ,cAAA;IACWC,cAAA,GAgBXrJ,KAAA,CAhBAoB,SAAA;IACAjE,eAAA,GAeA6C,KAAA,CAfA7C,eAAA;IACAmM,WAAA,GAcAtJ,KAAA,CAdAsJ,WAAA;IACAR,KAAA,GAaA9I,KAAA,CAbA8I,KAAA;IACQS,WAAA,GAYRvJ,KAAA,CAZAiH,MAAA;IACSuC,YAAA,GAWTxJ,KAAA,CAXAyJ,OAAA;IACqBC,kBAAA,GAUrB1J,KAAA,CAVAvC,mBAAA;IACiBkM,cAAA,GASjB3J,KAAA,CATA4J,eAAA;IACoBC,iBAAA,GAQpB7J,KAAA,CARArC,kBAAA;IACS4H,KAMN,GAAAlE,6BAAA,CACHrB,KAAA;MAIAtB,GAAA,GAqBAoK,KAAA,CArBApK,GAAA;IACA2G,IAAA,GAoBAyD,KAAA,CApBAzD,IAAA;IACAyE,eAEW,GAiBXhB,KAAA,CAjBA1H,SAAA;IACAhE,OAAA,GAgBA0L,KAAA,CAhBA1L,OAAA;IACS2M,aAAA,GAeTjB,KAAA,CAfAW,OAAA;IACQO,YAAA,GAcRlB,KAAA,CAdA7B,MAAA;IACgBgD,oBAAA,GAahBnB,KAAA,CAbAM,cAAA;IACA9L,YAAA,GAYAwL,KAAA,CAZAxL,YAAA;IACS4M,YAAA,GAWTpB,KAAA,CAXA9B,OAAA;IACqBmD,mBAAA,GAUrBrB,KAAA,CAVArL,mBAAA;IACiB2M,eAAA,GASjBtB,KAAA,CATAc,eAAA;IACoBS,kBAAA,GAQpBvB,KAAA,CARAnL,kBAAA;IACA2M,gBAMG,GAAAjJ,6BAAA,CACHyH,KAAA;EAEJ,IAAMyB,IAAI,GAAGzL,QAAA,KACN0D,mBADM,MAEN8G,WAFM,EAGXlM,OAHW,CAAb;EAKA,IAAMgM,cAAc,GAAAtK,QAAA;IAChB,oBAAoB;EADJ,GAEbqB,WAAW,CAAC8J,oBAAD,EAAuBd,mBAAvB,CAFE,CAApB;EAKA,IAAM1L,mBAAmB,GAAG0M,mBAAmB,IAAIT,kBAAvB,IAA6CzM,QAAQ,CAACQ,mBAAlF;EACA,IAAME,kBAAkB,GAAGwC,WAAW,CAACkK,kBAAD,EAAqBR,iBAArB,EAAwC5M,QAAQ,CAACU,kBAAjD,CAAtC;EACA,IAAM6M,eAAe,GAAA1L,QAAA;IACjB2L,SAAS,EAAE3I,sBAAsB,CAACxE,YAAD;EADhB,GAEd6C,WAAW,CAACiK,eAAD,EAAkBT,cAAlB,CAFG,CAArB;EAKA,IAAI1C,MAAM,GAAG+C,YAAY,IAAIT,WAA7B;EACA,IAAI,OAAOtC,MAAP,KAAkB,UAAtB,EAAkC;IAC9BA,MAAM,GAAGA,MAAM,CAACvI,GAAD,CAAf;EACH;EAED,IAAI+K,OAAO,GAAGM,aAAa,IAAIP,YAA/B;EACA,IAAI,OAAOC,OAAP,KAAmB,UAAvB,EAAmC;IAC/BA,OAAO,GAAGA,OAAO,CAAC/K,GAAD,EAAMoK,KAAK,CAAC9B,OAAZ,CAAjB;EACH;;EAGD,IAAM0D,SAAS,GACX,CAAC,SAAD,EAAY,YAAZ,EAA0B,WAA1B,EAAuC,QAAvC,EAAiD,WAAjD,EAA8D,UAA9D,EAA0E/L,MAA1E,CAAiF,UAACuE,GAAD,EAAMyH,MAAN;IAAA,IAAA9L,SAAA;IAAA,OAAAC,QAAA,KAC1EoE,GAD0E,GAAArE,SAAA,OAAAA,SAAA,CAE5E8L,MAF4E,IAEnE5H,qBAAqB,CAAC,CAC5B/C,KAAK,CAAC8I,KAAN,CAAY6B,MAAZ,CAD4B,EAE5B3K,KAAK,CAAC2K,MAAD,CAFuB,CAAD,EAG5B3K,KAAK,CAAC8I,KAAN,CAAYpK,GAHgB,CAF8C,EAAAG,SAAA;EAAA,CAAjF,EAMI,EANJ,CADJ;EASA,OACIrD,cAAA,CAAA8F,aAAA,CAACsJ,QAAD;IACIC,aAAa;IACbpC,OAAO,EAAE;IACT,MAAIE,SAAA;IACJmC,QAAQ,EAAEJ,SAAS,CAACI;GAJxB,EAMItP,cAAA,CAAA8F,aAAA,CAAC6F,cAAD,EAAA5I,MAAA,CAAAgD,MAAA,KACQgE,KAAA,EACA+E,gBAAA;IACJjF,IAAI,EAAEA,IAAA;IACNjE,SAAS,EAAEI,IAAI,CACXgH,WAAW,CAAC5M,IADD,EAEX6K,SAAO,CAACS,WAFG,EAGXsB,WAAW,CAACpJ,WAAW,CAACG,cAAZ,CAA2BjC,YAA3B,CAAD,CAHA;IAKf4H,OAAO,EAAEO;IATb,EAWIjK,cAAA,CAAA8F,aAAA,CAAC7D,mBAAD,EAAAc,MAAA,CAAAgD,MAAA;IACIwJ,MAAM;IACN,MAAI1F,IAAA;IACJoD,OAAO,EAAE9K;KACL6M,eAAA;IACJQ,MAAM,EAAEN,SAAS,CAACM,MAAA;IAClBC,SAAS,EAAEP,SAAS,CAACO,SAAA;IACrBH,QAAQ,EAAE5B,kBAAA;IACVgC,OAAO,EAAER,SAAS,CAACQ,OAAA;IACnBC,UAAU,EAAET,SAAS,CAACS,UAAA;IACtB;IACA;IACAC,SAAS,EAAErI,qBAAqB,CAAC,CAAC2H,SAAS,CAACU,SAAX,EAAsBrC,aAAtB,CAAD;IAZpC,EAeKU,OAAO,IACJjO,cAAA,CAAA8F,aAAA,CAACN,eAAD,EAAAzC,MAAA,CAAAgD,MAAA,KACQ6H,cAAA;IACJiC,IAAI,EAAC;IACL1I,KAAK,EAAEA,KAAA;IACPvB,SAAS,EAAEI,IAAI,CACXiF,SAAO,CAACC,WADG,EAGXD,SAAO,CAACrH,WAAW,CAACK,SAAZ,CAAsBrC,OAAtB,CAAD,CAHI,EAIXoL,WAAW,CAACpJ,WAAW,CAACK,SAAZ,CAAsBrC,OAAtB,CAAD,CAJA,EAKXiM,cALW,EAMXS,eANW,EAEc,CAAC3M,eAFf,IAEkCoN,IAFlC,IAER9D,SAAO,CAACE,WAFA;IAJnB,EAaInL,cAAA,CAAA8F,aAAA;IAAKgK,EAAE,EAAElC,cAAc,CAAC,kBAAD;IAAsBhI,SAAS,EAAEqF,SAAO,CAACO;GAAhE,EACK,CAAC7J,eAAD,GAAmBoN,IAAnB,GAA0B,IAD/B,EAEKL,YAFL,CAbJ,EAiBKjD,MAAM,IACHzL,cAAA,CAAA8F,aAAA;IAAKF,SAAS,EAAEqF,SAAO,CAACQ;GAAxB,EAAiCA,MAAjC,CAlBR,CAhBR,CAXJ,CANJ,CADJ;AA4DH,CAxKD;AC5FA,IAAMsE,QAAQ,GAAG;EACb;EACA;EACApP,SAAS,EAAE,mDAHE;EAIbqP,OAAO,EAAE;AAJI,CAAjB;AAOA,IAAMC,aAAa,GAAG,EAAtB;AAEA,IAAMC,eAAa,GAAG,mBAAtB;AAEA,IAAMC,SAAO,GAAG;EACZ/P,IAAI,EAAK8P,eAAL,UADQ;EAEZE,SAAS,EAAKF,eAAL,eAFG;EAGZ7J,GAAG,EAAK6J,eAAL,SAHS;EAIZ9J,MAAM,EAAK8J,eAAL,YAJM;EAKZ/J,IAAI,EAAK+J,eAAL,UALQ;EAMZhK,KAAK,EAAKgK,eAAL,WANO;EAOZG,MAAM,EAAKH,eAAL;AAPM,CAAhB;AAUA,IAAMI,MAAI,gBAAGxL,MAAM,CAAC,KAAD,CAAN,CAAc,UAAAd,IAAA;EAAA,IAAAe,KAAA,EAAAC,KAAA,EAAAU,KAAA,EAAA6K,KAAA,EAAAC,KAAA,EAAAC,KAAA;EAAA,IAAGxL,KAAH,GAAAjB,IAAA,CAAGiB,KAAH;EAAA,OAAAwL,KAAA,OAAAA,KAAA,QACjBN,SAAO,CAAC/P,IADS,KAAA2E,KAAA;IAEnB2L,SAAS,EAAE,YAFQ;IAGnBxL,OAAO,EAAE,MAHU;IAInByL,SAAS,EAAE,MAJQ;IAKnBzE,QAAQ,EAAE,OALS;IAMnB0E,MAAM,EAAE3L,KAAK,CAAC2L,MAAN,CAAapP,QANF;IAOnBqP,MAAM,EAAE,MAPW;IAQnBC,KAAK,EAAE,MARY;IASnBC,UAAU,EAAE,uIATO;IAUnB;IACAC,aAAa,EAAE;EAXI,GAAAjM,KAAA,CAYlBgL,QAAQ,CAACpP,SAZS,IAYG;IAClBqQ,aAAa,EAAE;EADG,CAZH,EAAAjM,KAAA,CAelBgL,QAAQ,CAACC,OAfS,IAeC;IAChBvD,OAAO,EAAKpL,gBAAgB,CAACG,QAAjB,WAAL,WADS;IAEhBuP,UAAU,EAAE;EAFI,CAfD,EAAAhM,KAAA,CAmBnBkM,QAnBmB,oBAmBM5P,gBAAgB,CAACC,IAAjB,cAAgC,CAnBtC,UAAAyD,KAAA,CAoBlBE,KAAK,CAACI,WAAN,CAAkB6L,IAAlB,CAAuB,IAAvB,CApBkB,IAoBa;IAC5BJ,KAAK,EAAE,MADqB;IAE5BG,QAAQ,mBAAiBhB,aAAa,GAAG,CAAjC;EAFoB,CApBb,EAAAlL,KAAA,GAAA0L,KAAA,QAyBjBN,SAAO,CAACC,SAzBS,KAAApL,KAAA,OAAAA,KAAA,CA0BlB+K,QAAQ,CAACC,OA1BS,IA0BC;IAChBvD,OAAO,EAAKpL,gBAAgB,CAACG,QAAjB,CAA0BD,KAA/B;EADS,CA1BD,EAAAyD,KAAA,GAAAyL,KAAA,QA8BjBN,SAAO,CAAC9J,GA9BS,IA8BD;IAClBA,GAAG,EAAEhF,gBAAgB,CAACC,IAAjB,cAAgCD,gBAAgB,CAACG,QAAjB,WADnB;IAElB2P,aAAa,EAAE;EAFG,CA9BC,EAAAV,KAAA,QAkCjBN,SAAO,CAAC/J,MAlCS,IAkCE;IACrBA,MAAM,EAAE/E,gBAAgB,CAACC,IAAjB,cAAgCD,gBAAgB,CAACG,QAAjB,WADnB;IAErB2P,aAAa,EAAE;EAFM,CAlCF,EAAAV,KAAA,QAsCjBN,SAAO,CAAChK,IAtCS,KAAAT,KAAA;IAuCnBS,IAAI,EAAE9E,gBAAgB,CAACC,IAAjB;EAvCa,GAAAoE,KAAA,CAwClBT,KAAK,CAACI,WAAN,CAAkBC,EAAlB,CAAqB,IAArB,CAxCkB,IAwCW;IAC1BkH,UAAU,EAAE;EADc,CAxCX,EAAA9G,KAAA,CA2ClBT,KAAK,CAACI,WAAN,CAAkB6L,IAAlB,CAAuB,IAAvB,CA3CkB,IA2Ca;IAC5B/K,IAAI,EAAK8J,aAAL;EADwB,CA3Cb,EAAAvK,KAAA,GAAA+K,KAAA,QA+CjBN,SAAO,CAACjK,KA/CS,KAAAqK,KAAA;IAgDnBrK,KAAK,EAAE7E,gBAAgB,CAACC,IAAjB;EAhDY,GAAAiP,KAAA,CAiDlBtL,KAAK,CAACI,WAAN,CAAkBC,EAAlB,CAAqB,IAArB,CAjDkB,IAiDW;IAC1BkH,UAAU,EAAE;EADc,CAjDX,EAAA+D,KAAA,CAoDlBtL,KAAK,CAACI,WAAN,CAAkB6L,IAAlB,CAAuB,IAAvB,CApDkB,IAoDa;IAC5BhL,KAAK,EAAK+J,aAAL;EADuB,CApDb,EAAAM,KAAA,GAAAE,KAAA,QAwDjBN,SAAO,CAACE,MAxDS,KAAAG,KAAA;IAyDnBrK,IAAI,EAAE,KAzDa;IA0DnBgG,SAAS,EAAE;EA1DQ,GAAAqE,KAAA,CA2DlBvL,KAAK,CAACI,WAAN,CAAkBC,EAAlB,CAAqB,IAArB,CA3DkB,IA2DW;IAC1BkH,UAAU,EAAE;EADc,CA3DX,EAAAgE,KAAA,GAAAC,KAAA;AAAA,CAAd,CAAb;AAwEA,IAAMW,iBAAiB,GAAqC,SAAtDA,iBAAsDA,CAAC5M,KAAD;MAChDoB,SAAA,GAA6CpB,KAAA,CAA7CoB,SAAA;IAAW9D,YAAA,GAAkC0C,KAAA,CAAlC1C,YAAA;IAAcP,KAAA,GAAoBiD,KAAA,CAApBjD,KAAA;IAAUwI,KAAA,GAAAlE,6BAAA,CAAUrB,KAAA;EAErD,IAAM6M,iBAAiB,GAAGrL,IAAI,CAC1BmK,SAAO,CAACrO,YAAY,CAACC,QAAd,CADmB,EAE1BoO,SAAO,CAACrO,YAAY,CAACE,UAAd,CAFmB,EAI1BmO,SAAO,CAAC/P,IAJkB;EAAA;EAK1BwF,SAL0B,EAGHrE,KAHG,IAGvB4O,SAAO,CAACC,SAHe,CAA9B;EAQA,OACIpQ,cAAA,CAAA8F,aAAA,CAACwK,MAAD,EAAAvN,MAAA,CAAAgD,MAAA;IAAMH,SAAS,EAAEyL;KAAuBtH,KAAA,CAAxC,CADJ;AAGH,CAdD;AAgBA,IAAAuH,mBAAA,gBAAetR,cAAK,CAACuR,IAAN,CAAWH,iBAAX,CAAf;;ACnHA;AACA,IAAMI,OAAO,GAAG5J,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzC;AAEA,IAAAV,OAAA,GAAe,SAAAA,CAACoE,OAAD;EACX,IAAI,CAACgG,OAAL,EAAc;EAEd,IAAI,OAAOzJ,OAAP,KAAmB,WAAvB,EAAoC;IAChCA,OAAO,CAACV,KAAR,CAAcmE,OAAd;EACH;EACD,IAAI;IACA,MAAM,IAAIiG,KAAJ,CAAUjG,OAAV,CAAN;EACH,CAFD,CAEE,OAAOkG,CAAP,EAAU;AACf,CATD;ICwBMC,gBAAA,0BAAAC,UAAA;;EACF,SAAAD,iBAAYnN,KAAZ;;IACIqN,KAAA,GAAAD,UAAA,CAAAE,IAAA,OAAMtN,KAAN;IAeJ;;;;;IAIAqN,KAAA,CAAAE,eAAA,GAAkB,UAACvG,OAAD,EAA2BwG,IAA3B;UAA2BA,IAAA;QAAAA,IAAA,GAAsB;;kBAK3DA,IAAA;QAHA9O,GAAA,GAAA+O,KAAA,CAAA/O,GAAA;QACAgP,gBAAA,GAAAD,KAAA,CAAAC,gBAAA;QACG3N,OAAA,GAAAsB,6BAAA,CAAAoM,KAAA;MAGP,IAAME,eAAe,GAAGjO,SAAS,CAAChB,GAAD,CAAjC;MACA,IAAM4M,EAAE,GAAGqC,eAAe,GAAIjP,GAAJ,GAA0B,IAAIkP,IAAJ,GAAWC,OAAX,KAAuBC,IAAI,CAACC,MAAL,EAA3E;MAEA,IAAMC,MAAM,GAAGlO,KAAK,CAACC,OAAD,EAAUsN,KAAA,CAAKrN,KAAf,EAAsB/C,QAAtB,CAApB;MACA,IAAM6L,KAAK,GAAAhK,QAAA;QACPJ,GAAG,EAAE4M;MADE,GAEJvL,OAFI;QAGPiH,OAAO,EAAPA,OAHO;QAIP3B,IAAI,EAAE,IAJC;QAKP4I,OAAO,EAAE,KALF;QAMPjF,YAAY,EAAE,KANP;QAOP5L,OAAO,EAAE4Q,MAAM,CAAC,SAAD,CAPR;QAQP1Q,YAAY,EAAE0Q,MAAM,CAAC,cAAD,CARb;QASP3Q,gBAAgB,EAAE2Q,MAAM,CAAC,kBAAD;MATjB,EAAX;MAYA,IAAIjO,OAAO,CAACmO,OAAZ,EAAqB;QACjBpF,KAAK,CAACzL,gBAAN,GAAyBoF,SAAzB;MACH;MAED4K,KAAA,CAAKc,QAAL,CAAc,UAACC,KAAD;QACV,IAAKV,gBAAgB,KAAKjL,SAArB,IAAkC4K,KAAA,CAAKrN,KAAL,CAAW0N,gBAA9C,IAAmEA,gBAAvE,EAAyF;UACrF,IAAMW,eAAe,GAAG,SAAlBA,eAAkBA,CAACC,IAAD;YAAA,OACpBX,eAAe,GAAGW,IAAI,CAAC5P,GAAL,KAAaA,GAAhB,GAAsB4P,IAAI,CAACtH,OAAL,KAAiBA,OADlC;UAAA,CAAxB;UAIA,IAAMuH,OAAO,GAAGH,KAAK,CAACI,KAAN,CAAYC,SAAZ,CAAsBJ,eAAtB,IAAyC,CAAC,CAA1D;UACA,IAAMK,MAAM,GAAGN,KAAK,CAACO,MAAN,CAAaF,SAAb,CAAuBJ,eAAvB,IAA0C,CAAC,CAA1D;UACA,IAAIE,OAAO,IAAIG,MAAf,EAAuB;YACnB,OAAON,KAAP;UACH;QACJ;QAED,OAAOf,KAAA,CAAKuB,kBAAL,CAAA9P,QAAA,KACAsP,KADA;UAEHI,KAAK,KAAAxK,MAAA,CAAMoK,KAAK,CAACI,KAAZ,GAAmB1F,KAAnB;QAFF,GAAP;MAIH,CAjBD;MAmBA,OAAOwC,EAAP;IACH,CA/CD;IAiDA;;;;;IAIA+B,KAAA,CAAAuB,kBAAA,GAA8B,UAACR,KAAD;UAClBO,MAAA,GAAWP,KAAA,CAAXO,MAAA;MACR,IAAIA,MAAM,CAAChL,MAAP,IAAiB0J,KAAA,CAAKnQ,QAA1B,EAAoC;QAChC,OAAOmQ,KAAA,CAAKwB,mBAAL,CAAyBT,KAAzB,CAAP;MACH;MACD,OAAOf,KAAA,CAAKyB,YAAL,CAAkBV,KAAlB,CAAP;IACH,CAND;IAQA;;;;IAGAf,KAAA,CAAAyB,YAAA,GAAwB,UAACV,KAAD;UACZI,KAAA,GAAkBJ,KAAA,CAAlBI,KAAA;QAAOG,MAAA,GAAWP,KAAA,CAAXO,MAAA;MACf,IAAIH,KAAK,CAAC7K,MAAN,GAAe,CAAnB,EAAsB;QAClB,OAAA7E,QAAA,KACOsP,KADP;UAEIO,MAAM,KAAA3K,MAAA,CAAM2K,MAAN,GAAcH,KAAK,CAAC,CAAD,CAAnB,EAFV;UAGIA,KAAK,EAAEA,KAAK,CAACtQ,KAAN,CAAY,CAAZ,EAAesQ,KAAK,CAAC7K,MAArB;QAHX;MAKH;MACD,OAAOyK,KAAP;IACH,CAVD;IAYA;;;;;;;;;IAQAf,KAAA,CAAAwB,mBAAA,GAA+B,UAACT,KAAD;MAC3B,IAAIA,KAAK,CAACO,MAAN,CAAaI,IAAb,CAAkB,UAAAT,IAAI;QAAA,OAAI,CAACA,IAAI,CAACjJ,IAAN,IAAciJ,IAAI,CAACtF,YAAvB;MAAA,CAAtB,CAAJ,EAAgE;QAC5D,OAAOoF,KAAP;MACH;MAED,IAAIY,MAAM,GAAG,KAAb;MACA,IAAIC,MAAM,GAAG,KAAb;MAEA,IAAMC,eAAe,GAAGd,KAAK,CAACO,MAAN,CAAahQ,MAAb,CAAoB,UAACuE,GAAD,EAAMyB,OAAN;QAAA,OACxCzB,GAAG,IAAIyB,OAAO,CAACU,IAAR,IAAgBV,OAAO,CAACuJ,OAAxB,GAAkC,CAAlC,GAAsC,CAA1C,CADqC;MAAA,CAApB,EAErB,CAFqB,CAAxB;MAIA,IAAIgB,eAAe,KAAK7B,KAAA,CAAKnQ,QAA7B,EAAuC;QACnCkG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAV,OAAO,CAACjG,QAAQ,CAACC,cAAV,CAAP;QACAqS,MAAM,GAAG,IAAT;MACH;MAED,IAAMN,MAAM,GAAGP,KAAK,CAACO,MAAN,CAAaQ,GAAb,CAAiB,UAACb,IAAD;QAC5B,IAAI,CAACU,MAAD,KAAY,CAACV,IAAI,CAACJ,OAAN,IAAiBe,MAA7B,CAAJ,EAA0C;UACtCD,MAAM,GAAG,IAAT;UAEA,IAAI,CAACV,IAAI,CAACL,OAAV,EAAmB;YACf,OAAAnP,QAAA,KACOwP,IADP;cAEItF,YAAY,EAAE;YAFlB;UAIH;UAED,IAAIsF,IAAI,CAACpJ,OAAT,EAAkBoJ,IAAI,CAACpJ,OAAL,CAAa,IAAb,EAAmBnG,OAAO,CAACG,QAA3B,EAAqCoP,IAAI,CAAC5P,GAA1C;UAClB,IAAI2O,KAAA,CAAKrN,KAAL,CAAWkF,OAAf,EAAwBmI,KAAA,CAAKrN,KAAL,CAAWkF,OAAX,CAAmB,IAAnB,EAAyBnG,OAAO,CAACG,QAAjC,EAA2CoP,IAAI,CAAC5P,GAAhD;UAExB,OAAAI,QAAA,KACOwP,IADP;YAEIjJ,IAAI,EAAE;UAFV;QAIH;QAED,OAAAvG,QAAA,KAAYwP,IAAZ;MACH,CArBc,CAAf;MAuBA,OAAAxP,QAAA,KAAYsP,KAAZ;QAAmBO,MAAM,EAANA;MAAnB;IACH,CAzCD;IA2CA;;;;IAGAtB,KAAA,CAAA+B,kBAAA,GAA0D,UAACC,IAAD,EAAOC,WAAP,EAAoB5Q,GAApB;MACtD,IAAI,CAACgB,SAAS,CAAChB,GAAD,CAAd,EAAqB;QACjB,MAAM,IAAIuO,KAAJ,CAAU,wDAAV,CAAN;MACH;MAEDI,KAAA,CAAKc,QAAL,CAAc,UAAA3O,IAAA;QAAA,IAAGmP,MAAH,GAAAnP,IAAA,CAAGmP,MAAH;QAAA,OAAiB;UAC3BA,MAAM,EAAEA,MAAM,CAACQ,GAAP,CAAW,UAAAb,IAAI;YAAA,OACnBA,IAAI,CAAC5P,GAAL,KAAaA,GAAb,GAAAI,QAAA,KAAwBwP,IAAxB;cAA8BL,OAAO,EAAE;YAAvC,KAAAnP,QAAA,KAAqDwP,IAArD,CADmB;UAAA,CAAf;QADmB,CAAjB;MAAA,CAAd;IAKH,CAVD;IAYA;;;;IAGAjB,KAAA,CAAAkC,gBAAA,GAAsD,UAACtJ,KAAD,EAAQuJ,MAAR,EAAgB9Q,GAAhB;MAClD;MACA;MACA,IAAI2O,KAAA,CAAKrN,KAAL,CAAWkF,OAAf,EAAwB;QACpBmI,KAAA,CAAKrN,KAAL,CAAWkF,OAAX,CAAmBe,KAAnB,EAA0BuJ,MAA1B,EAAkC9Q,GAAlC;MACH;MAED,IAAI8Q,MAAM,KAAKzQ,OAAO,CAACE,SAAvB,EAAkC;MAClC,IAAMwQ,cAAc,GAAG/Q,GAAG,KAAK+D,SAA/B;MAEA4K,KAAA,CAAKc,QAAL,CAAc,UAAA5N,KAAA;QAAA,IAAGoO,MAAH,GAAApO,KAAA,CAAGoO,MAAH;UAAWH,KAAX,GAAAjO,KAAA,CAAWiO,KAAX;QAAA,OAAwB;UAClCG,MAAM,EAAEA,MAAM,CAACQ,GAAP,CAAW,UAACb,IAAD;YACf,IAAI,CAACmB,cAAD,IAAmBnB,IAAI,CAAC5P,GAAL,KAAaA,GAApC,EAAyC;cACrC,OAAAI,QAAA,KAAYwP,IAAZ;YACH;YAED,OAAOA,IAAI,CAACL,OAAL,GAAAnP,QAAA,KACIwP,IADJ;cACUjJ,IAAI,EAAE;YADhB,KAAAvG,QAAA,KAEIwP,IAFJ;cAEUtF,YAAY,EAAE;YAFxB,EAAP;UAGH,CARO,CAD0B;UAUlCwF,KAAK,EAAEA,KAAK,CAAC/P,MAAN,CAAa,UAAA6P,IAAI;YAAA,OAAIA,IAAI,CAAC5P,GAAL,KAAaA,GAAjB;UAAA,CAAjB;QAV2B,CAAxB;MAAA,CAAd;IAYH,CAtBD;IAwBA;;;;IAGA2O,KAAA,CAAAqC,aAAA,GAAkD,UAAChR,GAAD;MAC9C;MACA,IAAMiR,UAAU,GAAGtC,KAAA,CAAKe,KAAL,CAAWO,MAAX,CAAkBiB,IAAlB,CAAuB,UAAAtB,IAAI;QAAA,OAAIA,IAAI,CAAC5P,GAAL,KAAaA,GAAjB;MAAA,CAA3B,CAAnB;MACA,IAAIgB,SAAS,CAAChB,GAAD,CAAT,IAAkBiR,UAAlB,IAAgCA,UAAU,CAACzK,OAA/C,EAAwD;QACpDyK,UAAU,CAACzK,OAAX,CAAmB,IAAnB,EAAyBnG,OAAO,CAACI,UAAjC,EAA6CT,GAA7C;MACH;MAED2O,KAAA,CAAKkC,gBAAL,CAAsB,IAAtB,EAA4BxQ,OAAO,CAACI,UAApC,EAAgDT,GAAhD;IACH,CARD;IAUA;;;;;;;IAOA;;IACA2O,KAAA,CAAAwC,iBAAA,GAAwD,UAAC5J,KAAD,EAAQ6J,IAAR,EAAcC,IAAd;MACpD,IAAMrR,GAAG,GAAGoR,IAAI,IAAIC,IAApB;MACA,IAAI,CAACrQ,SAAS,CAAChB,GAAD,CAAd,EAAqB;QACjB,MAAM,IAAIuO,KAAJ,CAAU,uDAAV,CAAN;MACH;MAEDI,KAAA,CAAKc,QAAL,CAAc,UAACC,KAAD;QACV,IAAM4B,QAAQ,GAAG3C,KAAA,CAAKyB,YAAL,CAAAhQ,QAAA,KACVsP,KADU;UAEbO,MAAM,EAAEP,KAAK,CAACO,MAAN,CAAalQ,MAAb,CAAoB,UAAA6P,IAAI;YAAA,OAAIA,IAAI,CAAC5P,GAAL,KAAaA,GAAjB;UAAA,CAAxB;QAFK,GAAjB;QAKA,IAAIsR,QAAQ,CAACxB,KAAT,CAAe7K,MAAf,KAA0B,CAA9B,EAAiC;UAC7B,OAAOqM,QAAP;QACH;QAED,OAAO3C,KAAA,CAAKwB,mBAAL,CAAyBmB,QAAzB,CAAP;MACH,CAXD;IAYH,CAlBD;IAhNI3C,KAAA,CAAKe,KAAL,GAAa;MACTO,MAAM,EAAE,EADC;MAETH,KAAK,EAAE,EAFE;MAGTyB,YAAY,EAAE;QACV1C,eAAe,EAAEF,KAAA,CAAKE,eAAL,CAAqB2C,IAArB,CAAAC,sBAAA,CAAA9C,KAAA,EADP;QAEVqC,aAAa,EAAErC,KAAA,CAAKqC,aAAL,CAAmBQ,IAAnB,CAAAC,sBAAA,CAAA9C,KAAA;MAFL;IAHL,CAAb;;EAQH;;SA4ND+C,MAAA,YAAAA,OAAA;;QACYH,YAAA,GAAiB,KAAK7B,KAAA,CAAtB6B,YAAA;sBAaJ,KAAKjQ,KAAA;MAXKsJ,WAIV,GAAA+G,WAAA,CAAA/G,WAAA;sCACAvM,KAAA;MAAAA,KAAA,GAAAuT,iBAAA,cAAQrT,QAAQ,CAACF,KAAA,GAAAuT,iBAAA;0CACjBnT,eAAA;MAAAA,eAAA,GAAAoT,qBAAA,cAAkBtT,QAAQ,CAACE,eAAA,GAAAoT,qBAAA;MAC3BC,OAAA,GAAAH,WAAA,CAAAG,OAAA;MACA1L,QAAA,GAAAuL,WAAA,CAAAvL,QAAA;wCACAxG,OAAA;MAAAA,OAAA,GAAAmS,mBAAA,cAAU,KAAAA,mBAAA;MACPzQ,KAAA,GAAAqB,6BAAA,CAAAgP,WAAA;IAGP,IAAMK,KAAK,GAAG,KAAKtC,KAAL,CAAWO,MAAX,CAAkBhQ,MAAlB,CAA2C,UAACuE,GAAD,EAAMyB,OAAN;;MACrD,IAAMgM,QAAQ,GAAGxS,kBAAkB,CAACwG,OAAO,CAACrH,YAAT,CAAnC;MACA,IAAMsT,kBAAkB,GAAG1N,GAAG,CAACyN,QAAD,CAAH,IAAiB,EAA5C;MACA,OAAA7R,QAAA,KACOoE,GADP,GAAArE,SAAA,OAAAA,SAAA,CAEK8R,QAFL,OAAA3M,MAAA,CAEoB4M,kBAFpB,GAEwCjM,OAFxC,IAAA9F,SAAA;IAIH,CAPa,EAOX,EAPW,CAAd;IASA,IAAMgS,SAAS,GAAGtS,MAAM,CAACC,IAAP,CAAYkS,KAAZ,EAAmBvB,GAAnB,CAAuB,UAAC7P,MAAD;MACrC,IAAMqP,MAAM,GAAG+B,KAAK,CAACpR,MAAD,CAApB;MACA,OACI9D,cAAA,CAAA8F,aAAA,CAACwL,mBAAD;QACIpO,GAAG,EAAEY,MAAA;QACLvC,KAAK,EAAEA,KAAA;QACPO,YAAY,EAAEqR,MAAM,CAAC,CAAD,CAAN,CAAUrR,YAAA;QACxB8D,SAAS,EAAEI,IAAI,CACXlD,OAAO,CAAClC,aADG,EAEXkC,OAAO,CAACc,WAAW,CAACC,uBAAZ,CAAoCC,MAApC,CAAD,CAFI;OAJnB,EASKqP,MAAM,CAACQ,GAAP,CAAW,UAAArG,KAAK;QAAA,OACbtN,cAAA,CAAA8F,aAAA,CAACiH,YAAD,EAAAhK,MAAA,CAAAgD,MAAA,KACQvB,KAAA;UACJtB,GAAG,EAAEoK,KAAK,CAACpK,GAAA;UACXoK,KAAK,EAAEA,KAAA;UACP/L,KAAK,EAAEA,KAAA;UACPuM,WAAW,EAAEA,WAAA;UACbnM,eAAe,EAAEA,eAAA;UACjBmB,OAAO,EAAED,iBAAiB,CAACC,OAAD;UAC1B4G,OAAO,EAAE4L,MAAI,CAACvB,gBAAA;UACdzE,QAAQ,EAAE/H,qBAAqB,CAAC,CAAC+N,MAAI,CAACjB,iBAAN,EAAyBiB,MAAI,CAAC9Q,KAAL,CAAW8K,QAApC,CAAD;UAC/BM,SAAS,EAAErI,qBAAqB,CAAC,CAAC+N,MAAI,CAAC1B,kBAAN,EAA0B0B,MAAI,CAAC9Q,KAAL,CAAWoL,SAArC,CAAD;UAVpC,CADa;MAAA,CAAhB,CATL,CADJ;IA0BH,CA5BiB,CAAlB;IA8BA,OACI5P,cAAA,CAAA8F,aAAA,CAAC/F,eAAe,CAACwV,QAAjB;MAA0BpR,KAAK,EAAEsQ;KAAjC,EACKnL,QADL,EAEK0L,OAAO,GAAGQ,YAAY,CAACH,SAAD,EAAYL,OAAZ,CAAf,GAAsCK,SAFlD,CADJ;EAMH;;;;MAtRG,OAAO,KAAK7Q,KAAL,CAAW9C,QAAX,IAAuBD,QAAQ,CAACC,QAAvC;IACH;;;EAf0B+T,SAAA;;AC3B/B;AACA,IAAMC,gBAAgB,GAAG,mDAAzB;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAkBA,CAAC1M,EAAD,EAAQ;EAC5B,IAAM2M,KAAK,GAAG,MAAG3M,EAAH,EAAQ2M,KAAR,CAAcF,gBAAd,CAAd;EACA,IAAMhR,IAAI,GAAGkR,KAAK,IAAIA,KAAK,CAAC,CAAD,CAA3B;EACA,OAAOlR,IAAI,IAAI,EAAf;AACH,CAJD;AAMA;;;;;;AAKA,IAAMmR,wBAAwB,GAAG,SAA3BA,wBAA2BA,CAACJ,SAAD,EAAYK,QAAZ;EAAA,IAAYA,QAAZ;IAAYA,QAAZ,GAAuB,EAAvB;EAAA;EAAA,OAC7BL,SAAS,CAACM,WAAV,IAAyBN,SAAS,CAAC/Q,IAAnC,IAA2CiR,eAAe,CAACF,SAAD,CAA1D,IAAyEK,QAD5C;AAAA,CAAjC;AAIA,IAAME,cAAc,GAAG,SAAjBA,cAAiBA,CAACC,SAAD,EAAYC,SAAZ,EAAuBC,WAAvB,EAAuC;EAC1D,IAAMC,YAAY,GAAGP,wBAAwB,CAACK,SAAD,CAA7C;EACA,OACID,SAAS,CAACF,WAAV,KAA0BK,YAAY,KAAK,EAAjB,GAAyBD,WAAzB,SAAwCC,YAAxC,SAA0DD,WAApF,CADJ;AAGH,CALD;AAOA;;;;;AAIA,IAAME,UAAU,GAAG,SAAbA,UAAaA,CAAA,EAAM;EACrB,IAAMC,SAAS,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,OAAxD;EACA,OAAOD,SAAS,GAAGA,SAAS,CAAC,mBAAD,CAAZ,GAAoC,MAApD;AACH,CAHD;AAKA;;;;;;;AAMA,IAAAE,cAAA,GAAe,SAAAA,CAACf,SAAD,EAAe;EAC1B,IAAIA,SAAS,IAAI,IAAjB,EAAuB;IACnB,OAAOxO,SAAP;EACH;EAED,IAAI,OAAOwO,SAAP,KAAqB,QAAzB,EAAmC;IAC/B,OAAOA,SAAP;EACH;EAED,IAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;IACjC,OAAOI,wBAAwB,CAACJ,SAAD,EAAY,WAAZ,CAA/B;EACH;EAED,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;IAC/B,QAAQA,SAAS,CAACgB,QAAlB;MACI,KAAKJ,UAAU,EAAf;QACI,OAAOL,cAAc,CAACP,SAAD,EAAYA,SAAS,CAACb,MAAtB,EAA8B,YAA9B,CAArB;MACJ;QACI,OAAO3N,SAAP;IAJR;EAMH;EAED,OAAOA,SAAP;AACH,CAvBD;AClCA,IAAMyP,YAAY,GAAG,SAAfA,YAAeA,CAACjB,SAAD,EAAe;EAChC,IAAMkB,gBAAgB,GAAG3W,cAAK,CAACyF,UAAN,CAAiB,UAACjB,KAAD,EAAQmB,GAAR;IAAA,OACtC3F,cAAA,CAAA8F,aAAA,CAAC/F,eAAD,CAAiB6W,QAAjB,QACK,UAAAC,OAAO;MAAA,OACJ7W,cAAA,CAAA8F,aAAA,CAAC2P,SAAD,EAAAnS,QAAA,KACQkB,KADR;QAEImB,GAAG,EAAEA,GAFT;QAGIoM,eAAe,EAAE8E,OAAO,CAAC9E,eAH7B;QAIImC,aAAa,EAAE2C,OAAO,CAAC3C;MAJ3B,GADI;IAAA,CADZ,CADsC;EAAA,CAAjB,CAAzB;EAaA,IAAItM,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;IACvC6O,gBAAgB,CAACZ,WAAjB,qBAA+CS,cAAc,CAACf,SAAD,CAA7D;EACH;EAEDqB,oBAAoB,CAACH,gBAAD,EAAmBlB,SAAnB,CAApB;EAEA,OAAOkB,gBAAP;AACH,CArBD;ACDA,IAAAI,WAAA,GAAe,SAAAA,CAAA;EAAA,OAAuBC,UAAU,CAACjX,eAAD,CAAjC;AAAA,CAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}