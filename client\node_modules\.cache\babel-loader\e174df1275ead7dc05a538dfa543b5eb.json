{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\dashboardAdmin\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport AdminLayout from \"../layout\";\nimport DashboardCard from \"./DashboardCard\";\nimport Customize from \"./Customize\";\nimport { dashboardState, dashboardReducer } from \"./DashboardContext\";\nimport TodaySell from \"./TodaySell\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DashboardContext = /*#__PURE__*/createContext();\nconst DashboardComponent = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(DashboardCard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Customize, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TodaySell, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = DashboardComponent;\nconst DashboardAdmin = props => {\n  _s();\n  const [data, dispatch] = useReducer(dashboardReducer, dashboardState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(DashboardContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n        children: /*#__PURE__*/_jsxDEV(DashboardComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardAdmin, \"ltc6yAQDxLHk6B85rF0HACTOD0I=\");\n_c2 = DashboardAdmin;\nexport default DashboardAdmin;\nvar _c, _c2;\n$RefreshReg$(_c, \"DashboardComponent\");\n$RefreshReg$(_c2, \"DashboardAdmin\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "AdminLayout", "DashboardCard", "Customize", "dashboardState", "dashboardReducer", "TodaySell", "jsxDEV", "_jsxDEV", "DashboardContext", "DashboardComponent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "DashboardAdmin", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/dashboardAdmin/index.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport AdminLayout from \"../layout\";\r\nimport DashboardCard from \"./DashboardCard\";\r\nimport Customize from \"./Customize\";\r\nimport { dashboardState, dashboardReducer } from \"./DashboardContext\";\r\nimport TodaySell from \"./TodaySell\";\r\n\r\nexport const DashboardContext = createContext();\r\n\r\nconst DashboardComponent = () => {\r\n  return (\r\n    <Fragment>\r\n      <DashboardCard />\r\n      <Customize />\r\n      <TodaySell />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst DashboardAdmin = (props) => {\r\n  const [data, dispatch] = useReducer(dashboardReducer, dashboardState);\r\n  return (\r\n    <Fragment>\r\n      <DashboardContext.Provider value={{ data, dispatch }}>\r\n        <AdminLayout children={<DashboardComponent />} />\r\n      </DashboardContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default DashboardAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,oBAAoB;AACrE,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,gBAAgB,gBAAGV,aAAa,CAAC,CAAC;AAE/C,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACEF,OAAA,CAACV,QAAQ;IAAAa,QAAA,gBACPH,OAAA,CAACN,aAAa;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBP,OAAA,CAACL,SAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbP,OAAA,CAACF,SAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACC,EAAA,GARIN,kBAAkB;AAUxB,MAAMO,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGrB,UAAU,CAACK,gBAAgB,EAAED,cAAc,CAAC;EACrE,oBACEI,OAAA,CAACV,QAAQ;IAAAa,QAAA,eACPH,OAAA,CAACC,gBAAgB,CAACa,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAV,QAAA,eACnDH,OAAA,CAACP,WAAW;QAACU,QAAQ,eAAEH,OAAA,CAACE,kBAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEf,CAAC;AAACI,EAAA,CATIF,cAAc;AAAAO,GAAA,GAAdP,cAAc;AAWpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}