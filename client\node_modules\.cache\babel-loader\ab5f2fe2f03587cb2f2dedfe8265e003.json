{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\UpdateOrderModal.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext, useState, useEffect } from \"react\";\nimport { OrderContext } from \"./index\";\nimport { getAllOrders, updateOrderStatus } from \"./FetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UpdateOrderModal = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(OrderContext);\n  const [status, setStatus] = useState(\"\");\n  const [oId, setOid] = useState(\"\");\n  useEffect(() => {\n    setOid(data.updateOrderModal.oId);\n    setStatus(data.updateOrderModal.status);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [data.updateOrderModal.modal]);\n  const fetchData = async () => {\n    // Pass the current filter status if needed, or undefined for all\n    let responseData = await getAllOrders();\n    if (responseData && Array.isArray(responseData)) {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData\n      });\n    }\n  };\n  const submitForm = async () => {\n    dispatch({\n      type: \"loading\",\n      payload: true\n    });\n    let responseData = await updateOrderStatus(oId, status);\n    if (responseData.error) {\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    } else if (responseData.success) {\n      console.log(responseData.success);\n      dispatch({\n        type: \"updateOrderModalClose\"\n      });\n      fetchData();\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: e => dispatch({\n        type: \"updateOrderModalClose\"\n      }),\n      className: `${data.updateOrderModal.modal ? \"\" : \"hidden\"} fixed top-0 left-0 z-30 w-full h-full bg-black opacity-50`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${data.updateOrderModal.modal ? \"\" : \"hidden\"} fixed inset-0 m-4  flex items-center z-30 justify-center`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative bg-white w-11/12 md:w-3/6 shadow-lg flex flex-col items-center space-y-4  overflow-y-auto px-4 py-4 md:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between w-full pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-left font-semibold text-2xl tracking-wider\",\n            children: \"Update Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: \"#303031\"\n            },\n            onClick: e => dispatch({\n              type: \"updateOrderModalClose\"\n            }),\n            className: \"cursor-pointer text-gray-100 py-2 px-2 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-1 w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"status\",\n            children: \"Order Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: status,\n            name: \"status\",\n            onChange: e => setStatus(e.target.value),\n            className: \"px-4 py-2 border focus:outline-none\",\n            id: \"status\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"PENDING\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CONFIRMED\",\n              children: \"Confirmed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"REJECTED\",\n              children: \"Rejected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"SHIPPED\",\n              children: \"Shipped\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"DELIVERED\",\n              children: \"Delivered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-1 w-full pb-4 md:pb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: \"#303031\"\n            },\n            onClick: submitForm,\n            className: \"rounded-full bg-gray-800 text-gray-100 text-lg font-medium py-2\",\n            children: \"Update Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(UpdateOrderModal, \"Yc5luI3hH4+iZ5ojrhPf5Dqe1ow=\");\n_c = UpdateOrderModal;\nexport default UpdateOrderModal;\nvar _c;\n$RefreshReg$(_c, \"UpdateOrderModal\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useState", "useEffect", "OrderContext", "getAllOrders", "updateOrderStatus", "jsxDEV", "_jsxDEV", "UpdateOrderModal", "props", "_s", "data", "dispatch", "status", "setStatus", "oId", "setOid", "updateOrderModal", "modal", "fetchData", "responseData", "Array", "isArray", "type", "payload", "submitForm", "error", "success", "console", "log", "children", "onClick", "e", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "htmlFor", "value", "name", "onChange", "target", "id", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/UpdateOrderModal.js"], "sourcesContent": ["import React, { Fragment, useContext, useState, useEffect } from \"react\";\r\nimport { OrderContext } from \"./index\";\r\nimport { getAllOrders, updateOrderStatus } from \"./FetchApi\";\r\n\r\nconst UpdateOrderModal = (props) => {\r\n  const { data, dispatch } = useContext(OrderContext);\r\n\r\n  const [status, setStatus] = useState(\"\");\r\n  const [oId, setOid] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    setOid(data.updateOrderModal.oId);\r\n    setStatus(data.updateOrderModal.status);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [data.updateOrderModal.modal]);\r\n\r\n  const fetchData = async () => {\r\n    // Pass the current filter status if needed, or undefined for all\r\n    let responseData = await getAllOrders();\r\n    if (responseData && Array.isArray(responseData)) {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData,\r\n      });\r\n    }\r\n  };\r\n\r\n  const submitForm = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    let responseData = await updateOrderStatus(oId, status);\r\n    if (responseData.error) {\r\n      dispatch({ type: \"loading\", payload: false });\r\n    } else if (responseData.success) {\r\n      console.log(responseData.success);\r\n      dispatch({ type: \"updateOrderModalClose\" });\r\n      fetchData();\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Fragment>\r\n      {/* Black Overlay */}\r\n      <div\r\n        onClick={(e) => dispatch({ type: \"updateOrderModalClose\" })}\r\n        className={`${\r\n          data.updateOrderModal.modal ? \"\" : \"hidden\"\r\n        } fixed top-0 left-0 z-30 w-full h-full bg-black opacity-50`}\r\n      />\r\n      {/* End Black Overlay */}\r\n\r\n      {/* Modal Start */}\r\n      <div\r\n        className={`${\r\n          data.updateOrderModal.modal ? \"\" : \"hidden\"\r\n        } fixed inset-0 m-4  flex items-center z-30 justify-center`}\r\n      >\r\n        <div className=\"relative bg-white w-11/12 md:w-3/6 shadow-lg flex flex-col items-center space-y-4  overflow-y-auto px-4 py-4 md:px-8\">\r\n          <div className=\"flex items-center justify-between w-full pt-4\">\r\n            <span className=\"text-left font-semibold text-2xl tracking-wider\">\r\n              Update Order\r\n            </span>\r\n            {/* Close Modal */}\r\n            <span\r\n              style={{ background: \"#303031\" }}\r\n              onClick={(e) => dispatch({ type: \"updateOrderModalClose\" })}\r\n              className=\"cursor-pointer text-gray-100 py-2 px-2 rounded-full\"\r\n            >\r\n              <svg\r\n                className=\"w-6 h-6\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M6 18L18 6M6 6l12 12\"\r\n                />\r\n              </svg>\r\n            </span>\r\n          </div>\r\n          <div className=\"flex flex-col space-y-1 w-full\">\r\n            <label htmlFor=\"status\">Order Status</label>\r\n            <select\r\n              value={status}\r\n              name=\"status\"\r\n              onChange={(e) => setStatus(e.target.value)}\r\n              className=\"px-4 py-2 border focus:outline-none\"\r\n              id=\"status\"\r\n            >\r\n              <option value=\"PENDING\">Pending</option>\r\n              <option value=\"CONFIRMED\">Confirmed</option>\r\n              <option value=\"REJECTED\">Rejected</option>\r\n              <option value=\"SHIPPED\">Shipped</option>\r\n              <option value=\"DELIVERED\">Delivered</option>\r\n            </select>\r\n          </div>\r\n          <div className=\"flex flex-col space-y-1 w-full pb-4 md:pb-6\">\r\n            <button\r\n              style={{ background: \"#303031\" }}\r\n              onClick={submitForm}\r\n              className=\"rounded-full bg-gray-800 text-gray-100 text-lg font-medium py-2\"\r\n            >\r\n              Update Order\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default UpdateOrderModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACxE,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGZ,UAAU,CAACG,YAAY,CAAC;EAEnD,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACc,GAAG,EAAEC,MAAM,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAElCC,SAAS,CAAC,MAAM;IACdc,MAAM,CAACL,IAAI,CAACM,gBAAgB,CAACF,GAAG,CAAC;IACjCD,SAAS,CAACH,IAAI,CAACM,gBAAgB,CAACJ,MAAM,CAAC;IACvC;EACF,CAAC,EAAE,CAACF,IAAI,CAACM,gBAAgB,CAACC,KAAK,CAAC,CAAC;EAEjC,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B;IACA,IAAIC,YAAY,GAAG,MAAMhB,YAAY,CAAC,CAAC;IACvC,IAAIgB,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;MAC/CR,QAAQ,CAAC;QACPW,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEJ;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bb,QAAQ,CAAC;MAAEW,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5C,IAAIJ,YAAY,GAAG,MAAMf,iBAAiB,CAACU,GAAG,EAAEF,MAAM,CAAC;IACvD,IAAIO,YAAY,CAACM,KAAK,EAAE;MACtBd,QAAQ,CAAC;QAAEW,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIJ,YAAY,CAACO,OAAO,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAACT,YAAY,CAACO,OAAO,CAAC;MACjCf,QAAQ,CAAC;QAAEW,IAAI,EAAE;MAAwB,CAAC,CAAC;MAC3CJ,SAAS,CAAC,CAAC;MACXP,QAAQ,CAAC;QAAEW,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC;EAED,oBACEjB,OAAA,CAACR,QAAQ;IAAA+B,QAAA,gBAEPvB,OAAA;MACEwB,OAAO,EAAGC,CAAC,IAAKpB,QAAQ,CAAC;QAAEW,IAAI,EAAE;MAAwB,CAAC,CAAE;MAC5DU,SAAS,EAAE,GACTtB,IAAI,CAACM,gBAAgB,CAACC,KAAK,GAAG,EAAE,GAAG,QAAQ;IACgB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAIF9B,OAAA;MACE0B,SAAS,EAAE,GACTtB,IAAI,CAACM,gBAAgB,CAACC,KAAK,GAAG,EAAE,GAAG,QAAQ,2DACe;MAAAY,QAAA,eAE5DvB,OAAA;QAAK0B,SAAS,EAAC,sHAAsH;QAAAH,QAAA,gBACnIvB,OAAA;UAAK0B,SAAS,EAAC,+CAA+C;UAAAH,QAAA,gBAC5DvB,OAAA;YAAM0B,SAAS,EAAC,iDAAiD;YAAAH,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP9B,OAAA;YACE+B,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCR,OAAO,EAAGC,CAAC,IAAKpB,QAAQ,CAAC;cAAEW,IAAI,EAAE;YAAwB,CAAC,CAAE;YAC5DU,SAAS,EAAC,qDAAqD;YAAAH,QAAA,eAE/DvB,OAAA;cACE0B,SAAS,EAAC,SAAS;cACnBO,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,4BAA4B;cAAAb,QAAA,eAElCvB,OAAA;gBACEqC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAsB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9B,OAAA;UAAK0B,SAAS,EAAC,gCAAgC;UAAAH,QAAA,gBAC7CvB,OAAA;YAAOyC,OAAO,EAAC,QAAQ;YAAAlB,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C9B,OAAA;YACE0C,KAAK,EAAEpC,MAAO;YACdqC,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAGnB,CAAC,IAAKlB,SAAS,CAACkB,CAAC,CAACoB,MAAM,CAACH,KAAK,CAAE;YAC3ChB,SAAS,EAAC,qCAAqC;YAC/CoB,EAAE,EAAC,QAAQ;YAAAvB,QAAA,gBAEXvB,OAAA;cAAQ0C,KAAK,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9B,OAAA;cAAQ0C,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C9B,OAAA;cAAQ0C,KAAK,EAAC,UAAU;cAAAnB,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C9B,OAAA;cAAQ0C,KAAK,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9B,OAAA;cAAQ0C,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9B,OAAA;UAAK0B,SAAS,EAAC,6CAA6C;UAAAH,QAAA,eAC1DvB,OAAA;YACE+B,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCR,OAAO,EAAEN,UAAW;YACpBQ,SAAS,EAAC,iEAAiE;YAAAH,QAAA,EAC5E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC3B,EAAA,CA7GIF,gBAAgB;AAAA8C,EAAA,GAAhB9C,gBAAgB;AA+GtB,eAAeA,gBAAgB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}