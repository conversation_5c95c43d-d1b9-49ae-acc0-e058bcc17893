{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\productDetails\\\\Details.js\";\nimport React, { Fragment } from \"react\";\nimport ProductDetailsSection from \"./ProductDetailsSection\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Details = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ProductDetailsSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Details;\nexport default Details;\nvar _c;\n$RefreshReg$(_c, \"Details\");", "map": {"version": 3, "names": ["React", "Fragment", "ProductDetailsSection", "jsxDEV", "_jsxDEV", "Details", "props", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/Details.js"], "sourcesContent": ["import React, { Fragment } from \"react\";\r\nimport ProductDetailsSection from \"./ProductDetailsSection\";\r\n\r\nconst Details = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <ProductDetailsSection />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Details;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,qBAAqB,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,OAAO,GAAIC,KAAK,IAAK;EACzB,oBACEF,OAAA,CAACH,QAAQ;IAAAM,QAAA,eACPH,OAAA,CAACF,qBAAqB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEf,CAAC;AAACC,EAAA,GANIP,OAAO;AAQb,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}