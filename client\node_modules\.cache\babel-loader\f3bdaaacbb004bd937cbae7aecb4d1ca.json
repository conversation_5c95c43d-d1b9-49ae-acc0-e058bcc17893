{"ast": null, "code": "export const orderState = {\n  orders: [],\n  addCategoryModal: false,\n  updateOrderModal: {\n    modal: false,\n    oId: null,\n    status: \"\"\n  },\n  loading: false\n};\nexport const orderReducer = (state, action) => {\n  switch (action.type) {\n    /* Get all category */\n    case \"fetchOrderAndChangeState\":\n      return {\n        ...state,\n        orders: action.payload\n      };\n    /* Create a category */\n    case \"addCategoryModal\":\n      return {\n        ...state,\n        addCategoryModal: action.payload\n      };\n    /* Edit a category */\n    case \"updateOrderModalOpen\":\n      return {\n        ...state,\n        updateOrderModal: {\n          modal: true,\n          oId: action.oId,\n          status: action.status\n        }\n      };\n    case \"updateOrderModalClose\":\n      return {\n        ...state,\n        updateOrderModal: {\n          modal: false,\n          oId: null,\n          status: \"\"\n        }\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["orderState", "orders", "addCategoryModal", "updateOrderModal", "modal", "oId", "status", "loading", "orderReducer", "state", "action", "type", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/OrderContext.js"], "sourcesContent": ["export const orderState = {\r\n  orders: [],\r\n  addCategoryModal: false,\r\n  updateOrderModal: {\r\n    modal: false,\r\n    oId: null,\r\n    status: \"\",\r\n  },\r\n  loading: false,\r\n};\r\n\r\nexport const orderReducer = (state, action) => {\r\n  switch (action.type) {\r\n    /* Get all category */\r\n    case \"fetchOrderAndChangeState\":\r\n      return {\r\n        ...state,\r\n        orders: action.payload,\r\n      };\r\n    /* Create a category */\r\n    case \"addCategoryModal\":\r\n      return {\r\n        ...state,\r\n        addCategoryModal: action.payload,\r\n      };\r\n    /* Edit a category */\r\n    case \"updateOrderModalOpen\":\r\n      return {\r\n        ...state,\r\n        updateOrderModal: {\r\n          modal: true,\r\n          oId: action.oId,\r\n          status: action.status,\r\n        },\r\n      };\r\n    case \"updateOrderModalClose\":\r\n      return {\r\n        ...state,\r\n        updateOrderModal: {\r\n          modal: false,\r\n          oId: null,\r\n          status: \"\",\r\n        },\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG;EACxBC,MAAM,EAAE,EAAE;EACVC,gBAAgB,EAAE,KAAK;EACvBC,gBAAgB,EAAE;IAChBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC7C,QAAQA,MAAM,CAACC,IAAI;IACjB;IACA,KAAK,0BAA0B;MAC7B,OAAO;QACL,GAAGF,KAAK;QACRR,MAAM,EAAES,MAAM,CAACE;MACjB,CAAC;IACH;IACA,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGH,KAAK;QACRP,gBAAgB,EAAEQ,MAAM,CAACE;MAC3B,CAAC;IACH;IACA,KAAK,sBAAsB;MACzB,OAAO;QACL,GAAGH,KAAK;QACRN,gBAAgB,EAAE;UAChBC,KAAK,EAAE,IAAI;UACXC,GAAG,EAAEK,MAAM,CAACL,GAAG;UACfC,MAAM,EAAEI,MAAM,CAACJ;QACjB;MACF,CAAC;IACH,KAAK,uBAAuB;MAC1B,OAAO;QACL,GAAGG,KAAK;QACRN,gBAAgB,EAAE;UAChBC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE;QACV;MACF,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGG,KAAK;QACRF,OAAO,EAAEG,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}