{"ast": null, "code": "export const productState = {\n  products: null,\n  addProductModal: false,\n  editProductModal: {\n    modal: false,\n    pId: \"\",\n    pName: \"\",\n    pDescription: \"\",\n    pImages: null,\n    pStatus: \"\",\n    pCategory: \"\",\n    pQuantity: \"\",\n    pPrice: \"\",\n    pOffer: \"\"\n  }\n};\nexport const productReducer = (state, action) => {\n  switch (action.type) {\n    /* Get all product */\n    case \"fetchProductsAndChangeState\":\n      return {\n        ...state,\n        products: action.payload\n      };\n    /* Create a product */\n    case \"addProductModal\":\n      return {\n        ...state,\n        addProductModal: action.payload\n      };\n    /* Edit a product */\n    case \"editProductModalOpen\":\n      return {\n        ...state,\n        editProductModal: {\n          modal: true,\n          pId: action.product.pId,\n          pName: action.product.pName,\n          pDescription: action.product.pDescription,\n          pImages: action.product.pImages,\n          pStatus: action.product.pStatus,\n          pCategory: action.product.pCategory,\n          pQuantity: action.product.pQuantity,\n          pPrice: action.product.pPrice,\n          pOffer: action.product.pOffer\n        }\n      };\n    case \"editProductModalClose\":\n      return {\n        ...state,\n        editProductModal: {\n          modal: false,\n          pId: \"\",\n          pName: \"\",\n          pDescription: \"\",\n          pImages: null,\n          pStatus: \"\",\n          pCategory: \"\",\n          pQuantity: \"\",\n          pPrice: \"\",\n          pOffer: \"\"\n        }\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["productState", "products", "addProductModal", "editProductModal", "modal", "pId", "pName", "pDescription", "pImages", "pStatus", "pCategory", "pQuantity", "pPrice", "pOffer", "productReducer", "state", "action", "type", "payload", "product"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/ProductContext.js"], "sourcesContent": ["export const productState = {\r\n  products: null,\r\n  addProductModal: false,\r\n  editProductModal: {\r\n    modal: false,\r\n    pId: \"\",\r\n    pName: \"\",\r\n    pDescription: \"\",\r\n    pImages: null,\r\n    pStatus: \"\",\r\n    pCategory: \"\",\r\n    pQuantity: \"\",\r\n    pPrice: \"\",\r\n    pOffer: \"\",\r\n  },\r\n};\r\n\r\nexport const productReducer = (state, action) => {\r\n  switch (action.type) {\r\n    /* Get all product */\r\n    case \"fetchProductsAndChangeState\":\r\n      return {\r\n        ...state,\r\n        products: action.payload,\r\n      };\r\n    /* Create a product */\r\n    case \"addProductModal\":\r\n      return {\r\n        ...state,\r\n        addProductModal: action.payload,\r\n      };\r\n    /* Edit a product */\r\n    case \"editProductModalOpen\":\r\n      return {\r\n        ...state,\r\n        editProductModal: {\r\n          modal: true,\r\n          pId: action.product.pId,\r\n          pName: action.product.pName,\r\n          pDescription: action.product.pDescription,\r\n          pImages: action.product.pImages,\r\n          pStatus: action.product.pStatus,\r\n          pCategory: action.product.pCategory,\r\n          pQuantity: action.product.pQuantity,\r\n          pPrice: action.product.pPrice,\r\n          pOffer: action.product.pOffer,\r\n        },\r\n      };\r\n    case \"editProductModalClose\":\r\n      return {\r\n        ...state,\r\n        editProductModal: {\r\n          modal: false,\r\n          pId: \"\",\r\n          pName: \"\",\r\n          pDescription: \"\",\r\n          pImages: null,\r\n          pStatus: \"\",\r\n          pCategory: \"\",\r\n          pQuantity: \"\",\r\n          pPrice: \"\",\r\n          pOffer: \"\",\r\n        },\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAG;EAC1BC,QAAQ,EAAE,IAAI;EACdC,eAAe,EAAE,KAAK;EACtBC,gBAAgB,EAAE;IAChBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACV;AACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC/C,QAAQA,MAAM,CAACC,IAAI;IACjB;IACA,KAAK,6BAA6B;MAChC,OAAO;QACL,GAAGF,KAAK;QACRd,QAAQ,EAAEe,MAAM,CAACE;MACnB,CAAC;IACH;IACA,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGH,KAAK;QACRb,eAAe,EAAEc,MAAM,CAACE;MAC1B,CAAC;IACH;IACA,KAAK,sBAAsB;MACzB,OAAO;QACL,GAAGH,KAAK;QACRZ,gBAAgB,EAAE;UAChBC,KAAK,EAAE,IAAI;UACXC,GAAG,EAAEW,MAAM,CAACG,OAAO,CAACd,GAAG;UACvBC,KAAK,EAAEU,MAAM,CAACG,OAAO,CAACb,KAAK;UAC3BC,YAAY,EAAES,MAAM,CAACG,OAAO,CAACZ,YAAY;UACzCC,OAAO,EAAEQ,MAAM,CAACG,OAAO,CAACX,OAAO;UAC/BC,OAAO,EAAEO,MAAM,CAACG,OAAO,CAACV,OAAO;UAC/BC,SAAS,EAAEM,MAAM,CAACG,OAAO,CAACT,SAAS;UACnCC,SAAS,EAAEK,MAAM,CAACG,OAAO,CAACR,SAAS;UACnCC,MAAM,EAAEI,MAAM,CAACG,OAAO,CAACP,MAAM;UAC7BC,MAAM,EAAEG,MAAM,CAACG,OAAO,CAACN;QACzB;MACF,CAAC;IACH,KAAK,uBAAuB;MAC1B,OAAO;QACL,GAAGE,KAAK;QACRZ,gBAAgB,EAAE;UAChBC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,EAAE;UACbC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE;QACV;MACF,CAAC;IACH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}