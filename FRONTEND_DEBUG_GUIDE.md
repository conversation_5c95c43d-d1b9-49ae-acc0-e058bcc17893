# 🔧 Frontend Debug Guide - Tại sao không hiển thị sản phẩm?

## 🚨 Vấn đề đã sửa
✅ **Port mismatch**: Đã sửa `.env` từ port 8000 → 8080

## 🧪 Các bước debug

### 1. **Kiểm tra Backend**
```bash
# Test API trực tiếp
curl http://localhost:8080/api/v1/products/customer

# Hoặc mở browser:
http://localhost:8080/api/v1/products/customer
```

**Kết quả mong đợi:** JSON array với 18 sản phẩm

### 2. **Kiểm tra Frontend Config**
```bash
# Kiểm tra file .env
cat client/.env
```

**Phải có:** `REACT_APP_API_URL=http://localhost:8080`

### 3. **Test API từ Browser**
Mở: `http://localhost:3000/test-api.html`

Trang này sẽ test trực tiếp API connection và hiển thị kết quả.

### 4. **Kiểm tra Console Logs**
1. Mở trang chủ: `http://localhost:3000`
2. Nhấn F12 → Console tab
3. Tìm các log messages:
   - `🔍 SingleProduct: Starting fetchData...`
   - `🌐 FetchApi: Making request to...`
   - `📦 FetchApi: Response data:`

### 5. **Kiểm tra Network Tab**
1. F12 → Network tab
2. Reload trang
3. Tìm request đến `/api/v1/products/customer`
4. Kiểm tra:
   - Status code (phải là 200)
   - Response data
   - CORS headers

## 🔍 Các lỗi thường gặp

### ❌ **Backend không chạy**
**Triệu chứng:** Network error, connection refused
**Giải pháp:**
```bash
cd backend
./mvnw spring-boot:run
```

### ❌ **Port sai**
**Triệu chứng:** 404 Not Found
**Kiểm tra:** File `.env` phải có port 8080

### ❌ **CORS Error**
**Triệu chứng:** CORS policy error trong console
**Giải pháp:** Backend đã cấu hình CORS cho localhost:3000

### ❌ **Database không có dữ liệu**
**Triệu chứng:** API trả về array rỗng `[]`
**Kiểm tra:**
```sql
-- Kết nối PostgreSQL và check
SELECT COUNT(*) FROM product;
```

### ❌ **Component không render**
**Triệu chứng:** Trang trắng, không có lỗi
**Kiểm tra:** Console có JavaScript errors không

## 🛠️ Debug Tools đã thêm

### 1. **Logging trong FetchApi.js**
- Log API URL
- Log request/response
- Log errors chi tiết

### 2. **Logging trong SingleProduct.js**
- Log fetch process
- Log data validation
- Log component state

### 3. **Debug banner trên trang chủ**
- Hiển thị API URL
- Links đến test pages
- Hướng dẫn debug

### 4. **Test pages**
- `/api-test` - React component test
- `/test-api.html` - Pure JavaScript test

## 🚀 Cách chạy để debug

### 1. **Start Backend**
```bash
cd backend
./mvnw spring-boot:run
```
Đợi thấy: "Started EcommerceApplication"

### 2. **Start Frontend**
```bash
cd client
npm start
```

### 3. **Test từng bước**
1. **Test backend:** `http://localhost:8080/api/v1/products/customer`
2. **Test frontend API:** `http://localhost:3000/test-api.html`
3. **Test React app:** `http://localhost:3000/api-test`
4. **Test trang chủ:** `http://localhost:3000`

## 📊 Expected Results

### ✅ **Backend API Response:**
```json
[
  {
    "productId": 1,
    "category": "Electronics",
    "name": "Smartphone X",
    "description": "Latest smartphone model",
    "price": 17500000,
    "images": ["http://example.com/image1.jpg"],
    "availabilityStatus": "AVAILABLE"
  },
  ...
]
```

### ✅ **Frontend Console Logs:**
```
🔍 SingleProduct: Starting fetchData...
🌐 FetchApi: Making request to: http://localhost:8080/api/v1/products/customer
📦 FetchApi: Response data: [Array of 18 products]
✅ SingleProduct: Setting products, count: 18
```

### ✅ **Trang chủ hiển thị:**
- Debug banner với API URL
- Grid 18 sản phẩm
- Mỗi sản phẩm có: tên, giá, hình ảnh

## 🎯 Troubleshooting Checklist

- [ ] Backend chạy tại port 8080
- [ ] Database có 18 sản phẩm
- [ ] File `.env` có đúng port 8080
- [ ] Không có CORS errors
- [ ] Console không có JavaScript errors
- [ ] Network tab thấy API call thành công
- [ ] API response có dữ liệu
- [ ] Component SingleProduct render

## 📞 Nếu vẫn không hoạt động

1. **Restart cả backend và frontend**
2. **Clear browser cache** (Ctrl+Shift+R)
3. **Check database connection** trong backend logs
4. **Test với Postman/curl** để isolate vấn đề
5. **Check firewall/antivirus** có block port không

## 🎉 Success Indicators

Khi mọi thứ hoạt động:
- ✅ Trang chủ hiển thị 18 sản phẩm
- ✅ Console có debug logs
- ✅ Network tab thấy 200 OK
- ✅ Có thể click vào sản phẩm
- ✅ Search hoạt động
