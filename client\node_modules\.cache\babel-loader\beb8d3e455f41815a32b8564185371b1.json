{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\products\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport AdminLayout from \"../layout\";\nimport ProductMenu from \"./ProductMenu\";\nimport ProductTable from \"./ProductTable\";\nimport { productState, productReducer } from \"./ProductContext\";\n\n/* This context manage all of the products component's data */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ProductContext = /*#__PURE__*/createContext();\nconst ProductComponent = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 space-y-4 p-4\",\n    children: [/*#__PURE__*/_jsxDEV(ProductMenu, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductTable, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductComponent;\nconst Products = props => {\n  _s();\n  /* To use useReducer make sure that reducer is the first arg */\n  const [data, dispatch] = useReducer(productReducer, productState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ProductContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n        children: /*#__PURE__*/_jsxDEV(ProductComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"2vahxWAk4rFvx580JPbjuSbfkyE=\");\n_c2 = Products;\nexport default Products;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProductComponent\");\n$RefreshReg$(_c2, \"Products\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "AdminLayout", "ProductMenu", "ProductTable", "productState", "productReducer", "jsxDEV", "_jsxDEV", "ProductContext", "ProductComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Products", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/index.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport AdminLayout from \"../layout\";\r\nimport ProductMenu from \"./ProductMenu\";\r\nimport ProductTable from \"./ProductTable\";\r\nimport { productState, productReducer } from \"./ProductContext\";\r\n\r\n/* This context manage all of the products component's data */\r\nexport const ProductContext = createContext();\r\n\r\nconst ProductComponent = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 space-y-4 p-4\">\r\n      <ProductMenu />\r\n      <ProductTable />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Products = (props) => {\r\n  /* To use useReducer make sure that reducer is the first arg */\r\n  const [data, dispatch] = useReducer(productReducer, productState);\r\n\r\n  return (\r\n    <Fragment>\r\n      <ProductContext.Provider value={{ data, dispatch }}>\r\n        <AdminLayout children={<ProductComponent />} />\r\n      </ProductContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Products;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,YAAY,EAAEC,cAAc,QAAQ,kBAAkB;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,cAAc,gBAAGT,aAAa,CAAC,CAAC;AAE7C,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACEF,OAAA;IAAKG,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7CJ,OAAA,CAACL,WAAW;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfR,OAAA,CAACJ,YAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIP,gBAAgB;AAStB,MAAMQ,QAAQ,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC1B;EACA,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGrB,UAAU,CAACK,cAAc,EAAED,YAAY,CAAC;EAEjE,oBACEG,OAAA,CAACT,QAAQ;IAAAa,QAAA,eACPJ,OAAA,CAACC,cAAc,CAACc,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAV,QAAA,eACjDJ,OAAA,CAACN,WAAW;QAACU,QAAQ,eAAEJ,OAAA,CAACE,gBAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEf,CAAC;AAACI,EAAA,CAXIF,QAAQ;AAAAO,GAAA,GAARP,QAAQ;AAad,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}