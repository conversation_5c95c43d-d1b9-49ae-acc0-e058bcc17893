{"ast": null, "code": "export const productDetailsState = {\n  loading: false,\n  menu: true\n};\nexport const productDetailsReducer = (state, action) => {\n  switch (action.type) {\n    case \"menu\":\n      return {\n        ...state,\n        menu: action.payload\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    case \"cartState\":\n      return {\n        ...state,\n        cartState: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["productDetailsState", "loading", "menu", "productDetailsReducer", "state", "action", "type", "payload", "cartState"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/ProductDetailsContext.js"], "sourcesContent": ["export const productDetailsState = {\r\n  loading: false,\r\n  menu: true,\r\n};\r\n\r\nexport const productDetailsReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"menu\":\r\n      return {\r\n        ...state,\r\n        menu: action.payload,\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    case \"cartState\":\r\n      return {\r\n        ...state,\r\n        cartState: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG;EACjCC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE;AACR,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACtD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,MAAM;MACT,OAAO;QACL,GAAGF,KAAK;QACRF,IAAI,EAAEG,MAAM,CAACE;MACf,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGH,KAAK;QACRH,OAAO,EAAEI,MAAM,CAACE;MAClB,CAAC;IACH,KAAK,WAAW;MACd,OAAO;QACL,GAAGH,KAAK;QACRI,SAAS,EAAEH,MAAM,CAACE;MACpB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}