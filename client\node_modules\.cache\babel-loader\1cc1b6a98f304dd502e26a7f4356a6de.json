{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"breakpoints\", \"mixins\", \"spacing\", \"palette\", \"transitions\", \"typography\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from './createMixins';\nimport createPalette from './createPalette';\nimport createTypography from './createTypography';\nimport shadows from './shadows';\nimport createTransitions from './createTransitions';\nimport zIndex from './zIndex';\nfunction createTheme(options = {}, ...args) {\n  const {\n      mixins: mixinsInput = {},\n      palette: paletteInput = {},\n      transitions: transitionsInput = {},\n      typography: typographyInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateCssVars` is the closest identifier for checking that the `options` is a result of `extendTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateCssVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: \\`vars\\` is a private field used for CSS variables support.\nPlease use another name.` : _formatMuiErrorMessage(18));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: _extends({}, zIndex)\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in, no-restricted-syntax\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.indexOf(key) !== -1 && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.indexOf('Mui') === 0) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme(...args) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createTheme(...args);\n}\nexport default createTheme;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_formatMuiErrorMessage", "_excluded", "deepmerge", "styleFunctionSx", "unstable_defaultSxConfig", "defaultSxConfig", "systemCreateTheme", "generateUtilityClass", "createMixins", "createPalette", "createTypography", "shadows", "createTransitions", "zIndex", "createTheme", "options", "args", "mixins", "mixinsInput", "palette", "paletteInput", "transitions", "transitionsInput", "typography", "typographyInput", "other", "vars", "generateCssVars", "undefined", "Error", "process", "env", "NODE_ENV", "systemTheme", "muiTheme", "breakpoints", "slice", "reduce", "acc", "argument", "stateClasses", "traverse", "node", "component", "key", "child", "indexOf", "Object", "keys", "length", "stateClass", "console", "error", "JSON", "stringify", "root", "join", "components", "for<PERSON>ach", "styleOverrides", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme", "warnedOnce", "createMuiTheme"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/styles/createTheme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"breakpoints\", \"mixins\", \"spacing\", \"palette\", \"transitions\", \"typography\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from './createMixins';\nimport createPalette from './createPalette';\nimport createTypography from './createTypography';\nimport shadows from './shadows';\nimport createTransitions from './createTransitions';\nimport zIndex from './zIndex';\nfunction createTheme(options = {}, ...args) {\n  const {\n      mixins: mixinsInput = {},\n      palette: paletteInput = {},\n      transitions: transitionsInput = {},\n      typography: typographyInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateCssVars` is the closest identifier for checking that the `options` is a result of `extendTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateCssVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: \\`vars\\` is a private field used for CSS variables support.\nPlease use another name.` : _formatMuiErrorMessage(18));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: _extends({}, zIndex)\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in, no-restricted-syntax\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.indexOf(key) !== -1 && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.indexOf('Mui') === 0) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme(...args) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createTheme(...args);\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC;AACvG,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,IAAIC,wBAAwB,IAAIC,eAAe,QAAQ,6BAA6B;AAC1G,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAGC,IAAI,EAAE;EAC1C,MAAM;MACFC,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC;MACxBC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;MAC1BC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;MAClCC,UAAU,EAAEC,eAAe,GAAG,CAAC;IACjC,CAAC,GAAGT,OAAO;IACXU,KAAK,GAAG1B,6BAA6B,CAACgB,OAAO,EAAEd,SAAS,CAAC;EAC3D,IAAIc,OAAO,CAACW,IAAI;EAChB;EACA;EACAX,OAAO,CAACY,eAAe,KAAKC,SAAS,EAAE;IACrC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;AAC5D,yBAAyB,GAAGhC,sBAAsB,CAAC,EAAE,CAAC,CAAC;EACrD;EACA,MAAMmB,OAAO,GAAGV,aAAa,CAACW,YAAY,CAAC;EAC3C,MAAMa,WAAW,GAAG3B,iBAAiB,CAACS,OAAO,CAAC;EAC9C,IAAImB,QAAQ,GAAGhC,SAAS,CAAC+B,WAAW,EAAE;IACpChB,MAAM,EAAET,YAAY,CAACyB,WAAW,CAACE,WAAW,EAAEjB,WAAW,CAAC;IAC1DC,OAAO;IACP;IACAR,OAAO,EAAEA,OAAO,CAACyB,KAAK,CAAC,CAAC;IACxBb,UAAU,EAAEb,gBAAgB,CAACS,OAAO,EAAEK,eAAe,CAAC;IACtDH,WAAW,EAAET,iBAAiB,CAACU,gBAAgB,CAAC;IAChDT,MAAM,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEe,MAAM;EAC7B,CAAC,CAAC;EACFqB,QAAQ,GAAGhC,SAAS,CAACgC,QAAQ,EAAET,KAAK,CAAC;EACrCS,QAAQ,GAAGlB,IAAI,CAACqB,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKrC,SAAS,CAACoC,GAAG,EAAEC,QAAQ,CAAC,EAAEL,QAAQ,CAAC;EAC7E,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMQ,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3I,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIC,GAAG;;MAEP;MACA,KAAKA,GAAG,IAAIF,IAAI,EAAE;QAChB,MAAMG,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;QACvB,IAAIJ,YAAY,CAACM,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;UACrE,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,MAAMkB,UAAU,GAAG3C,oBAAoB,CAAC,EAAE,EAAEqC,GAAG,CAAC;YAChDO,OAAO,CAACC,KAAK,CAAC,CAAC,cAAcT,SAAS,yBAAyB,GAAG,gCAAgCC,GAAG,oBAAoB,EAAE,qCAAqC,EAAES,IAAI,CAACC,SAAS,CAACZ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,mCAAmCQ,UAAU,WAAW,EAAEG,IAAI,CAACC,SAAS,CAAC;cAC5QC,IAAI,EAAE;gBACJ,CAAC,KAAKL,UAAU,EAAE,GAAGL;cACvB;YACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;UACA;UACAd,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB;MACF;IACF,CAAC;IACDG,MAAM,CAACC,IAAI,CAACd,QAAQ,CAACuB,UAAU,CAAC,CAACC,OAAO,CAACf,SAAS,IAAI;MACpD,MAAMgB,cAAc,GAAGzB,QAAQ,CAACuB,UAAU,CAACd,SAAS,CAAC,CAACgB,cAAc;MACpE,IAAIA,cAAc,IAAIhB,SAAS,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACpDL,QAAQ,CAACkB,cAAc,EAAEhB,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EACAT,QAAQ,CAAC0B,iBAAiB,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAEO,eAAe,EAAEoB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACmC,iBAAiB,CAAC;EAC5G1B,QAAQ,CAAC2B,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAO5D,eAAe,CAAC;MACrB2D,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAO9B,QAAQ;AACjB;AACA,IAAI+B,UAAU,GAAG,KAAK;AACtB,OAAO,SAASC,cAAcA,CAAC,GAAGlD,IAAI,EAAE;EACtC,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACiC,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI;MACjBd,OAAO,CAACC,KAAK,CAAC,CAAC,8DAA8D,EAAE,EAAE,EAAE,qEAAqE,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;IACvK;EACF;EACA,OAAO1C,WAAW,CAAC,GAAGE,IAAI,CAAC;AAC7B;AACA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}