{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst BearerToken = () => localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : false;\n_c = BearerToken;\nconst Headers = () => {\n  return {\n    headers: {\n      token: `Bearer ${BearerToken()}`\n    }\n  };\n};\n_c2 = Headers;\nexport const getAllCategory = async () => {\n  try {\n    // Backend chưa có CategoryController, sử dụng mock data\n    const mockCategories = [{\n      _id: 'electronics',\n      cName: 'Electronics',\n      cImage: 'electronics.jpg',\n      cDescription: 'Electronic devices and gadgets',\n      cStatus: 'Active'\n    }, {\n      _id: 'fashion',\n      cName: 'Fashion',\n      cImage: 'fashion.jpg',\n      cDescription: 'Clothing and accessories',\n      cStatus: 'Active'\n    }, {\n      _id: 'beauty',\n      cName: 'Beauty',\n      cImage: 'beauty.jpg',\n      cDescription: 'Beauty and cosmetic products',\n      cStatus: 'Active'\n    }, {\n      _id: 'furniture',\n      cName: 'Furniture',\n      cImage: 'furniture.jpg',\n      cDescription: 'Home and office furniture',\n      cStatus: 'Active'\n    }, {\n      _id: 'beverages',\n      cName: 'Beverages',\n      cImage: 'beverages.jpg',\n      cDescription: 'Drinks and beverages',\n      cStatus: 'Active'\n    }, {\n      _id: 'food',\n      cName: 'Food',\n      cImage: 'food.jpg',\n      cDescription: 'Food products',\n      cStatus: 'Active'\n    }, {\n      _id: 'household',\n      cName: 'Household',\n      cImage: 'household.jpg',\n      cDescription: 'Household items',\n      cStatus: 'Active'\n    }, {\n      _id: 'toys',\n      cName: 'Toys',\n      cImage: 'toys.jpg',\n      cDescription: 'Toys and games',\n      cStatus: 'Active'\n    }, {\n      _id: 'media',\n      cName: 'Media',\n      cImage: 'media.jpg',\n      cDescription: 'Books, CDs, and media',\n      cStatus: 'Active'\n    }];\n    console.log(\"⚠️ getAllCategory: Sử dụng mock data vì backend chưa có CategoryController\");\n    return {\n      Categories: mockCategories\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const createCategory = async ({\n  cName,\n  cImage,\n  cDescription,\n  cStatus\n}) => {\n  let formData = new FormData();\n  formData.append(\"cImage\", cImage);\n  formData.append(\"cName\", cName);\n  formData.append(\"cDescription\", cDescription);\n  formData.append(\"cStatus\", cStatus);\n  try {\n    let res = await axios.post(`${apiURL}/api/category/add-category`, formData, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const editCategory = async (cId, des, status) => {\n  let data = {\n    cId: cId,\n    cDescription: des,\n    cStatus: status\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/category/edit-category`, data, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteCategory = async cId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/category/delete-category`, {\n      cId\n    }, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nvar _c, _c2;\n$RefreshReg$(_c, \"BearerToken\");\n$RefreshReg$(_c2, \"Headers\");", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "BearerToken", "localStorage", "getItem", "JSON", "parse", "token", "_c", "Headers", "headers", "_c2", "getAllCategory", "mockCategories", "_id", "cName", "cImage", "cDescription", "cStatus", "console", "log", "Categories", "error", "createCategory", "formData", "FormData", "append", "res", "post", "data", "editCategory", "cId", "des", "status", "deleteCategory", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst BearerToken = () =>\r\n  localStorage.getItem(\"jwt\")\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).token\r\n    : false;\r\nconst Headers = () => {\r\n  return {\r\n    headers: {\r\n      token: `Bearer ${BearerToken()}`,\r\n    },\r\n  };\r\n};\r\n\r\nexport const getAllCategory = async () => {\r\n  try {\r\n    // Backend chưa có CategoryController, sử dụng mock data\r\n    const mockCategories = [\r\n      { _id: 'electronics', cName: 'Electronics', cImage: 'electronics.jpg', cDescription: 'Electronic devices and gadgets', cStatus: 'Active' },\r\n      { _id: 'fashion', cName: 'Fashion', cImage: 'fashion.jpg', cDescription: 'Clothing and accessories', cStatus: 'Active' },\r\n      { _id: 'beauty', cName: 'Beauty', cImage: 'beauty.jpg', cDescription: 'Beauty and cosmetic products', cStatus: 'Active' },\r\n      { _id: 'furniture', cName: 'Furniture', cImage: 'furniture.jpg', cDescription: 'Home and office furniture', cStatus: 'Active' },\r\n      { _id: 'beverages', cName: 'Beverages', cImage: 'beverages.jpg', cDescription: 'Drinks and beverages', cStatus: 'Active' },\r\n      { _id: 'food', cName: 'Food', cImage: 'food.jpg', cDescription: 'Food products', cStatus: 'Active' },\r\n      { _id: 'household', cName: 'Household', cImage: 'household.jpg', cDescription: 'Household items', cStatus: 'Active' },\r\n      { _id: 'toys', cName: 'Toys', cImage: 'toys.jpg', cDescription: 'Toys and games', cStatus: 'Active' },\r\n      { _id: 'media', cName: 'Media', cImage: 'media.jpg', cDescription: 'Books, CDs, and media', cStatus: 'Active' }\r\n    ];\r\n\r\n    console.log(\"⚠️ getAllCategory: Sử dụng mock data vì backend chưa có CategoryController\");\r\n    return { Categories: mockCategories };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const createCategory = async ({\r\n  cName,\r\n  cImage,\r\n  cDescription,\r\n  cStatus,\r\n}) => {\r\n  let formData = new FormData();\r\n  formData.append(\"cImage\", cImage);\r\n  formData.append(\"cName\", cName);\r\n  formData.append(\"cDescription\", cDescription);\r\n  formData.append(\"cStatus\", cStatus);\r\n\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/add-category`,\r\n      formData,\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const editCategory = async (cId, des, status) => {\r\n  let data = { cId: cId, cDescription: des, cStatus: status };\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/edit-category`,\r\n      data,\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteCategory = async (cId) => {\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/delete-category`,\r\n      { cId },\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,WAAW,GAAGA,CAAA,KAClBC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GACvBC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACG,KAAK,GAC7C,KAAK;AAACC,EAAA,GAHNN,WAAW;AAIjB,MAAMO,OAAO,GAAGA,CAAA,KAAM;EACpB,OAAO;IACLC,OAAO,EAAE;MACPH,KAAK,EAAE,UAAUL,WAAW,CAAC,CAAC;IAChC;EACF,CAAC;AACH,CAAC;AAACS,GAAA,GANIF,OAAO;AAQb,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF;IACA,MAAMC,cAAc,GAAG,CACrB;MAAEC,GAAG,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,YAAY,EAAE,gCAAgC;MAAEC,OAAO,EAAE;IAAS,CAAC,EAC1I;MAAEJ,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE,aAAa;MAAEC,YAAY,EAAE,0BAA0B;MAAEC,OAAO,EAAE;IAAS,CAAC,EACxH;MAAEJ,GAAG,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE,YAAY;MAAEC,YAAY,EAAE,8BAA8B;MAAEC,OAAO,EAAE;IAAS,CAAC,EACzH;MAAEJ,GAAG,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,YAAY,EAAE,2BAA2B;MAAEC,OAAO,EAAE;IAAS,CAAC,EAC/H;MAAEJ,GAAG,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,YAAY,EAAE,sBAAsB;MAAEC,OAAO,EAAE;IAAS,CAAC,EAC1H;MAAEJ,GAAG,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,UAAU;MAAEC,YAAY,EAAE,eAAe;MAAEC,OAAO,EAAE;IAAS,CAAC,EACpG;MAAEJ,GAAG,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,YAAY,EAAE,iBAAiB;MAAEC,OAAO,EAAE;IAAS,CAAC,EACrH;MAAEJ,GAAG,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,UAAU;MAAEC,YAAY,EAAE,gBAAgB;MAAEC,OAAO,EAAE;IAAS,CAAC,EACrG;MAAEJ,GAAG,EAAE,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,WAAW;MAAEC,YAAY,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAS,CAAC,CAChH;IAEDC,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IACzF,OAAO;MAAEC,UAAU,EAAER;IAAe,CAAC;EACvC,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdH,OAAO,CAACC,GAAG,CAACE,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAO;EACnCR,KAAK;EACLC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,IAAIM,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEV,MAAM,CAAC;EACjCQ,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEX,KAAK,CAAC;EAC/BS,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAET,YAAY,CAAC;EAC7CO,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAER,OAAO,CAAC;EAEnC,IAAI;IACF,IAAIS,GAAG,GAAG,MAAM9B,KAAK,CAAC+B,IAAI,CACxB,GAAG9B,MAAM,4BAA4B,EACrC0B,QAAQ,EACRf,OAAO,CAAC,CACV,CAAC;IACD,OAAOkB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdH,OAAO,CAACC,GAAG,CAACE,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMQ,YAAY,GAAG,MAAAA,CAAOC,GAAG,EAAEC,GAAG,EAAEC,MAAM,KAAK;EACtD,IAAIJ,IAAI,GAAG;IAAEE,GAAG,EAAEA,GAAG;IAAEd,YAAY,EAAEe,GAAG;IAAEd,OAAO,EAAEe;EAAO,CAAC;EAC3D,IAAI;IACF,IAAIN,GAAG,GAAG,MAAM9B,KAAK,CAAC+B,IAAI,CACxB,GAAG9B,MAAM,6BAA6B,EACtC+B,IAAI,EACJpB,OAAO,CAAC,CACV,CAAC;IACD,OAAOkB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdH,OAAO,CAACC,GAAG,CAACE,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMY,cAAc,GAAG,MAAOH,GAAG,IAAK;EAC3C,IAAI;IACF,IAAIJ,GAAG,GAAG,MAAM9B,KAAK,CAAC+B,IAAI,CACxB,GAAG9B,MAAM,+BAA+B,EACxC;MAAEiC;IAAI,CAAC,EACPtB,OAAO,CAAC,CACV,CAAC;IACD,OAAOkB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdH,OAAO,CAACC,GAAG,CAACE,KAAK,CAAC;EACpB;AACF,CAAC;AAAC,IAAAd,EAAA,EAAAG,GAAA;AAAAwB,YAAA,CAAA3B,EAAA;AAAA2B,YAAA,CAAAxB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}