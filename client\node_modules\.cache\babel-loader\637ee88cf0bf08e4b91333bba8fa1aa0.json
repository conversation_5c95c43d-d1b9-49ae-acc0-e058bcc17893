{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\products\\\\ProductMenu.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext } from \"react\";\nimport { ProductContext } from \"./index\";\nimport AddProductModal from \"./AddProductModal\";\nimport EditProductModal from \"./EditProductModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductMenu = props => {\n  _s();\n  const {\n    dispatch\n  } = useContext(ProductContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: \"#303031\"\n          },\n          onClick: e => dispatch({\n            type: \"addProductModal\",\n            payload: true\n          }),\n          className: \"rounded-full cursor-pointer p-2 bg-gray-800 flex items-center text-gray-100 text-sm font-semibold uppercase\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-gray-100 mr-2\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), \"Add Product\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddProductModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditProductModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductMenu, \"y0TpHjmA0G5gpKtNPtwKueTIMNE=\");\n_c = ProductMenu;\nexport default ProductMenu;\nvar _c;\n$RefreshReg$(_c, \"ProductMenu\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "ProductContext", "AddProductModal", "EditProductModal", "jsxDEV", "_jsxDEV", "ProductMenu", "props", "_s", "dispatch", "children", "className", "style", "background", "onClick", "e", "type", "payload", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/ProductMenu.js"], "sourcesContent": ["import React, { Fragment, useContext } from \"react\";\r\nimport { ProductContext } from \"./index\";\r\nimport AddProductModal from \"./AddProductModal\";\r\nimport EditProductModal from \"./EditProductModal\";\r\n\r\nconst ProductMenu = (props) => {\r\n  const { dispatch } = useContext(ProductContext);\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 flex justify-between items-center\">\r\n        <div className=\"flex items-center\">\r\n          {/* It's open the add product modal */}\r\n          <span\r\n            style={{ background: \"#303031\" }}\r\n            onClick={(e) =>\r\n              dispatch({ type: \"addProductModal\", payload: true })\r\n            }\r\n            className=\"rounded-full cursor-pointer p-2 bg-gray-800 flex items-center text-gray-100 text-sm font-semibold uppercase\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 text-gray-100 mr-2\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Add Product\r\n          </span>\r\n        </div>\r\n        <AddProductModal />\r\n        <EditProductModal />\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductMenu;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAS,CAAC,GAAGT,UAAU,CAACC,cAAc,CAAC;EAC/C,oBACEI,OAAA,CAACN,QAAQ;IAAAW,QAAA,eACPL,OAAA;MAAKM,SAAS,EAAC,8CAA8C;MAAAD,QAAA,gBAC3DL,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAD,QAAA,eAEhCL,OAAA;UACEO,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAU,CAAE;UACjCC,OAAO,EAAGC,CAAC,IACTN,QAAQ,CAAC;YAAEO,IAAI,EAAE,iBAAiB;YAAEC,OAAO,EAAE;UAAK,CAAC,CACpD;UACDN,SAAS,EAAC,6GAA6G;UAAAD,QAAA,gBAEvHL,OAAA;YACEM,SAAS,EAAC,4BAA4B;YACtCO,IAAI,EAAC,cAAc;YACnBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAV,QAAA,eAElCL,OAAA;cACEgB,QAAQ,EAAC,SAAS;cAClBC,CAAC,EAAC,4GAA4G;cAC9GC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtB,OAAA,CAACH,eAAe;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBtB,OAAA,CAACF,gBAAgB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACnB,EAAA,CAlCIF,WAAW;AAAAsB,EAAA,GAAXtB,WAAW;AAoCjB,eAAeA,WAAW;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}