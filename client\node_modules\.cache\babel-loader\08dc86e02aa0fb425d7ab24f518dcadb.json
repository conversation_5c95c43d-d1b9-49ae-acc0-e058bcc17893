{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllOrders = async status => {\n  try {\n    // Backend chưa có API get all orders, tạm thời return empty array\n    // TODO: Implement when backend has order management API\n    console.log(\"⚠️ getAllOrders: Backend chưa có API get all orders\");\n    return [];\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const updateOrderStatus = async (orderId, status) => {\n  try {\n    // Backend chưa có API update order status\n    // TODO: Implement when backend has order management API\n    console.log(\"⚠️ updateOrderStatus: Backend chưa có API update order status\");\n    return {\n      success: false,\n      message: \"API chưa có\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const deleteOrder = async (orderId, userId) => {\n  try {\n    // Sử dụng cancel order thay vì delete\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllOrders", "status", "console", "log", "error", "updateOrderStatus", "orderId", "success", "message", "deleteOrder", "userId", "res", "post", "data"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllOrders = async (status) => {\r\n  try {\r\n    // Backend chưa có API get all orders, tạm thời return empty array\r\n    // TODO: Implement when backend has order management API\r\n    console.log(\"⚠️ getAllOrders: Backend chưa có API get all orders\");\r\n    return [];\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateOrderStatus = async (orderId, status) => {\r\n  try {\r\n    // Backend chưa có API update order status\r\n    // TODO: Implement when backend has order management API\r\n    console.log(\"⚠️ updateOrderStatus: Backend chưa có API update order status\");\r\n    return { success: false, message: \"API chưa có\" };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteOrder = async (orderId, userId) => {\r\n  try {\r\n    // Sử dụng cancel order thay vì delete\r\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,YAAY,GAAG,MAAOC,MAAM,IAAK;EAC5C,IAAI;IACF;IACA;IACAC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClE,OAAO,EAAE;EACX,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEL,MAAM,KAAK;EAC1D,IAAI;IACF;IACA;IACAC,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;IAC5E,OAAO;MAAEI,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAc,CAAC;EACnD,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAAA,CAAOH,OAAO,EAAEI,MAAM,KAAK;EACpD,IAAI;IACF;IACA,IAAIC,GAAG,GAAG,MAAMhB,KAAK,CAACiB,IAAI,CAAC,GAAGhB,MAAM,iBAAiBU,OAAO,SAAS,CAAC;IACtE,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOT,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}