2025-07-07 15:20:11 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 21596 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 15:20:11 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 15:20:11 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:20:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 15:20:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 434 ms. Found 10 JPA repository interfaces.
2025-07-07 15:20:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-07 15:20:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:20:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 15:20:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:20:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4552 ms
2025-07-07 15:20:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 15:20:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 15:20:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 15:20:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@227a933d
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:20:17 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 15:20:21 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 15:20:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:20:23 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 15:20:27 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 15:20:27 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 575385a2-61e4-462b-80a2-3aadf2acfb52

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 15:20:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:20:30 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7cb81ae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f6b9ab7, org.springframework.security.web.context.SecurityContextHolderFilter@30865a1, org.springframework.security.web.header.HeaderWriterFilter@68378acc, org.springframework.web.filter.CorsFilter@1e4dc9fc, org.springframework.security.web.authentication.logout.LogoutFilter@3abe3f9a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5707ce70, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@11225084, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@661a018e, org.springframework.security.web.session.SessionManagementFilter@5867c87, org.springframework.security.web.access.ExceptionTranslationFilter@1331742, org.springframework.security.web.access.intercept.AuthorizationFilter@29e63bc3]
2025-07-07 15:20:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 15:20:32 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 22.129 seconds (process running for 23.17)
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 108 ms
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:51 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:55:54 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 15752 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 15:55:54 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 15:55:54 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:55:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 15:55:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 197 ms. Found 10 JPA repository interfaces.
2025-07-07 15:55:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 15:55:57 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:55:57 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 15:55:57 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:55:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3073 ms
2025-07-07 15:55:57 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 15:55:57 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 15:55:57 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 15:55:58 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 15:55:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:55:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@bfb93cf
2025-07-07 15:55:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:55:59 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 15:56:01 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 15:56:01 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:56:02 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 15:56:03 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 15:56:03 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ad6f60f8-d1cc-4430-ba52-0069522ce849

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 15:56:03 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:56:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@661a018e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d872a12, org.springframework.security.web.context.SecurityContextHolderFilter@11225084, org.springframework.security.web.header.HeaderWriterFilter@27561cd2, org.springframework.web.filter.CorsFilter@7efaa137, org.springframework.security.web.authentication.logout.LogoutFilter@3edea9e6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6d48f1c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f49a5f8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e08853d, org.springframework.security.web.session.SessionManagementFilter@30865a1, org.springframework.security.web.access.ExceptionTranslationFilter@5ea73927, org.springframework.security.web.access.intercept.AuthorizationFilter@7cb81ae]
2025-07-07 15:56:04 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-07 15:56:04 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:56:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 15:56:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 15:56:04 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07 15:56:04 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-07 15:57:09 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 980 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 15:57:09 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 15:57:09 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:57:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 15:57:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 10 JPA repository interfaces.
2025-07-07 15:57:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 15:57:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:57:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 15:57:11 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:57:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2391 ms
2025-07-07 15:57:11 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 15:57:12 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 15:57:12 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 15:57:12 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 15:57:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:57:12 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@61ab6521
2025-07-07 15:57:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:57:12 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 15:57:14 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 15:57:14 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:57:14 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 15:57:15 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 15:57:15 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 58764384-eb05-4836-b390-990e02fecf5c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 15:57:15 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:57:15 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6fb9179e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3abe3f9a, org.springframework.security.web.context.SecurityContextHolderFilter@14eae244, org.springframework.security.web.header.HeaderWriterFilter@5d225368, org.springframework.web.filter.CorsFilter@7b338d46, org.springframework.security.web.authentication.logout.LogoutFilter@34c9c4be, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@79658fe5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@16f108a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19fc48a0, org.springframework.security.web.session.SessionManagementFilter@47b7bcb6, org.springframework.security.web.access.ExceptionTranslationFilter@50eaafd6, org.springframework.security.web.access.intercept.AuthorizationFilter@236ec794]
2025-07-07 15:57:16 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-07 15:57:16 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:57:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 15:57:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 15:57:16 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07 15:57:16 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-07 15:57:44 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:57:44 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:57:44 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:57:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@6d5a601 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@c90bb06 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@3db8a6a5 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@16f6fabd (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@57c1de94 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@5e8007e1 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@26751f1a (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:44 [http-nio-8080-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@7f73a7b (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:57:45 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:59:29 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 16092 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 15:59:29 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 15:59:29 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:59:31 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 15:59:31 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 194 ms. Found 10 JPA repository interfaces.
2025-07-07 15:59:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 15:59:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:59:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 15:59:33 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:59:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3062 ms
2025-07-07 15:59:33 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 15:59:34 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 15:59:34 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 15:59:35 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 15:59:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:59:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7757025d
2025-07-07 15:59:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:59:36 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 15:59:39 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 15:59:39 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:59:40 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 15:59:41 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 15:59:41 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c83eddaa-4caa-40b7-acd1-70e4d311b696

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 15:59:42 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:59:42 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@19fc48a0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3edea9e6, org.springframework.security.web.context.SecurityContextHolderFilter@16f108a4, org.springframework.security.web.header.HeaderWriterFilter@6b951ee5, org.springframework.web.filter.CorsFilter@75d70348, org.springframework.security.web.authentication.logout.LogoutFilter@39a7fff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2e00ad90, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7b4f1512, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@12e9e909, org.springframework.security.web.session.SessionManagementFilter@14eae244, org.springframework.security.web.access.ExceptionTranslationFilter@48f9f1d6, org.springframework.security.web.access.intercept.AuthorizationFilter@6fb9179e]
2025-07-07 15:59:43 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-07 15:59:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:59:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 15:59:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 15:59:43 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-07 15:59:43 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-07 16:01:17 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 16908 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:01:17 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:01:17 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:01:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:01:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 171 ms. Found 10 JPA repository interfaces.
2025-07-07 16:01:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:01:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:01:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:01:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:01:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2889 ms
2025-07-07 16:01:20 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:01:21 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:01:21 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:01:21 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:01:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:01:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7757025d
2025-07-07 16:01:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:01:21 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:01:23 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:01:23 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:01:24 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:01:25 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:01:26 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 442802ea-7c5e-4267-a363-cbc33f3cc293

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:01:26 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:01:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7b338d46, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19fc48a0, org.springframework.security.web.context.SecurityContextHolderFilter@79658fe5, org.springframework.security.web.header.HeaderWriterFilter@4d687300, org.springframework.web.filter.CorsFilter@3edea9e6, org.springframework.security.web.authentication.logout.LogoutFilter@1b54ae72, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@554a30fc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e00ad90, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75d70348, org.springframework.security.web.session.SessionManagementFilter@73e3831e, org.springframework.security.web.access.ExceptionTranslationFilter@5d225368, org.springframework.security.web.access.intercept.AuthorizationFilter@50564f77]
2025-07-07 16:01:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:01:26 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 9.777 seconds (process running for 10.181)
2025-07-07 16:17:11 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 9872 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:17:11 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:17:11 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:17:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:17:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 193 ms. Found 10 JPA repository interfaces.
2025-07-07 16:17:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:17:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:17:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:17:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:17:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4007 ms
2025-07-07 16:17:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:17:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:17:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:17:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:17:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:17:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@25de8898
2025-07-07 16:17:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:17:17 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:17:19 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:17:20 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:17:20 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:17:21 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:17:21 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0cee2dd8-9c07-4a6c-80a6-3efd8eead644

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:17:21 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:17:22 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1e4dc9fc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@661a018e, org.springframework.security.web.context.SecurityContextHolderFilter@5707ce70, org.springframework.security.web.header.HeaderWriterFilter@47b7bcb6, org.springframework.web.filter.CorsFilter@3d872a12, org.springframework.security.web.authentication.logout.LogoutFilter@19fc48a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@51470a6c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d48f1c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7efaa137, org.springframework.security.web.session.SessionManagementFilter@259c7ff3, org.springframework.security.web.access.ExceptionTranslationFilter@68378acc, org.springframework.security.web.access.intercept.AuthorizationFilter@144d8c4f]
2025-07-07 16:17:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:17:22 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 11.605 seconds (process running for 12.135)
2025-07-07 16:19:26 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:19:26 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:19:26 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:19:26 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:26 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:26 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:26 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:26 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:19:26 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:19:27 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:19:59 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/2?userId=1
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/2?userId=1
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 2, role: CUSTOMER, userId: 1
2025-07-07 16:19:59 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:19:59 [http-nio-8080-exec-8] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 2
2025-07-07 16:19:59 [http-nio-8080-exec-8] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 2
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:19:59 [http-nio-8080-exec-10] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:19:59 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:19:59 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:19:59 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:20:12 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:20:12 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:12 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:12 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:20:12 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:20:17 [http-nio-8080-exec-10] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:20:17 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/18?userId=1
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:17 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/18?userId=1
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-4] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 18, role: CUSTOMER, userId: 1
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:17 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:20:17 [http-nio-8080-exec-4] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 18
2025-07-07 16:20:17 [http-nio-8080-exec-4] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 18
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:17 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:20:18 [http-nio-8080-exec-7] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:18 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:18 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:18 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:18 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:19 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:19 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:19 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:19 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:20:19 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:20:38 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/v1/auth/register
2025-07-07 16:20:38 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:38 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:38 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:38 [http-nio-8080-exec-2] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:39 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:39 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:39 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:39 [http-nio-8080-exec-1] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-4] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-6] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:41 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:41 [http-nio-8080-exec-7] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-3] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:42 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:42 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-8] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:42 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:42 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:42 [http-nio-8080-exec-10] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:43 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/register
2025-07-07 16:20:43 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:43 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/register
2025-07-07 16:20:43 [http-nio-8080-exec-2] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.register(com.darian.ecommerce.auth.dto.UserDTO): [Field error in object 'userDTO' on field 'username': rejected value [null]; codes [NotBlank.userDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:47 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/v1/auth/login
2025-07-07 16:20:47 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-07 16:20:47 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:47 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-07 16:20:47 [http-nio-8080-exec-4] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.login(com.darian.ecommerce.auth.dto.LoginDTO): [Field error in object 'loginDTO' on field 'username': rejected value [null]; codes [NotBlank.loginDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [loginDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:20:53 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-07 16:20:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:20:53 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-07 16:20:53 [http-nio-8080-exec-6] ERROR c.d.e.s.e.GlobalExceptionHandler - ValidationException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<?> com.darian.ecommerce.auth.AuthController.login(com.darian.ecommerce.auth.dto.LoginDTO): [Field error in object 'loginDTO' on field 'username': rejected value [null]; codes [NotBlank.loginDTO.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [loginDTO.username,username]; arguments []; default message [username]]; default message [Username is required]] 
2025-07-07 16:21:01 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/product/wish-product
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/wish-product
2025-07-07 16:21:01 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:02 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:21:02 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:02 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:02 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:02 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:02 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:21:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 16:21:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 16:21:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:06 [http-nio-8080-exec-6] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 16:21:06 [http-nio-8080-exec-6] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:07 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:15 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:15 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:15 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:15 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:15 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:15 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:28 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:28 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:28 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:28 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:28 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/cart/1/add?productId=1&quantity=1
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error?productId=1&quantity=1
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:35 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:39 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 16:21:39 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:39 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 16:21:39 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 16:21:39 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:39 [http-nio-8080-exec-6] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 16:21:39 [http-nio-8080-exec-6] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/cart/1/add?productId=1&quantity=1
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error?productId=1&quantity=1
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:44 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:21:44 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:44 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:44 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:44 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:21:46 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:46 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:46 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:46 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:21:50 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/search?keyword=sm&userId=1
2025-07-07 16:21:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:50 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/search?keyword=sm&userId=1
2025-07-07 16:21:50 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - User sm searching products with keyword: CUSTOMER, role: 1, userId: {}
2025-07-07 16:21:50 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:50 [http-nio-8080-exec-2] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed search action on product: sm
2025-07-07 16:21:50 [http-nio-8080-exec-2] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 searching products with keyword: CUSTOMER, role: sm, userId: {}
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/cart/1/add?productId=1&quantity=1
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error?productId=1&quantity=1
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:56 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:59 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:21:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:21:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:21:59 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:22:52 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:22:52 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:22:52 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:22:52 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:24:19 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:24:19 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:24:19 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:24:19 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:25:08 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:25:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:25:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 16:25:22 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 10460 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:25:22 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:25:22 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:25:24 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:25:24 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 222 ms. Found 10 JPA repository interfaces.
2025-07-07 16:25:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:25:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:25:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:25:25 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:25:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3468 ms
2025-07-07 16:25:26 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:25:26 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:25:26 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:25:27 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:25:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:25:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@bfb93cf
2025-07-07 16:25:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:25:27 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:25:30 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:25:30 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:25:31 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:25:33 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:25:33 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: edef7a37-d32b-45f6-98fa-d003d1c758f1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:25:33 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:25:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1e4dc9fc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@661a018e, org.springframework.security.web.context.SecurityContextHolderFilter@5707ce70, org.springframework.security.web.header.HeaderWriterFilter@47b7bcb6, org.springframework.web.filter.CorsFilter@3d872a12, org.springframework.security.web.authentication.logout.LogoutFilter@19fc48a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@51470a6c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d48f1c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7efaa137, org.springframework.security.web.session.SessionManagementFilter@259c7ff3, org.springframework.security.web.access.ExceptionTranslationFilter@68378acc, org.springframework.security.web.access.intercept.AuthorizationFilter@144d8c4f]
2025-07-07 16:25:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:25:34 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 12.956 seconds (process running for 13.591)
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:25:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:41 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:25:41 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:48 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 16:25:48 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:25:48 [http-nio-8080-exec-9] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 16:25:48 [http-nio-8080-exec-9] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:48 [http-nio-8080-exec-6] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:25:48 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:48 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:48 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:55 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/product/product-by-category
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/product-by-category
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:56 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:57 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:57 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:57 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:57 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:57 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:25:57 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/product-by-category
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:25:59 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:25:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:26:00 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:26:00 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:00 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:00 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:26:00 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:26:01 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/product-by-category
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:02 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:02 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:04 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:04 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:04 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:04 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:26:04 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/product-by-category
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:05 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:05 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:26:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:07 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:26:07 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:26:46 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:26:46 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:46 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:26:46 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:26:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:26:48 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:26:48 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:26:48 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:28:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:28:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:28:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:28:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:28:50 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:28:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:28:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 16:33:09 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 4844 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:33:09 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:33:09 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:33:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:33:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 214 ms. Found 10 JPA repository interfaces.
2025-07-07 16:33:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:33:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:33:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:33:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:33:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3686 ms
2025-07-07 16:33:13 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:33:13 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:33:14 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:33:14 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:33:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:33:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@227a933d
2025-07-07 16:33:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:33:15 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:33:17 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "uk_7r8d4ymqmq6nnfqqi1i1d3wbs" of relation "payment_transaction" does not exist, skipping
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "uk_hl02wv5hym99ys465woijmfib" of relation "user_account" does not exist, skipping
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-07 16:33:17 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "uk_castjbvpeeus0r8lbpehiu0e4" of relation "user_account" does not exist, skipping
2025-07-07 16:33:17 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:33:18 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:33:19 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:33:19 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 76d39c72-e4f6-4244-afab-c8bbf769b57e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:33:19 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:33:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@55bd9d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a4f14c9, org.springframework.security.web.context.SecurityContextHolderFilter@22608777, org.springframework.security.web.header.HeaderWriterFilter@1ef48e49, org.springframework.web.filter.CorsFilter@7b81283, org.springframework.security.web.authentication.logout.LogoutFilter@94d4dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48f9f1d6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4d687300, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e8b5e7d, org.springframework.security.web.session.SessionManagementFilter@5fd40d2e, org.springframework.security.web.access.ExceptionTranslationFilter@4907472e, org.springframework.security.web.access.intercept.AuthorizationFilter@6c6c7ee5]
2025-07-07 16:33:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:33:20 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 11.164 seconds (process running for 11.722)
2025-07-07 16:33:23 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:33:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:33:23 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 16:33:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:23 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:23 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:33:33 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:33 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:33 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:33:33 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:33:33 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:33:33 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:37 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:34:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:37 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:37 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:37 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:37 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:34:55 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:55 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:55 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:55 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:34:55 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:55 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:57 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:34:57 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:34:58 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:57 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:34:58 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:58 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:58 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:58 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:58 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:58 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:58 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:34:58 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:34:58 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:34:58 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:34:58 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:34:58 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:36:51 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:36:51 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:36:51 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:36:51 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:36:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:36:51 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:37:59 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:37:59 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:37:59 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:37:59 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:37:59 [http-nio-8080-exec-5] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:37:59 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:39:50 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:39:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:39:50 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:39:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:40:12 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:40:12 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:40:12 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:40:12 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:41:13 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:41:13 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:41:13 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 16:49:13 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 17716 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:49:13 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:49:13 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:49:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:49:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173 ms. Found 10 JPA repository interfaces.
2025-07-07 16:49:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:49:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:49:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:49:16 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:49:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3410 ms
2025-07-07 16:49:17 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:49:17 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:49:17 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:49:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:49:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:49:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7bc2ae16
2025-07-07 16:49:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:49:18 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:49:20 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:49:20 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:49:21 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:49:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:49:24 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c9855bef-5553-40f5-9df0-a427f2ffcd31

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:49:24 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:49:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@531245fe, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d7298be, org.springframework.security.web.context.SecurityContextHolderFilter@77f5cffd, org.springframework.security.web.header.HeaderWriterFilter@1723640f, org.springframework.web.filter.CorsFilter@2c289a9e, org.springframework.security.web.authentication.logout.LogoutFilter@4bd0d62f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f3628c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6bcc277a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ee47336, org.springframework.security.web.session.SessionManagementFilter@7111b312, org.springframework.security.web.access.ExceptionTranslationFilter@3828f303, org.springframework.security.web.access.intercept.AuthorizationFilter@457512b]
2025-07-07 16:49:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:49:26 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 13.476 seconds (process running for 14.135)
2025-07-07 16:49:43 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:49:43 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:49:43 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:49:44 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:49:44 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:44 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:44 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:49:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:49:44 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:49:45 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:49:49 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 16:49:49 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:49:49 [http-nio-8080-exec-6] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 16:49:49 [http-nio-8080-exec-6] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:49:49 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:49:50 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:50 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:49:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:50 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:49:50 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:50:04 [http-nio-8080-exec-3] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:04 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 16:50:04 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 16:50:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:50:04 [http-nio-8080-exec-6] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 16:50:04 [http-nio-8080-exec-6] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:04 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:05 [http-nio-8080-exec-8] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:05 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:05 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:05 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:05 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:50:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:50:06 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/product/product-by-category
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:13 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:14 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:14 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:14 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:14 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:50:14 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:50:21 [http-nio-8080-exec-8] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:21 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/10?userId=1
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/10?userId=1
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:21 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:21 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 10, role: CUSTOMER, userId: 1
2025-07-07 16:50:21 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:50:21 [http-nio-8080-exec-7] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 10
2025-07-07 16:50:21 [http-nio-8080-exec-7] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 10
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:21 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-4] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:22 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:50:22 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:50:22 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:50:22 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:50:23 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:51:43 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:51:43 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:51:43 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:51:43 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:51:43 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:51:43 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:51:53 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:51:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:51:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 16:51:59 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 6468 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 16:51:59 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 16:51:59 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:52:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 16:52:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 194 ms. Found 10 JPA repository interfaces.
2025-07-07 16:52:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-07 16:52:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:52:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 16:52:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:52:02 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3279 ms
2025-07-07 16:52:03 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 16:52:03 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 16:52:03 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 16:52:03 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 16:52:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:52:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5b7308aa
2025-07-07 16:52:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:52:04 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 16:52:06 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 16:52:06 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 16:52:06 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 16:52:07 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 16:52:08 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 03eef3a9-ed0e-4487-8dbe-4a487702f76d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 16:52:08 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:52:08 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@44a3b4d9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16e3a461, org.springframework.security.web.context.SecurityContextHolderFilter@912fdbb, org.springframework.security.web.header.HeaderWriterFilter@50853850, org.springframework.web.filter.CorsFilter@7e2723d2, org.springframework.security.web.authentication.logout.LogoutFilter@457512b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d62648d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59fe8d94, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b0061b7, org.springframework.security.web.session.SessionManagementFilter@207283b4, org.springframework.security.web.access.ExceptionTranslationFilter@17a17af5, org.springframework.security.web.access.intercept.AuthorizationFilter@6bc72ab6]
2025-07-07 16:52:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 16:52:08 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 9.878 seconds (process running for 10.223)
2025-07-07 16:52:14 [http-nio-8080-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:52:14 [http-nio-8080-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:52:14 [http-nio-8080-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-07 16:52:14 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:52:15 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:52:15 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:52:15 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:52:15 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:52:15 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:52:15 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:52:15 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:52:15 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:52:15 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:52:15 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:59:28 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:59:28 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:28 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:28 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:28 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:59:28 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:59:52 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/2?userId=1
2025-07-07 16:59:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:52 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/2?userId=1
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:52 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:52 [http-nio-8080-exec-4] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 2, role: CUSTOMER, userId: 1
2025-07-07 16:59:52 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:59:52 [http-nio-8080-exec-4] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 2
2025-07-07 16:59:52 [http-nio-8080-exec-4] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 2
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:53 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:53 [http-nio-8080-exec-8] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 16:59:53 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:53 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:53 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 16:59:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 16:59:54 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 16:59:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 16:59:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 17:04:38 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 17:04:38 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:38 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 17:04:38 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 17:04:43 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:43 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:43 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:43 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:43 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 17:04:43 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 17:04:44 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 17:04:47 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/14?userId=1
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:47 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:47 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/14?userId=1
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:47 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 14, role: CUSTOMER, userId: 1
2025-07-07 17:04:47 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 17:04:47 [http-nio-8080-exec-1] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 14
2025-07-07 17:04:47 [http-nio-8080-exec-1] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 14
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /uploads/products/undefined
2025-07-07 17:04:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:48 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:48 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/cart/1
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/customize/get-slide-image
2025-07-07 17:04:49 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:49 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:49 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 17:04:49 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 17:04:49 [http-nio-8080-exec-8] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
