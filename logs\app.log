2025-07-07 15:20:11 [main] INFO  c.d.ecommerce.EcommerceApplication - Starting EcommerceApplication using Java 17.0.12 with PID 21596 (D:\ITSS_Reference\backend\target\classes started by laptop in D:\ITSS_Reference)
2025-07-07 15:20:11 [main] DEBUG c.d.ecommerce.EcommerceApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-07 15:20:11 [main] INFO  c.d.ecommerce.EcommerceApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 15:20:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-07 15:20:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 434 ms. Found 10 JPA repository interfaces.
2025-07-07 15:20:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-07 15:20:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 15:20:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-07 15:20:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 15:20:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4552 ms
2025-07-07 15:20:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-07 15:20:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-07 15:20:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-07 15:20:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@227a933d
2025-07-07 15:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:20:17 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-07 15:20:21 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-07 15:20:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-07 15:20:23 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-07 15:20:27 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-07 15:20:27 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 575385a2-61e4-462b-80a2-3aadf2acfb52

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-07 15:20:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:20:30 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7cb81ae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f6b9ab7, org.springframework.security.web.context.SecurityContextHolderFilter@30865a1, org.springframework.security.web.header.HeaderWriterFilter@68378acc, org.springframework.web.filter.CorsFilter@1e4dc9fc, org.springframework.security.web.authentication.logout.LogoutFilter@3abe3f9a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5707ce70, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@11225084, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@661a018e, org.springframework.security.web.session.SessionManagementFilter@5867c87, org.springframework.security.web.access.ExceptionTranslationFilter@1331742, org.springframework.security.web.access.intercept.AuthorizationFilter@29e63bc3]
2025-07-07 15:20:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-07 15:20:32 [main] INFO  c.d.ecommerce.EcommerceApplication - Started EcommerceApplication in 22.129 seconds (process running for 23.17)
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 108 ms
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:37:00 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:37:00 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:37:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:51 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:51 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:53 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:53 [http-nio-8080-exec-7] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:54 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:54 [http-nio-8080-exec-10] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:55 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:39:55 [http-nio-8080-exec-4] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:39:55 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 15:39:55 [http-nio-8080-exec-5] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer/1?userId=1
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:01 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer/1?userId=1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product details for productId: 1, role: CUSTOMER, userId: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.a.l.AuditLogEventListener - User 1 (role: CUSTOMER) performed view action on product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-9] INFO  c.d.e.audit.AuditLogServiceImpl - User 1 (role: CUSTOMER) viewed product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:01 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-1] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-2] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-6] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 15:40:02 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/products/customer
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.impl.ProductServiceImpl - Fetching product list for role: CUSTOMER
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 1
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 2
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 3
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 4
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 5
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 6
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 7
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 8
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 9
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 10
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 11
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 12
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 13
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 14
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 15
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 16
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 17
2025-07-07 15:40:02 [http-nio-8080-exec-3] INFO  c.d.e.p.s.i.RelatedProductServiceImpl - Suggesting related products for product: 18
