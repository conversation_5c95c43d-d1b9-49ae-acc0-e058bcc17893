{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\AllOrders.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Fragment, useContext, useEffect } from \"react\";\nimport moment from \"moment\";\nimport { OrderContext } from \"./index\";\nimport { fetchData, editOrderReq, deleteOrderReq } from \"./Actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst AllOrders = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(OrderContext);\n  // Filter out orders with \"DRAFT\" status\n  const ordersToDisplay = data.orders.filter(order => order.orderStatus !== \"DRAFT\");\n  const {\n    loading\n  } = data;\n  useEffect(() => {\n    fetchData(dispatch);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 animate-spin text-gray-600\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 overflow-auto bg-white shadow-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table-auto border w-full my-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Total\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Transaction Id\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Created at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Updated at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: ordersToDisplay && ordersToDisplay.length > 0 ? ordersToDisplay.map((item, i) => {\n            return /*#__PURE__*/_jsxDEV(CategoryTable, {\n              order: item,\n              editOrder: (oId, type, status) => editOrderReq(oId, type, status, dispatch)\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"11\",\n              className: \"text-xl text-center font-semibold py-8\",\n              children: \"No order found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 mt-2\",\n        children: [\"Total \", ordersToDisplay && ordersToDisplay.length, \" order found\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n\n/* Single Category Component */\n_s(AllOrders, \"Yu3WSwDPvtUKWDFIw3QA4Aw+HMM=\");\n_c = AllOrders;\nconst CategoryTable = ({\n  order,\n  editOrder\n}) => {\n  _s2();\n  const {\n    dispatch\n  } = useContext(OrderContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      className: \"border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"w-48 hover:bg-gray-200 p-2 flex flex-col space-y-1\",\n        children: order.items.map((item, i) => {\n          const imageUrl = item.productImages && item.productImages.length > 0 ? `${apiURL}/uploads/products/${item.productImages[0]}` : '/placeholder-product.jpg';\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"w-8 h-8 object-cover object-center\",\n              src: imageUrl,\n              alt: item.productName || 'Product Image'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.productName || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [item.quantity, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center cursor-default\",\n        children: [order.orderStatus === \"PENDING\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-yellow-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Pending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), order.orderStatus === \"CONFIRMED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-green-500 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Confirmed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), order.orderStatus === \"SHIPPED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Shipped\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), order.orderStatus === \"DELIVERED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Delivered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), order.orderStatus === \"REJECTED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-orange-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Rejected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), order.orderStatus === \"CANCELLED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: \"Cancelled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: [order.total, \".00 \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.transactionId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.deliveryInfo.recipientName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.deliveryInfo.phone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: order.deliveryInfo.address\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: moment(order.createdDate).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"hover:bg-gray-200 p-2 text-center\",\n        children: moment(order.updatedAt).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => editOrder(order._id, true, order.orderStatus),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 fill-current text-green-500\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => deleteOrderReq(order._id, dispatch),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-red-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s2(CategoryTable, \"y0TpHjmA0G5gpKtNPtwKueTIMNE=\");\n_c2 = CategoryTable;\nexport default AllOrders;\nvar _c, _c2;\n$RefreshReg$(_c, \"AllOrders\");\n$RefreshReg$(_c2, \"CategoryTable\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useEffect", "moment", "OrderContext", "fetchData", "editOrderReq", "deleteOrderReq", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "AllOrders", "props", "_s", "data", "dispatch", "ordersToDisplay", "orders", "filter", "order", "orderStatus", "loading", "className", "children", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "item", "i", "CategoryTable", "editOrder", "oId", "type", "status", "colSpan", "_c", "_s2", "items", "imageUrl", "productImages", "src", "alt", "productName", "quantity", "total", "transactionId", "deliveryInfo", "<PERSON><PERSON><PERSON>", "phone", "address", "createdDate", "format", "updatedAt", "onClick", "e", "_id", "fillRule", "clipRule", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/AllOrders.js"], "sourcesContent": ["import React, { Fragment, useContext, useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nimport { OrderContext } from \"./index\";\r\nimport { fetchData, editOrderReq, deleteOrderReq } from \"./Actions\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst AllOrders = (props) => {\r\n  const { data, dispatch } = useContext(OrderContext);\r\n  // Filter out orders with \"DRAFT\" status\r\n  const ordersToDisplay = data.orders.filter(order => order.orderStatus !== \"DRAFT\");\r\n  const { loading } = data;\r\n\r\n  useEffect(() => {\r\n    fetchData(dispatch);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 overflow-auto bg-white shadow-lg p-4\">\r\n        <table className=\"table-auto border w-full my-2\">\r\n          <thead>\r\n            <tr>\r\n              <th className=\"px-4 py-2 border\">Products</th>\r\n              <th className=\"px-4 py-2 border\">Status</th>\r\n              <th className=\"px-4 py-2 border\">Total</th>\r\n              <th className=\"px-4 py-2 border\">Transaction Id</th>\r\n              <th className=\"px-4 py-2 border\">Customer</th>\r\n              <th className=\"px-4 py-2 border\">Phone</th>\r\n              <th className=\"px-4 py-2 border\">Address</th>\r\n              <th className=\"px-4 py-2 border\">Created at</th>\r\n              <th className=\"px-4 py-2 border\">Updated at</th>\r\n              <th className=\"px-4 py-2 border\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {ordersToDisplay && ordersToDisplay.length > 0 ? (\r\n              ordersToDisplay.map((item, i) => {\r\n                return (\r\n                  <CategoryTable\r\n                    key={i}\r\n                    order={item}\r\n                    editOrder={(oId, type, status) =>\r\n                      editOrderReq(oId, type, status, dispatch)\r\n                    }\r\n                  />\r\n                );\r\n              })\r\n            ) : (\r\n              <tr>\r\n                <td\r\n                  colSpan=\"11\"\r\n                  className=\"text-xl text-center font-semibold py-8\"\r\n                >\r\n                  No order found\r\n                </td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </table>\r\n        <div className=\"text-sm text-gray-600 mt-2\">\r\n          Total {ordersToDisplay && ordersToDisplay.length} order found\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\n/* Single Category Component */\r\nconst CategoryTable = ({ order, editOrder }) => {\r\n  const { dispatch } = useContext(OrderContext);\r\n\r\n  return (\r\n    <Fragment>\r\n      <tr className=\"border-b\">\r\n        <td className=\"w-48 hover:bg-gray-200 p-2 flex flex-col space-y-1\">\r\n          {order.items.map((item, i) => {\r\n            const imageUrl = item.productImages && item.productImages.length > 0\r\n              ? `${apiURL}/uploads/products/${item.productImages[0]}`\r\n              : '/placeholder-product.jpg';\r\n\r\n            return (\r\n              <span className=\"block flex items-center space-x-2\" key={i}>\r\n                <img\r\n                  className=\"w-8 h-8 object-cover object-center\"\r\n                  src={imageUrl}\r\n                  alt={item.productName || 'Product Image'}\r\n                />\r\n                <span>{item.productName || 'N/A'}</span>\r\n                <span>{item.quantity}x</span>\r\n              </span>\r\n            );\r\n          })}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center cursor-default\">\r\n          {order.orderStatus === \"PENDING\" && (\r\n            <span className=\"block text-yellow-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Pending\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"CONFIRMED\" && (\r\n            <span className=\"block text-green-500 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Confirmed\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"SHIPPED\" && (\r\n            <span className=\"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Shipped\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"DELIVERED\" && (\r\n            <span className=\"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Delivered\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"REJECTED\" && (\r\n            <span className=\"block text-orange-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Rejected\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"CANCELLED\" && (\r\n            <span className=\"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              Cancelled\r\n            </span>\r\n          )}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {order.total}.00 {/* Removed \"$\" */}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {order.transactionId}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.deliveryInfo.recipientName}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.deliveryInfo.phone}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">{order.deliveryInfo.address}</td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {moment(order.createdDate).format(\"lll\")}\r\n        </td>\r\n        <td className=\"hover:bg-gray-200 p-2 text-center\">\r\n          {moment(order.updatedAt).format(\"lll\")}\r\n        </td>\r\n        <td className=\"p-2 flex items-center justify-center\">\r\n          <span\r\n            onClick={(e) => editOrder(order._id, true, order.orderStatus)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 fill-current text-green-500\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\" />\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </span>\r\n          <span\r\n            onClick={(e) => deleteOrderReq(order._id, dispatch)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 text-red-500\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\r\n              />\r\n            </svg>\r\n          </span>\r\n        </td>\r\n      </tr>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default AllOrders;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,SAAS,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGjB,UAAU,CAACG,YAAY,CAAC;EACnD;EACA,MAAMe,eAAe,GAAGF,IAAI,CAACG,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,WAAW,KAAK,OAAO,CAAC;EAClF,MAAM;IAAEC;EAAQ,CAAC,GAAGP,IAAI;EAExBf,SAAS,CAAC,MAAM;IACdG,SAAS,CAACa,QAAQ,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIM,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKgB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDjB,OAAA;QACEgB,SAAS,EAAC,sCAAsC;QAChDE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAJ,QAAA,eAElCjB,OAAA;UACEsB,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAC,GAAG;UACfC,CAAC,EAAC;QAA6G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EACA,oBACE7B,OAAA,CAACT,QAAQ;IAAA0B,QAAA,eACPjB,OAAA;MAAKgB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9DjB,OAAA;QAAOgB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC9CjB,OAAA;UAAAiB,QAAA,eACEjB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7B,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7B,OAAA;UAAAiB,QAAA,EACGP,eAAe,IAAIA,eAAe,CAACoB,MAAM,GAAG,CAAC,GAC5CpB,eAAe,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;YAC/B,oBACEjC,OAAA,CAACkC,aAAa;cAEZrB,KAAK,EAAEmB,IAAK;cACZG,SAAS,EAAEA,CAACC,GAAG,EAAEC,IAAI,EAAEC,MAAM,KAC3BzC,YAAY,CAACuC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAE7B,QAAQ;YACzC,GAJIwB,CAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKP,CAAC;UAEN,CAAC,CAAC,gBAEF7B,OAAA;YAAAiB,QAAA,eACEjB,OAAA;cACEuC,OAAO,EAAC,IAAI;cACZvB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACnD;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACR7B,OAAA;QAAKgB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,QACpC,EAACP,eAAe,IAAIA,eAAe,CAACoB,MAAM,EAAC,cACnD;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;;AAED;AAAAtB,EAAA,CAlFMF,SAAS;AAAAmC,EAAA,GAATnC,SAAS;AAmFf,MAAM6B,aAAa,GAAGA,CAAC;EAAErB,KAAK;EAAEsB;AAAU,CAAC,KAAK;EAAAM,GAAA;EAC9C,MAAM;IAAEhC;EAAS,CAAC,GAAGjB,UAAU,CAACG,YAAY,CAAC;EAE7C,oBACEK,OAAA,CAACT,QAAQ;IAAA0B,QAAA,eACPjB,OAAA;MAAIgB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACtBjB,OAAA;QAAIgB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,EAC/DJ,KAAK,CAAC6B,KAAK,CAACX,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;UAC5B,MAAMU,QAAQ,GAAGX,IAAI,CAACY,aAAa,IAAIZ,IAAI,CAACY,aAAa,CAACd,MAAM,GAAG,CAAC,GAChE,GAAG7B,MAAM,qBAAqB+B,IAAI,CAACY,aAAa,CAAC,CAAC,CAAC,EAAE,GACrD,0BAA0B;UAE9B,oBACE5C,OAAA;YAAMgB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBACjDjB,OAAA;cACEgB,SAAS,EAAC,oCAAoC;cAC9C6B,GAAG,EAAEF,QAAS;cACdG,GAAG,EAAEd,IAAI,CAACe,WAAW,IAAI;YAAgB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACF7B,OAAA;cAAAiB,QAAA,EAAOe,IAAI,CAACe,WAAW,IAAI;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC7B,OAAA;cAAAiB,QAAA,GAAOe,IAAI,CAACgB,QAAQ,EAAC,GAAC;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP0BI,CAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQpD,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,kDAAkD;QAAAC,QAAA,GAC7DJ,KAAK,CAACC,WAAW,KAAK,SAAS,iBAC9Bd,OAAA;UAAMgB,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EAAC;QAE5F;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAhB,KAAK,CAACC,WAAW,KAAK,WAAW,iBAChCd,OAAA;UAAMgB,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EAAC;QAE3F;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAhB,KAAK,CAACC,WAAW,KAAK,SAAS,iBAC9Bd,OAAA;UAAMgB,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EAAC;QAE1F;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAhB,KAAK,CAACC,WAAW,KAAK,WAAW,iBAChCd,OAAA;UAAMgB,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EAAC;QAE3F;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAhB,KAAK,CAACC,WAAW,KAAK,UAAU,iBAC/Bd,OAAA;UAAMgB,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EAAC;QAE5F;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAhB,KAAK,CAACC,WAAW,KAAK,WAAW,iBAChCd,OAAA;UAAMgB,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEzF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,GAC9CJ,KAAK,CAACoC,KAAK,EAAC,MAAI;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CJ,KAAK,CAACqC;MAAa;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEJ,KAAK,CAACsC,YAAY,CAACC;MAAa;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzF7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEJ,KAAK,CAACsC,YAAY,CAACE;MAAK;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjF7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAEJ,KAAK,CAACsC,YAAY,CAACG;MAAO;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnF7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CvB,MAAM,CAACmB,KAAK,CAAC0C,WAAW,CAAC,CAACC,MAAM,CAAC,KAAK;MAAC;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9CvB,MAAM,CAACmB,KAAK,CAAC4C,SAAS,CAAC,CAACD,MAAM,CAAC,KAAK;MAAC;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACL7B,OAAA;QAAIgB,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBAClDjB,OAAA;UACE0D,OAAO,EAAGC,CAAC,IAAKxB,SAAS,CAACtB,KAAK,CAAC+C,GAAG,EAAE,IAAI,EAAE/C,KAAK,CAACC,WAAW,CAAE;UAC9DE,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhEjB,OAAA;YACEgB,SAAS,EAAC,qCAAqC;YAC/CE,IAAI,EAAC,cAAc;YACnBE,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,gBAElCjB,OAAA;cAAMyB,CAAC,EAAC;YAA+E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1F7B,OAAA;cACE6D,QAAQ,EAAC,SAAS;cAClBpC,CAAC,EAAC,wFAAwF;cAC1FqC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP7B,OAAA;UACE0D,OAAO,EAAGC,CAAC,IAAK7D,cAAc,CAACe,KAAK,CAAC+C,GAAG,EAAEnD,QAAQ,CAAE;UACpDO,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhEjB,OAAA;YACEgB,SAAS,EAAC,sBAAsB;YAChCE,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,eAElCjB,OAAA;cACEsB,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA8H;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEf,CAAC;AAACY,GAAA,CAlHIP,aAAa;AAAA6B,GAAA,GAAb7B,aAAa;AAoHnB,eAAe7B,SAAS;AAAC,IAAAmC,EAAA,EAAAuB,GAAA;AAAAC,YAAA,CAAAxB,EAAA;AAAAwB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}