{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// VNPay payment methods (thay thế BrainTree)\nexport const createVNPayPayment = async (orderId, amount, orderInfo) => {\n  try {\n    // Backend sử dụng VNPay thay vì BrainTree\n    let res = await axios.post(`${apiURL}/api/v1/payment/${orderId}`, {\n      amount: amount,\n      orderInfo: orderInfo,\n      returnUrl: `${window.location.origin}/payment/return`,\n      cancelUrl: `${window.location.origin}/payment/cancel`\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const getPaymentStatus = async orderId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/payment/${orderId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Legacy methods for compatibility (deprecated)\nexport const getBrainTreeToken = async () => {\n  console.log(\"⚠️ getBrainTreeToken: Deprecated - Backend sử dụng VNPay\");\n  return {\n    error: \"BrainTree không được hỗ trợ, sử dụng VNPay\"\n  };\n};\nexport const getPaymentProcess = async paymentData => {\n  console.log(\"⚠️ getPaymentProcess: Deprecated - Backend sử dụng VNPay\");\n  return {\n    error: \"BrainTree không được hỗ trợ, sử dụng VNPay\"\n  };\n};\nexport const createOrder = async cartData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Place order (confirm order)\nexport const placeOrder = async orderData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get order by ID\nexport const getOrderById = async orderId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Cancel order\nexport const cancelOrder = async orderId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Pay for order\nexport const payOrder = async (orderId, paymentData) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "createVNPayPayment", "orderId", "amount", "orderInfo", "res", "post", "returnUrl", "window", "location", "origin", "cancelUrl", "data", "error", "console", "log", "getPaymentStatus", "get", "getBrainTreeToken", "getPaymentProcess", "paymentData", "createOrder", "cartData", "placeOrder", "orderData", "getOrderById", "cancelOrder", "payOrder"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// VNPay payment methods (thay thế BrainTree)\r\nexport const createVNPayPayment = async (orderId, amount, orderInfo) => {\r\n  try {\r\n    // Backend sử dụng VNPay thay vì BrainTree\r\n    let res = await axios.post(`${apiURL}/api/v1/payment/${orderId}`, {\r\n      amount: amount,\r\n      orderInfo: orderInfo,\r\n      returnUrl: `${window.location.origin}/payment/return`,\r\n      cancelUrl: `${window.location.origin}/payment/cancel`\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getPaymentStatus = async (orderId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/payment/${orderId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Legacy methods for compatibility (deprecated)\r\nexport const getBrainTreeToken = async () => {\r\n  console.log(\"⚠️ getBrainTreeToken: Deprecated - Backend sử dụng VNPay\");\r\n  return { error: \"BrainTree không được hỗ trợ, sử dụng VNPay\" };\r\n};\r\n\r\nexport const getPaymentProcess = async (paymentData) => {\r\n  console.log(\"⚠️ getPaymentProcess: Deprecated - Backend sử dụng VNPay\");\r\n  return { error: \"BrainTree không được hỗ trợ, sử dụng VNPay\" };\r\n};\r\n\r\nexport const createOrder = async (cartData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Place order (confirm order)\r\nexport const placeOrder = async (orderData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get order by ID\r\nexport const getOrderById = async (orderId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Cancel order\r\nexport const cancelOrder = async (orderId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Pay for order\r\nexport const payOrder = async (orderId, paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAOC,OAAO,EAAEC,MAAM,EAAEC,SAAS,KAAK;EACtE,IAAI;IACF;IACA,IAAIC,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAAC,GAAGT,MAAM,mBAAmBK,OAAO,EAAE,EAAE;MAChEC,MAAM,EAAEA,MAAM;MACdC,SAAS,EAAEA,SAAS;MACpBG,SAAS,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,iBAAiB;MACrDC,SAAS,EAAE,GAAGH,MAAM,CAACC,QAAQ,CAACC,MAAM;IACtC,CAAC,CAAC;IACF,OAAOL,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAG,MAAOd,OAAO,IAAK;EACjD,IAAI;IACF,IAAIG,GAAG,GAAG,MAAMT,KAAK,CAACqB,GAAG,CAAC,GAAGpB,MAAM,mBAAmBK,OAAO,EAAE,CAAC;IAChE,OAAOG,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3CJ,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;EACvE,OAAO;IAAEF,KAAK,EAAE;EAA6C,CAAC;AAChE,CAAC;AAED,OAAO,MAAMM,iBAAiB,GAAG,MAAOC,WAAW,IAAK;EACtDN,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;EACvE,OAAO;IAAEF,KAAK,EAAE;EAA6C,CAAC;AAChE,CAAC;AAED,OAAO,MAAMQ,WAAW,GAAG,MAAOC,QAAQ,IAAK;EAC7C,IAAI;IACF,IAAIjB,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAAC,GAAGT,MAAM,4BAA4B,EAAEyB,QAAQ,CAAC;IAC3E,OAAOjB,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,UAAU,GAAG,MAAOC,SAAS,IAAK;EAC7C,IAAI;IACF,IAAInB,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAAC,GAAGT,MAAM,2BAA2B,EAAE2B,SAAS,CAAC;IAC3E,OAAOnB,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,YAAY,GAAG,MAAOvB,OAAO,IAAK;EAC7C,IAAI;IACF,IAAIG,GAAG,GAAG,MAAMT,KAAK,CAACqB,GAAG,CAAC,GAAGpB,MAAM,iBAAiBK,OAAO,EAAE,CAAC;IAC9D,OAAOG,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,WAAW,GAAG,MAAOxB,OAAO,IAAK;EAC5C,IAAI;IACF,IAAIG,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAAC,GAAGT,MAAM,iBAAiBK,OAAO,SAAS,CAAC;IACtE,OAAOG,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,QAAQ,GAAG,MAAAA,CAAOzB,OAAO,EAAEkB,WAAW,KAAK;EACtD,IAAI;IACF,IAAIf,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAAC,GAAGT,MAAM,iBAAiBK,OAAO,MAAM,EAAEkB,WAAW,CAAC;IAChF,OAAOf,GAAG,CAACO,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}