import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const getAllProduct = async () => {
  try {
    console.log("🌐 FetchApi: Making request to:", `${apiURL}/api/v1/products/customer`);
    console.log("🔧 FetchApi: API URL:", apiURL);

    let res = await axios.get(`${apiURL}/api/v1/products/customer`);
    console.log("📡 FetchApi: Response status:", res.status);
    console.log("📦 FetchApi: Response data:", res.data);
    console.log("📊 FetchApi: Data type:", typeof res.data);
    console.log("📋 FetchApi: Is array:", Array.isArray(res.data));

    return res.data;
  } catch (error) {
    console.error("❌ FetchApi: Error in getAllProduct:", error);
    console.error("❌ FetchApi: Error message:", error.message);
    console.error("❌ FetchApi: Error response:", error.response?.data);
    console.error("❌ FetchApi: Error status:", error.response?.status);
    throw error;
  }
};

export const getAllProductForManager = async () => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/products/manager`);
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const createPorductImage = async ({ pImage }) => {
  /* Most important part for uploading multiple image  */
  let formData = new FormData();
  for (const file of pImage) {
    formData.append("pImage", file);
  }
  /* Most important part for uploading multiple image  */
};

export const createProduct = async ({
  name,
  description,
  images,
  category,
  stockQuantity,
  price,
  weight,
  rushEligible,
  barcode,
  specifications,
  userId
}) => {
  const productData = {
    name,
    description,
    images: images || [],
    category,
    price: parseFloat(price),
    weight: parseFloat(weight) || 0,
    rushEligible: rushEligible || false,
    barcode,
    specifications
  };

  try {
    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, productData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const editProduct = async (product) => {
  console.log(product);
  /* Most important part for updating multiple image  */
  let formData = new FormData();
  if (product.pEditImages) {
    for (const file of product.pEditImages) {
      formData.append("pEditImages", file);
    }
  }
  /* Most important part for updating multiple image  */
  formData.append("pId", product.pId);
  formData.append("pName", product.pName);
  formData.append("pDescription", product.pDescription);
  formData.append("pStatus", product.pStatus);
  formData.append("pCategory", product.pCategory._id);
  formData.append("pQuantity", product.pQuantity);
  formData.append("pPrice", product.pPrice);
  formData.append("pOffer", product.pOffer);
  formData.append("pImages", product.pImages);

  try {
    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const deleteProduct = async (productId, userId) => {
  try {
    let res = await axios.delete(`${apiURL}/api/v1/products/${productId}?userId=${userId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const productByCategory = async (catId) => {
  try {
    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {
      catId,
    });
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const productByPrice = async (price) => {
  try {
    // Tạm thời filter client-side vì backend chưa có API filter by price
    let res = await axios.get(`${apiURL}/api/v1/products/customer`);
    const products = res.data;

    if (price === "all") {
      return products;
    }

    // Filter theo price range
    const priceRanges = {
      "0-100": [0, 100000],
      "100-500": [100000, 500000],
      "500-1000": [500000, 1000000],
      "1000+": [1000000, Infinity]
    };

    const range = priceRanges[price];
    if (range) {
      return products.filter(product =>
        product.price >= range[0] && product.price < range[1]
      );
    }

    return products;
  } catch (error) {
    console.log(error);
  }
};

// Thêm API mới cho product details
export const getProductDetails = async (productId, userId = 1) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Thêm API search products
export const searchProducts = async (keyword, userId = 1) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/products/customer/search?keyword=${keyword}&userId=${userId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Thêm API get related products
export const getRelatedProducts = async (productId) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
