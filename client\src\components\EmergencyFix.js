import React, { useEffect } from 'react';

const EmergencyFix = () => {
  useEffect(() => {
    // Chạy ngay khi component mount
    const emergencyFix = () => {
      console.log('🚨 EMERGENCY FIX: Tìm và loại bỏ overlay che phủ...');
      
      // 1. Tìm tất cả elements có position fixed/absolute
      const allElements = document.querySelectorAll('*');
      const problematicElements = [];
      
      allElements.forEach(el => {
        const style = getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        
        // Kiểm tra elements che phủ toàn màn hình
        if (
          (style.position === 'fixed' || style.position === 'absolute') &&
          rect.width > window.innerWidth * 0.8 &&
          rect.height > window.innerHeight * 0.8 &&
          (rect.top <= 0 || rect.left <= 0)
        ) {
          problematicElements.push({
            element: el,
            className: el.className,
            zIndex: style.zIndex,
            backgroundColor: style.backgroundColor,
            opacity: style.opacity
          });
        }
      });
      
      console.log('🔍 Tìm thấy các elements có thể gây vấn đề:', problematicElements);
      
      // 2. Loại bỏ các overlay có vấn đề
      problematicElements.forEach(item => {
        const { element, className } = item;
        
        // Không xóa các element quan trọng
        if (!className.includes('debug') && 
            !className.includes('emergency') && 
            !className.includes('quick-fix') &&
            !element.textContent.includes('Emergency') &&
            !element.textContent.includes('Debug')) {
          
          console.log('🗑️ Ẩn element:', className);
          element.style.display = 'none';
          element.style.pointerEvents = 'none';
        }
      });
      
      // 3. Force enable pointer events cho body
      document.body.style.pointerEvents = 'auto';
      document.documentElement.style.pointerEvents = 'auto';
      
      // 4. Loại bỏ các class có thể gây vấn đề
      document.querySelectorAll('.fixed').forEach(el => {
        const style = getComputedStyle(el);
        if (style.backgroundColor === 'rgba(0, 0, 0, 0.5)' || 
            style.backgroundColor === 'rgb(0, 0, 0)' ||
            el.className.includes('opacity-50')) {
          console.log('🗑️ Ẩn overlay đen:', el.className);
          el.style.display = 'none';
        }
      });
      
      // 5. Tạo test button để kiểm tra
      const createTestButton = () => {
        // Xóa test button cũ nếu có
        const oldButton = document.getElementById('emergency-test-button');
        if (oldButton) oldButton.remove();
        
        const testButton = document.createElement('button');
        testButton.id = 'emergency-test-button';
        testButton.innerHTML = '🧪 TEST CLICK';
        testButton.style.cssText = `
          position: fixed !important;
          top: 10px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          z-index: 99999 !important;
          background: #ff0000 !important;
          color: white !important;
          padding: 15px 30px !important;
          border: none !important;
          border-radius: 5px !important;
          font-size: 16px !important;
          font-weight: bold !important;
          cursor: pointer !important;
          pointer-events: auto !important;
          box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
        `;
        
        testButton.onclick = () => {
          alert('✅ THÀNH CÔNG! Click đã hoạt động!');
          testButton.style.background = '#00ff00';
          testButton.innerHTML = '✅ CLICK WORKS!';
          
          // Tự động ẩn sau 3 giây
          setTimeout(() => {
            testButton.style.display = 'none';
          }, 3000);
        };
        
        document.body.appendChild(testButton);
        console.log('✅ Đã tạo test button');
      };
      
      createTestButton();
      
      // 6. Log kết quả
      console.log('🔧 Emergency fix hoàn thành!');
      console.log('📋 Hướng dẫn:');
      console.log('1. Thử click nút TEST CLICK màu đỏ ở trên cùng');
      console.log('2. Nếu hoạt động → vấn đề đã được sửa');
      console.log('3. Nếu không hoạt động → vấn đề sâu hơn, cần kiểm tra JavaScript errors');
    };
    
    // Chạy fix ngay lập tức
    emergencyFix();
    
    // Chạy lại sau 1 giây để đảm bảo
    setTimeout(emergencyFix, 1000);
    
    // Chạy lại mỗi 5 giây để duy trì
    const interval = setInterval(emergencyFix, 5000);
    
    return () => clearInterval(interval);
  }, []);

  // Component này không render gì cả, chỉ chạy logic
  return null;
};

export default EmergencyFix;
