{"ast": null, "code": "import { createOrder } from \"./FetchApi\";\nexport const fetchData = async (cartListProduct, dispatch) => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  try {\n    let responseData = await cartListProduct();\n    console.log(\"🛒 Order Action: Cart data:\", responseData);\n    if (responseData && responseData.Products) {\n      setTimeout(function () {\n        dispatch({\n          type: \"cartProduct\",\n          payload: responseData.Products\n        });\n        dispatch({\n          type: \"loading\",\n          payload: false\n        });\n      }, 1000);\n    } else {\n      console.log(\"⚠️ Order Action: No products in cart\");\n      dispatch({\n        type: \"cartProduct\",\n        payload: []\n      });\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  } catch (error) {\n    console.error(\"❌ Order Action: Error fetching cart data:\", error);\n    dispatch({\n      type: \"loading\",\n      payload: false\n    });\n  }\n};\nexport const fetchbrainTree = async (getBrainTreeToken, setState) => {\n  try {\n    let responseData = await getBrainTreeToken();\n    if (responseData && responseData) {\n      setState({\n        clientToken: responseData.clientToken,\n        success: responseData.success\n      });\n      console.log(responseData);\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const pay = async (data, dispatch, state, setState, getPaymentProcess, totalCost, history) => {\n  console.log(state);\n  if (!state.address) {\n    setState({\n      ...state,\n      error: \"Please provide your address\"\n    });\n  } else if (!state.phone) {\n    setState({\n      ...state,\n      error: \"Please provide your phone number\"\n    });\n  } else {\n    let nonce;\n    state.instance.requestPaymentMethod().then(data => {\n      dispatch({\n        type: \"loading\",\n        payload: true\n      });\n      nonce = data.nonce;\n      let paymentData = {\n        amountTotal: totalCost(),\n        paymentMethod: nonce\n      };\n      getPaymentProcess(paymentData).then(async res => {\n        if (res) {\n          let orderData = {\n            allProduct: JSON.parse(localStorage.getItem(\"cart\")),\n            user: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\n            amount: res.transaction.amount,\n            transactionId: res.transaction.id,\n            address: state.address,\n            phone: state.phone\n          };\n          try {\n            let resposeData = await createOrder(orderData);\n            if (resposeData.success) {\n              localStorage.setItem(\"cart\", JSON.stringify([]));\n              dispatch({\n                type: \"cartProduct\",\n                payload: null\n              });\n              dispatch({\n                type: \"cartTotalCost\",\n                payload: null\n              });\n              dispatch({\n                type: \"orderSuccess\",\n                payload: true\n              });\n              setState({\n                clientToken: \"\",\n                instance: {}\n              });\n              dispatch({\n                type: \"loading\",\n                payload: false\n              });\n              return history.push(\"/\");\n            } else if (resposeData.error) {\n              console.log(resposeData.error);\n            }\n          } catch (error) {\n            console.log(error);\n          }\n        }\n      }).catch(err => {\n        console.log(err);\n      });\n    }).catch(error => {\n      console.log(error);\n      setState({\n        ...state,\n        error: error.message\n      });\n    });\n  }\n};", "map": {"version": 3, "names": ["createOrder", "fetchData", "cartListProduct", "dispatch", "type", "payload", "responseData", "console", "log", "Products", "setTimeout", "error", "fetchbrainTree", "getBrainTreeToken", "setState", "clientToken", "success", "pay", "data", "state", "getPaymentProcess", "totalCost", "history", "address", "phone", "nonce", "instance", "requestPaymentMethod", "then", "paymentData", "amountTotal", "paymentMethod", "res", "orderData", "allProduct", "JSON", "parse", "localStorage", "getItem", "user", "_id", "amount", "transaction", "transactionId", "id", "respose<PERSON><PERSON>", "setItem", "stringify", "push", "catch", "err", "message"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/Action.js"], "sourcesContent": ["import { createOrder } from \"./FetchApi\";\r\n\r\nexport const fetchData = async (cartListProduct, dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  try {\r\n    let responseData = await cartListProduct();\r\n    console.log(\"🛒 Order Action: Cart data:\", responseData);\r\n\r\n    if (responseData && responseData.Products) {\r\n      setTimeout(function () {\r\n        dispatch({ type: \"cartProduct\", payload: responseData.Products });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      }, 1000);\r\n    } else {\r\n      console.log(\"⚠️ Order Action: No products in cart\");\r\n      dispatch({ type: \"cartProduct\", payload: [] });\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"❌ Order Action: Error fetching cart data:\", error);\r\n    dispatch({ type: \"loading\", payload: false });\r\n  }\r\n};\r\n\r\nexport const fetchbrainTree = async (getBrainTreeToken, setState) => {\r\n  try {\r\n    let responseData = await getBrainTreeToken();\r\n    if (responseData && responseData) {\r\n      setState({\r\n        clientToken: responseData.clientToken,\r\n        success: responseData.success,\r\n      });\r\n      console.log(responseData);\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const pay = async (\r\n  data,\r\n  dispatch,\r\n  state,\r\n  setState,\r\n  getPaymentProcess,\r\n  totalCost,\r\n  history\r\n) => {\r\n  console.log(state);\r\n  if (!state.address) {\r\n    setState({ ...state, error: \"Please provide your address\" });\r\n  } else if (!state.phone) {\r\n    setState({ ...state, error: \"Please provide your phone number\" });\r\n  } else {\r\n    let nonce;\r\n    state.instance\r\n      .requestPaymentMethod()\r\n      .then((data) => {\r\n        dispatch({ type: \"loading\", payload: true });\r\n        nonce = data.nonce;\r\n        let paymentData = {\r\n          amountTotal: totalCost(),\r\n          paymentMethod: nonce,\r\n        };\r\n        getPaymentProcess(paymentData)\r\n          .then(async (res) => {\r\n            if (res) {\r\n              let orderData = {\r\n                allProduct: JSON.parse(localStorage.getItem(\"cart\")),\r\n                user: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\r\n                amount: res.transaction.amount,\r\n                transactionId: res.transaction.id,\r\n                address: state.address,\r\n                phone: state.phone,\r\n              };\r\n              try {\r\n                let resposeData = await createOrder(orderData);\r\n                if (resposeData.success) {\r\n                  localStorage.setItem(\"cart\", JSON.stringify([]));\r\n                  dispatch({ type: \"cartProduct\", payload: null });\r\n                  dispatch({ type: \"cartTotalCost\", payload: null });\r\n                  dispatch({ type: \"orderSuccess\", payload: true });\r\n                  setState({ clientToken: \"\", instance: {} });\r\n                  dispatch({ type: \"loading\", payload: false });\r\n                  return history.push(\"/\");\r\n                } else if (resposeData.error) {\r\n                  console.log(resposeData.error);\r\n                }\r\n              } catch (error) {\r\n                console.log(error);\r\n              }\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      })\r\n      .catch((error) => {\r\n        console.log(error);\r\n        setState({ ...state, error: error.message });\r\n      });\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AAExC,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAOC,eAAe,EAAEC,QAAQ,KAAK;EAC5DA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAI;IACF,IAAIC,YAAY,GAAG,MAAMJ,eAAe,CAAC,CAAC;IAC1CK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,YAAY,CAAC;IAExD,IAAIA,YAAY,IAAIA,YAAY,CAACG,QAAQ,EAAE;MACzCC,UAAU,CAAC,YAAY;QACrBP,QAAQ,CAAC;UAAEC,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEC,YAAY,CAACG;QAAS,CAAC,CAAC;QACjEN,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC/C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDL,QAAQ,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;MAC9CF,QAAQ,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjER,QAAQ,CAAC;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;EAC/C;AACF,CAAC;AAED,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAOC,iBAAiB,EAAEC,QAAQ,KAAK;EACnE,IAAI;IACF,IAAIR,YAAY,GAAG,MAAMO,iBAAiB,CAAC,CAAC;IAC5C,IAAIP,YAAY,IAAIA,YAAY,EAAE;MAChCQ,QAAQ,CAAC;QACPC,WAAW,EAAET,YAAY,CAACS,WAAW;QACrCC,OAAO,EAAEV,YAAY,CAACU;MACxB,CAAC,CAAC;MACFT,OAAO,CAACC,GAAG,CAACF,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdJ,OAAO,CAACC,GAAG,CAACG,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMM,GAAG,GAAG,MAAAA,CACjBC,IAAI,EACJf,QAAQ,EACRgB,KAAK,EACLL,QAAQ,EACRM,iBAAiB,EACjBC,SAAS,EACTC,OAAO,KACJ;EACHf,OAAO,CAACC,GAAG,CAACW,KAAK,CAAC;EAClB,IAAI,CAACA,KAAK,CAACI,OAAO,EAAE;IAClBT,QAAQ,CAAC;MAAE,GAAGK,KAAK;MAAER,KAAK,EAAE;IAA8B,CAAC,CAAC;EAC9D,CAAC,MAAM,IAAI,CAACQ,KAAK,CAACK,KAAK,EAAE;IACvBV,QAAQ,CAAC;MAAE,GAAGK,KAAK;MAAER,KAAK,EAAE;IAAmC,CAAC,CAAC;EACnE,CAAC,MAAM;IACL,IAAIc,KAAK;IACTN,KAAK,CAACO,QAAQ,CACXC,oBAAoB,CAAC,CAAC,CACtBC,IAAI,CAAEV,IAAI,IAAK;MACdf,QAAQ,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5CoB,KAAK,GAAGP,IAAI,CAACO,KAAK;MAClB,IAAII,WAAW,GAAG;QAChBC,WAAW,EAAET,SAAS,CAAC,CAAC;QACxBU,aAAa,EAAEN;MACjB,CAAC;MACDL,iBAAiB,CAACS,WAAW,CAAC,CAC3BD,IAAI,CAAC,MAAOI,GAAG,IAAK;QACnB,IAAIA,GAAG,EAAE;UACP,IAAIC,SAAS,GAAG;YACdC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;YACpDC,IAAI,EAAEJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG;YACtDC,MAAM,EAAET,GAAG,CAACU,WAAW,CAACD,MAAM;YAC9BE,aAAa,EAAEX,GAAG,CAACU,WAAW,CAACE,EAAE;YACjCrB,OAAO,EAAEJ,KAAK,CAACI,OAAO;YACtBC,KAAK,EAAEL,KAAK,CAACK;UACf,CAAC;UACD,IAAI;YACF,IAAIqB,WAAW,GAAG,MAAM7C,WAAW,CAACiC,SAAS,CAAC;YAC9C,IAAIY,WAAW,CAAC7B,OAAO,EAAE;cACvBqB,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEX,IAAI,CAACY,SAAS,CAAC,EAAE,CAAC,CAAC;cAChD5C,QAAQ,CAAC;gBAAEC,IAAI,EAAE,aAAa;gBAAEC,OAAO,EAAE;cAAK,CAAC,CAAC;cAChDF,QAAQ,CAAC;gBAAEC,IAAI,EAAE,eAAe;gBAAEC,OAAO,EAAE;cAAK,CAAC,CAAC;cAClDF,QAAQ,CAAC;gBAAEC,IAAI,EAAE,cAAc;gBAAEC,OAAO,EAAE;cAAK,CAAC,CAAC;cACjDS,QAAQ,CAAC;gBAAEC,WAAW,EAAE,EAAE;gBAAEW,QAAQ,EAAE,CAAC;cAAE,CAAC,CAAC;cAC3CvB,QAAQ,CAAC;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,OAAO,EAAE;cAAM,CAAC,CAAC;cAC7C,OAAOiB,OAAO,CAAC0B,IAAI,CAAC,GAAG,CAAC;YAC1B,CAAC,MAAM,IAAIH,WAAW,CAAClC,KAAK,EAAE;cAC5BJ,OAAO,CAACC,GAAG,CAACqC,WAAW,CAAClC,KAAK,CAAC;YAChC;UACF,CAAC,CAAC,OAAOA,KAAK,EAAE;YACdJ,OAAO,CAACC,GAAG,CAACG,KAAK,CAAC;UACpB;QACF;MACF,CAAC,CAAC,CACDsC,KAAK,CAAEC,GAAG,IAAK;QACd3C,OAAO,CAACC,GAAG,CAAC0C,GAAG,CAAC;MAClB,CAAC,CAAC;IACN,CAAC,CAAC,CACDD,KAAK,CAAEtC,KAAK,IAAK;MAChBJ,OAAO,CAACC,GAAG,CAACG,KAAK,CAAC;MAClBG,QAAQ,CAAC;QAAE,GAAGK,KAAK;QAAER,KAAK,EAAEA,KAAK,CAACwC;MAAQ,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}