import React, { useState } from 'react';

const ImageWithFallback = ({ 
  src, 
  alt, 
  className = '', 
  fallbackSrc = 'https://via.placeholder.com/400x400/e5e7eb/6b7280?text=No+Image',
  onClick,
  ...props 
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const getValidImageUrl = (imageUrl) => {
    if (!imageUrl) {
      return fallbackSrc;
    }
    
    // Check if it's a broken example.com URL
    if (imageUrl.includes('example.com')) {
      return fallbackSrc;
    }
    
    return imageUrl;
  };

  const handleError = (e) => {
    if (!hasError) {
      setHasError(true);
      setIsLoading(false);
      e.target.src = fallbackSrc;

      // Suppress console errors by overriding console.error temporarily
      const originalConsoleError = console.error;
      console.error = () => {};
      setTimeout(() => {
        console.error = originalConsoleError;
      }, 100);

      // Prevent default error handling
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <img
      src={hasError ? fallbackSrc : getValidImageUrl(src)}
      alt={alt}
      className={`${className} ${isLoading ? 'opacity-70' : 'opacity-100'} transition-opacity duration-200`}
      onClick={onClick}
      onError={handleError}
      onLoad={handleLoad}
      loading="lazy"
      {...props}
    />
  );
};

export default ImageWithFallback;
