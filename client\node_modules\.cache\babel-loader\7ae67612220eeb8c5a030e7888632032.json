{"ast": null, "code": "import capitalize from '@mui/utils/capitalize';\nexport default capitalize;", "map": {"version": 3, "names": ["capitalize"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/utils/capitalize.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nexport default capitalize;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}