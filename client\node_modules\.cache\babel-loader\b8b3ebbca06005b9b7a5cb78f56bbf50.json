{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nconst EmergencyFix = () => {\n  _s();\n  useEffect(() => {\n    // Chạy ngay khi component mount\n    const emergencyFix = () => {\n      console.log('🚨 EMERGENCY FIX: Tìm và loại bỏ overlay che phủ...');\n\n      // 1. Tìm tất cả elements có position fixed/absolute\n      const allElements = document.querySelectorAll('*');\n      const problematicElements = [];\n      allElements.forEach(el => {\n        const style = getComputedStyle(el);\n        const rect = el.getBoundingClientRect();\n\n        // Kiểm tra elements che phủ toàn màn hình\n        if ((style.position === 'fixed' || style.position === 'absolute') && rect.width > window.innerWidth * 0.8 && rect.height > window.innerHeight * 0.8 && (rect.top <= 0 || rect.left <= 0)) {\n          problematicElements.push({\n            element: el,\n            className: el.className,\n            zIndex: style.zIndex,\n            backgroundColor: style.backgroundColor,\n            opacity: style.opacity\n          });\n        }\n      });\n      console.log('🔍 Tìm thấy các elements có thể gây vấn đề:', problematicElements);\n\n      // 2. Loại bỏ các overlay có vấn đề\n      problematicElements.forEach(item => {\n        const {\n          element,\n          className\n        } = item;\n\n        // Không xóa các element quan trọng\n        if (!className.includes('debug') && !className.includes('emergency') && !className.includes('quick-fix') && !element.textContent.includes('Emergency') && !element.textContent.includes('Debug')) {\n          console.log('🗑️ Ẩn element:', className);\n          element.style.display = 'none';\n          element.style.pointerEvents = 'none';\n        }\n      });\n\n      // 3. Force enable pointer events cho body\n      document.body.style.pointerEvents = 'auto';\n      document.documentElement.style.pointerEvents = 'auto';\n\n      // 4. Loại bỏ các class có thể gây vấn đề\n      document.querySelectorAll('.fixed').forEach(el => {\n        const style = getComputedStyle(el);\n        if (style.backgroundColor === 'rgba(0, 0, 0, 0.5)' || style.backgroundColor === 'rgb(0, 0, 0)' || el.className.includes('opacity-50')) {\n          console.log('🗑️ Ẩn overlay đen:', el.className);\n          el.style.display = 'none';\n        }\n      });\n\n      // 5. Tạo test button để kiểm tra\n      const createTestButton = () => {\n        // Xóa test button cũ nếu có\n        const oldButton = document.getElementById('emergency-test-button');\n        if (oldButton) oldButton.remove();\n        const testButton = document.createElement('button');\n        testButton.id = 'emergency-test-button';\n        testButton.innerHTML = '🧪 TEST CLICK';\n        testButton.style.cssText = `\n          position: fixed !important;\n          top: 10px !important;\n          left: 50% !important;\n          transform: translateX(-50%) !important;\n          z-index: 99999 !important;\n          background: #ff0000 !important;\n          color: white !important;\n          padding: 15px 30px !important;\n          border: none !important;\n          border-radius: 5px !important;\n          font-size: 16px !important;\n          font-weight: bold !important;\n          cursor: pointer !important;\n          pointer-events: auto !important;\n          box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;\n        `;\n        testButton.onclick = () => {\n          alert('✅ THÀNH CÔNG! Click đã hoạt động!');\n          testButton.style.background = '#00ff00';\n          testButton.innerHTML = '✅ CLICK WORKS!';\n\n          // Tự động ẩn sau 3 giây\n          setTimeout(() => {\n            testButton.style.display = 'none';\n          }, 3000);\n        };\n        document.body.appendChild(testButton);\n        console.log('✅ Đã tạo test button');\n      };\n      createTestButton();\n\n      // 6. Log kết quả\n      console.log('🔧 Emergency fix hoàn thành!');\n      console.log('📋 Hướng dẫn:');\n      console.log('1. Thử click nút TEST CLICK màu đỏ ở trên cùng');\n      console.log('2. Nếu hoạt động → vấn đề đã được sửa');\n      console.log('3. Nếu không hoạt động → vấn đề sâu hơn, cần kiểm tra JavaScript errors');\n    };\n\n    // Chạy fix ngay lập tức\n    emergencyFix();\n\n    // Chạy lại sau 1 giây để đảm bảo\n    setTimeout(emergencyFix, 1000);\n\n    // Chạy lại mỗi 5 giây để duy trì\n    const interval = setInterval(emergencyFix, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Component này không render gì cả, chỉ chạy logic\n  return null;\n};\n_s(EmergencyFix, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = EmergencyFix;\nexport default EmergencyFix;\nvar _c;\n$RefreshReg$(_c, \"EmergencyFix\");", "map": {"version": 3, "names": ["React", "useEffect", "EmergencyFix", "_s", "emergencyFix", "console", "log", "allElements", "document", "querySelectorAll", "problematicElements", "for<PERSON>ach", "el", "style", "getComputedStyle", "rect", "getBoundingClientRect", "position", "width", "window", "innerWidth", "height", "innerHeight", "top", "left", "push", "element", "className", "zIndex", "backgroundColor", "opacity", "item", "includes", "textContent", "display", "pointerEvents", "body", "documentElement", "createTestButton", "oldButton", "getElementById", "remove", "testButton", "createElement", "id", "innerHTML", "cssText", "onclick", "alert", "background", "setTimeout", "append<PERSON><PERSON><PERSON>", "interval", "setInterval", "clearInterval", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/EmergencyFix.js"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nconst EmergencyFix = () => {\n  useEffect(() => {\n    // Chạy ngay khi component mount\n    const emergencyFix = () => {\n      console.log('🚨 EMERGENCY FIX: Tìm và loại bỏ overlay che phủ...');\n      \n      // 1. Tìm tất cả elements có position fixed/absolute\n      const allElements = document.querySelectorAll('*');\n      const problematicElements = [];\n      \n      allElements.forEach(el => {\n        const style = getComputedStyle(el);\n        const rect = el.getBoundingClientRect();\n        \n        // Kiểm tra elements che phủ toàn màn hình\n        if (\n          (style.position === 'fixed' || style.position === 'absolute') &&\n          rect.width > window.innerWidth * 0.8 &&\n          rect.height > window.innerHeight * 0.8 &&\n          (rect.top <= 0 || rect.left <= 0)\n        ) {\n          problematicElements.push({\n            element: el,\n            className: el.className,\n            zIndex: style.zIndex,\n            backgroundColor: style.backgroundColor,\n            opacity: style.opacity\n          });\n        }\n      });\n      \n      console.log('🔍 Tìm thấy các elements có thể gây vấn đề:', problematicElements);\n      \n      // 2. Loại bỏ các overlay có vấn đề\n      problematicElements.forEach(item => {\n        const { element, className } = item;\n        \n        // Không xóa các element quan trọng\n        if (!className.includes('debug') && \n            !className.includes('emergency') && \n            !className.includes('quick-fix') &&\n            !element.textContent.includes('Emergency') &&\n            !element.textContent.includes('Debug')) {\n          \n          console.log('🗑️ Ẩn element:', className);\n          element.style.display = 'none';\n          element.style.pointerEvents = 'none';\n        }\n      });\n      \n      // 3. Force enable pointer events cho body\n      document.body.style.pointerEvents = 'auto';\n      document.documentElement.style.pointerEvents = 'auto';\n      \n      // 4. Loại bỏ các class có thể gây vấn đề\n      document.querySelectorAll('.fixed').forEach(el => {\n        const style = getComputedStyle(el);\n        if (style.backgroundColor === 'rgba(0, 0, 0, 0.5)' || \n            style.backgroundColor === 'rgb(0, 0, 0)' ||\n            el.className.includes('opacity-50')) {\n          console.log('🗑️ Ẩn overlay đen:', el.className);\n          el.style.display = 'none';\n        }\n      });\n      \n      // 5. Tạo test button để kiểm tra\n      const createTestButton = () => {\n        // Xóa test button cũ nếu có\n        const oldButton = document.getElementById('emergency-test-button');\n        if (oldButton) oldButton.remove();\n        \n        const testButton = document.createElement('button');\n        testButton.id = 'emergency-test-button';\n        testButton.innerHTML = '🧪 TEST CLICK';\n        testButton.style.cssText = `\n          position: fixed !important;\n          top: 10px !important;\n          left: 50% !important;\n          transform: translateX(-50%) !important;\n          z-index: 99999 !important;\n          background: #ff0000 !important;\n          color: white !important;\n          padding: 15px 30px !important;\n          border: none !important;\n          border-radius: 5px !important;\n          font-size: 16px !important;\n          font-weight: bold !important;\n          cursor: pointer !important;\n          pointer-events: auto !important;\n          box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;\n        `;\n        \n        testButton.onclick = () => {\n          alert('✅ THÀNH CÔNG! Click đã hoạt động!');\n          testButton.style.background = '#00ff00';\n          testButton.innerHTML = '✅ CLICK WORKS!';\n          \n          // Tự động ẩn sau 3 giây\n          setTimeout(() => {\n            testButton.style.display = 'none';\n          }, 3000);\n        };\n        \n        document.body.appendChild(testButton);\n        console.log('✅ Đã tạo test button');\n      };\n      \n      createTestButton();\n      \n      // 6. Log kết quả\n      console.log('🔧 Emergency fix hoàn thành!');\n      console.log('📋 Hướng dẫn:');\n      console.log('1. Thử click nút TEST CLICK màu đỏ ở trên cùng');\n      console.log('2. Nếu hoạt động → vấn đề đã được sửa');\n      console.log('3. Nếu không hoạt động → vấn đề sâu hơn, cần kiểm tra JavaScript errors');\n    };\n    \n    // Chạy fix ngay lập tức\n    emergencyFix();\n    \n    // Chạy lại sau 1 giây để đảm bảo\n    setTimeout(emergencyFix, 1000);\n    \n    // Chạy lại mỗi 5 giây để duy trì\n    const interval = setInterval(emergencyFix, 5000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  // Component này không render gì cả, chỉ chạy logic\n  return null;\n};\n\nexport default EmergencyFix;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAExC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzBF,SAAS,CAAC,MAAM;IACd;IACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzBC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;MAElE;MACA,MAAMC,WAAW,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,GAAG,CAAC;MAClD,MAAMC,mBAAmB,GAAG,EAAE;MAE9BH,WAAW,CAACI,OAAO,CAACC,EAAE,IAAI;QACxB,MAAMC,KAAK,GAAGC,gBAAgB,CAACF,EAAE,CAAC;QAClC,MAAMG,IAAI,GAAGH,EAAE,CAACI,qBAAqB,CAAC,CAAC;;QAEvC;QACA,IACE,CAACH,KAAK,CAACI,QAAQ,KAAK,OAAO,IAAIJ,KAAK,CAACI,QAAQ,KAAK,UAAU,KAC5DF,IAAI,CAACG,KAAK,GAAGC,MAAM,CAACC,UAAU,GAAG,GAAG,IACpCL,IAAI,CAACM,MAAM,GAAGF,MAAM,CAACG,WAAW,GAAG,GAAG,KACrCP,IAAI,CAACQ,GAAG,IAAI,CAAC,IAAIR,IAAI,CAACS,IAAI,IAAI,CAAC,CAAC,EACjC;UACAd,mBAAmB,CAACe,IAAI,CAAC;YACvBC,OAAO,EAAEd,EAAE;YACXe,SAAS,EAAEf,EAAE,CAACe,SAAS;YACvBC,MAAM,EAAEf,KAAK,CAACe,MAAM;YACpBC,eAAe,EAAEhB,KAAK,CAACgB,eAAe;YACtCC,OAAO,EAAEjB,KAAK,CAACiB;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEFzB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEI,mBAAmB,CAAC;;MAE/E;MACAA,mBAAmB,CAACC,OAAO,CAACoB,IAAI,IAAI;QAClC,MAAM;UAAEL,OAAO;UAAEC;QAAU,CAAC,GAAGI,IAAI;;QAEnC;QACA,IAAI,CAACJ,SAAS,CAACK,QAAQ,CAAC,OAAO,CAAC,IAC5B,CAACL,SAAS,CAACK,QAAQ,CAAC,WAAW,CAAC,IAChC,CAACL,SAAS,CAACK,QAAQ,CAAC,WAAW,CAAC,IAChC,CAACN,OAAO,CAACO,WAAW,CAACD,QAAQ,CAAC,WAAW,CAAC,IAC1C,CAACN,OAAO,CAACO,WAAW,CAACD,QAAQ,CAAC,OAAO,CAAC,EAAE;UAE1C3B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqB,SAAS,CAAC;UACzCD,OAAO,CAACb,KAAK,CAACqB,OAAO,GAAG,MAAM;UAC9BR,OAAO,CAACb,KAAK,CAACsB,aAAa,GAAG,MAAM;QACtC;MACF,CAAC,CAAC;;MAEF;MACA3B,QAAQ,CAAC4B,IAAI,CAACvB,KAAK,CAACsB,aAAa,GAAG,MAAM;MAC1C3B,QAAQ,CAAC6B,eAAe,CAACxB,KAAK,CAACsB,aAAa,GAAG,MAAM;;MAErD;MACA3B,QAAQ,CAACC,gBAAgB,CAAC,QAAQ,CAAC,CAACE,OAAO,CAACC,EAAE,IAAI;QAChD,MAAMC,KAAK,GAAGC,gBAAgB,CAACF,EAAE,CAAC;QAClC,IAAIC,KAAK,CAACgB,eAAe,KAAK,oBAAoB,IAC9ChB,KAAK,CAACgB,eAAe,KAAK,cAAc,IACxCjB,EAAE,CAACe,SAAS,CAACK,QAAQ,CAAC,YAAY,CAAC,EAAE;UACvC3B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEM,EAAE,CAACe,SAAS,CAAC;UAChDf,EAAE,CAACC,KAAK,CAACqB,OAAO,GAAG,MAAM;QAC3B;MACF,CAAC,CAAC;;MAEF;MACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;QAC7B;QACA,MAAMC,SAAS,GAAG/B,QAAQ,CAACgC,cAAc,CAAC,uBAAuB,CAAC;QAClE,IAAID,SAAS,EAAEA,SAAS,CAACE,MAAM,CAAC,CAAC;QAEjC,MAAMC,UAAU,GAAGlC,QAAQ,CAACmC,aAAa,CAAC,QAAQ,CAAC;QACnDD,UAAU,CAACE,EAAE,GAAG,uBAAuB;QACvCF,UAAU,CAACG,SAAS,GAAG,eAAe;QACtCH,UAAU,CAAC7B,KAAK,CAACiC,OAAO,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;QAEDJ,UAAU,CAACK,OAAO,GAAG,MAAM;UACzBC,KAAK,CAAC,mCAAmC,CAAC;UAC1CN,UAAU,CAAC7B,KAAK,CAACoC,UAAU,GAAG,SAAS;UACvCP,UAAU,CAACG,SAAS,GAAG,gBAAgB;;UAEvC;UACAK,UAAU,CAAC,MAAM;YACfR,UAAU,CAAC7B,KAAK,CAACqB,OAAO,GAAG,MAAM;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QAED1B,QAAQ,CAAC4B,IAAI,CAACe,WAAW,CAACT,UAAU,CAAC;QACrCrC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACrC,CAAC;MAEDgC,gBAAgB,CAAC,CAAC;;MAElB;MACAjC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5BD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpDD,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;IACxF,CAAC;;IAED;IACAF,YAAY,CAAC,CAAC;;IAEd;IACA8C,UAAU,CAAC9C,YAAY,EAAE,IAAI,CAAC;;IAE9B;IACA,MAAMgD,QAAQ,GAAGC,WAAW,CAACjD,YAAY,EAAE,IAAI,CAAC;IAEhD,OAAO,MAAMkD,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,OAAO,IAAI;AACb,CAAC;AAACjD,EAAA,CAnIID,YAAY;AAAAqD,EAAA,GAAZrD,YAAY;AAqIlB,eAAeA,YAAY;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}