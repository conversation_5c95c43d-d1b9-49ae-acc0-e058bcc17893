{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\dashboardUser\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer, useEffect } from \"react\";\nimport { Navber, Footer, CartModal } from \"../partials\";\nimport Sidebar from \"./Sidebar\";\nimport { dashboardUserState, dashboardUserReducer } from \"./DashboardUserContext\";\nimport { fetchData } from \"./Action\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DashboardUserContext = /*#__PURE__*/createContext();\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [data, dispatch] = useReducer(dashboardUserReducer, dashboardUserState);\n  useEffect(() => {\n    fetchData(dispatch);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(DashboardUserContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-grow\",\n        children: [/*#__PURE__*/_jsxDEV(Navber, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CartModal, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-4 mt-24 md:mx-12 md:mt-32 lg:mt-24 flex flex-col md:flex-row\",\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), children]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"wry4B76nnfGLiY0zM3/xPsSOeJE=\");\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "useEffect", "<PERSON><PERSON><PERSON>", "Footer", "CartModal", "Sidebar", "dashboardUserState", "dashboardUserReducer", "fetchData", "jsxDEV", "_jsxDEV", "DashboardUserContext", "Layout", "children", "_s", "data", "dispatch", "Provider", "value", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/Layout.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer, useEffect } from \"react\";\r\nimport { Navber, Footer, CartModal } from \"../partials\";\r\nimport Sidebar from \"./Sidebar\";\r\nimport {\r\n  dashboardUserState,\r\n  dashboardUserReducer,\r\n} from \"./DashboardUserContext\";\r\nimport { fetchData } from \"./Action\";\r\n\r\nexport const DashboardUserContext = createContext();\r\n\r\nconst Layout = ({ children }) => {\r\n  const [data, dispatch] = useReducer(dashboardUserReducer, dashboardUserState);\r\n\r\n  useEffect(() => {\r\n    fetchData(dispatch);\r\n  }, []);\r\n\r\n  return (\r\n    <Fragment>\r\n      <DashboardUserContext.Provider value={{ data, dispatch }}>\r\n        <div className=\"flex-grow\">\r\n          <Navber />\r\n          <CartModal />\r\n          <div className=\"mx-4 mt-24 md:mx-12 md:mt-32 lg:mt-24 flex flex-col md:flex-row\">\r\n            <Sidebar />\r\n            {/* All Children pass from here */}\r\n            {children}\r\n          </div>\r\n        </div>\r\n        <Footer />\r\n      </DashboardUserContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,aAAa;AACvD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SACEC,kBAAkB,EAClBC,oBAAoB,QACf,wBAAwB;AAC/B,SAASC,SAAS,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,oBAAoB,gBAAGZ,aAAa,CAAC,CAAC;AAEnD,MAAMa,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGhB,UAAU,CAACO,oBAAoB,EAAED,kBAAkB,CAAC;EAE7EL,SAAS,CAAC,MAAM;IACdO,SAAS,CAACQ,QAAQ,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEN,OAAA,CAACZ,QAAQ;IAAAe,QAAA,eACPH,OAAA,CAACC,oBAAoB,CAACM,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAH,QAAA,gBACvDH,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAN,QAAA,gBACxBH,OAAA,CAACR,MAAM;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVb,OAAA,CAACN,SAAS;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACbb,OAAA;UAAKS,SAAS,EAAC,iEAAiE;UAAAN,QAAA,gBAC9EH,OAAA,CAACL,OAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAEVV,QAAQ;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNb,OAAA,CAACP,MAAM;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACmB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxB,CAAC;AAEf,CAAC;AAACT,EAAA,CAvBIF,MAAM;AAAAY,EAAA,GAANZ,MAAM;AAyBZ,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}