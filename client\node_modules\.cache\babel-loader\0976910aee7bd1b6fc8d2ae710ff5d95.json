{"ast": null, "code": "'use client';\n\nexport { default } from './Stack';\nexport { default as createStack } from './createStack';\nexport * from './StackProps';\nexport { default as stackClasses } from './stackClasses';\nexport * from './stackClasses';", "map": {"version": 3, "names": ["default", "createStack", "stackClasses"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/system/esm/Stack/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Stack';\nexport { default as createStack } from './createStack';\nexport * from './StackProps';\nexport { default as stackClasses } from './stackClasses';\nexport * from './stackClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,WAAW,QAAQ,eAAe;AACtD,cAAc,cAAc;AAC5B,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}