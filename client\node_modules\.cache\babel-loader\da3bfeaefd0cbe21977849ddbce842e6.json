{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport App from \"./App\";\nimport * as serviceWorker from \"./serviceWorker\";\nimport { SnackbarProvider } from 'notistack';\nimport { BrowserRouter } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(SnackbarProvider, {\n      maxSnack: 3,\n      children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this), document.getElementById(\"root\"));\nserviceWorker.unregister();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "serviceWorker", "SnackbarProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "render", "StrictMode", "children", "maxSnack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById", "unregister"], "sources": ["D:/ITSS_Reference/client/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom\";\r\nimport App from \"./App\";\r\nimport * as serviceWorker from \"./serviceWorker\";\r\nimport { SnackbarProvider } from 'notistack';\r\nimport { <PERSON><PERSON>erRouter } from \"react-router-dom\";\r\n\r\nReactDOM.render(\r\n  <React.StrictMode>\r\n    <BrowserRouter>\r\n      <SnackbarProvider maxSnack={3}>\r\n        <App />\r\n      </SnackbarProvider>\r\n    </BrowserRouter>\r\n  </React.StrictMode>,\r\n  document.getElementById(\"root\")\r\n);\r\n\r\nserviceWorker.unregister();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,KAAKC,aAAa,MAAM,iBAAiB;AAChD,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjDN,QAAQ,CAACO,MAAM,cACbD,OAAA,CAACP,KAAK,CAACS,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACF,aAAa;IAAAK,QAAA,eACZH,OAAA,CAACH,gBAAgB;MAACO,QAAQ,EAAE,CAAE;MAAAD,QAAA,eAC5BH,OAAA,CAACL,GAAG;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CAAC,EACnBC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDd,aAAa,CAACe,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}