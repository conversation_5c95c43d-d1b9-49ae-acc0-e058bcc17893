{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const wishListProducts = async () => {\n  let productArray = JSON.parse(localStorage.getItem(\"wishList\"));\n  try {\n    let res = await axios.post(`${apiURL}/api/product/wish-product`, {\n      productArray\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "wishListProducts", "productArray", "JSON", "parse", "localStorage", "getItem", "res", "post", "data", "error", "console", "log"], "sources": ["D:/ITSS_Reference/client/src/components/shop/wishlist/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const wishListProducts = async () => {\r\n  let productArray = JSON.parse(localStorage.getItem(\"wishList\"));\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/wish-product`, {\r\n      productArray,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,IAAIC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;EAC/D,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMX,KAAK,CAACY,IAAI,CAAC,GAAGX,MAAM,2BAA2B,EAAE;MAC/DK;IACF,CAAC,CAAC;IACF,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}