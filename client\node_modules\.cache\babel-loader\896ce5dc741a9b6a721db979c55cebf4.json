{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst base = `${apiURL}/api/v1/orders`;\nexport const createOrder = async orderData => {\n  try {\n    let res = await axios.post(`${base}/create`, orderData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const placeOrder = async orderData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/orders/place`, orderData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Payment for order - Updated to match backend endpoint\nexport const payOrder = async (orderId, paymentData) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/orders/${orderId}/pay`, paymentData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get order details by ID\nexport const getOrderDetails = async orderId => {\n  try {\n    const res = await axios.get(`${base}/${orderId}`);\n    return res.data;\n  } catch (error) {\n    console.error(\"Get order details failed:\", error);\n    throw error;\n  }\n};\n\n// Set delivery info\nexport const setDeliveryInfo = async (orderId, deliveryInfoDTO) => {\n  try {\n    const res = await axios.put(`${base}/${orderId}/delivery`, deliveryInfoDTO);\n    return res.data;\n  } catch (error) {\n    console.error(\"Set delivery info failed:\", error);\n    throw error;\n  }\n};\n\n// VNPay payment - Updated to match backend endpoint\nexport const createVNPayPayment = async (orderId, paymentData) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/payment/${orderId}`, paymentData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Set rush delivery info\nexport const setRushDeliveryInfo = async (orderId, rushInfoDTO) => {\n  try {\n    const res = await axios.put(`${base}/${orderId}/rush-delivery`, rushInfoDTO);\n    return res.data;\n  } catch (error) {\n    console.error(\"Set rush delivery info failed:\", error);\n    throw error;\n  }\n};\n\n// Get invoice for order\nexport const getInvoice = async orderId => {\n  try {\n    const res = await axios.get(`${base}/${orderId}/invoice`);\n    return res.data;\n  } catch (error) {\n    console.error(\"Get invoice failed:\", error);\n    throw error;\n  }\n};\n\n// Get order history for a customer\nexport const getOrderHistory = async customerId => {\n  try {\n    const res = await axios.get(`${base}/history/${customerId}`);\n    return res.data;\n  } catch (error) {\n    console.error(\"Get order history failed:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "base", "createOrder", "orderData", "res", "post", "headers", "data", "error", "console", "log", "placeOrder", "payOrder", "orderId", "paymentData", "getOrderDetails", "get", "setDeliveryInfo", "deliveryInfoDTO", "put", "createVNPayPayment", "setRushDeliveryInfo", "rushInfoDTO", "getInvoice", "getOrderHistory", "customerId"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\nconst base = `${apiURL}/api/v1/orders`;\r\n\r\nexport const createOrder = async (orderData) => {\r\n  try {\r\n    let res = await axios.post(`${base}/create`, orderData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const placeOrder = async (orderData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/orders/place`, orderData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Payment for order - Updated to match backend endpoint\r\nexport const payOrder = async (orderId, paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/orders/${orderId}/pay`, paymentData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get order details by ID\r\nexport const getOrderDetails = async (orderId) => {\r\n  try {\r\n    const res = await axios.get(`${base}/${orderId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"Get order details failed:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Set delivery info\r\nexport const setDeliveryInfo = async (orderId, deliveryInfoDTO) => {\r\n  try {\r\n    const res = await axios.put(`${base}/${orderId}/delivery`, deliveryInfoDTO);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"Set delivery info failed:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n// VNPay payment - Updated to match backend endpoint\r\nexport const createVNPayPayment = async (orderId, paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/payment/${orderId}`, paymentData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n// Set rush delivery info\r\nexport const setRushDeliveryInfo = async (orderId, rushInfoDTO) => {\r\n  try {\r\n    const res = await axios.put(`${base}/${orderId}/rush-delivery`, rushInfoDTO);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"Set rush delivery info failed:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get invoice for order\r\nexport const getInvoice = async (orderId) => {\r\n  try {\r\n    const res = await axios.get(`${base}/${orderId}/invoice`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"Get invoice failed:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n// Get order history for a customer\r\nexport const getOrderHistory = async (customerId) => {\r\n  try {\r\n    const res = await axios.get(`${base}/history/${customerId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"Get order history failed:\", error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAC5C,MAAMC,IAAI,GAAG,GAAGJ,MAAM,gBAAgB;AAEtC,OAAO,MAAMK,WAAW,GAAG,MAAOC,SAAS,IAAK;EAC9C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMR,KAAK,CAACS,IAAI,CAAC,GAAGJ,IAAI,SAAS,EAAEE,SAAS,EAAE;MACtDG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG,MAAOR,SAAS,IAAK;EAC7C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMR,KAAK,CAACS,IAAI,CAAC,GAAGR,MAAM,sBAAsB,EAAEM,SAAS,EAAE;MACrEG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,QAAQ,GAAG,MAAAA,CAAOC,OAAO,EAAEC,WAAW,KAAK;EACtD,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMR,KAAK,CAACS,IAAI,CAAC,GAAGR,MAAM,kBAAkBgB,OAAO,MAAM,EAAEC,WAAW,EAAE;MAChFR,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,eAAe,GAAG,MAAOF,OAAO,IAAK;EAChD,IAAI;IACF,MAAMT,GAAG,GAAG,MAAMR,KAAK,CAACoB,GAAG,CAAC,GAAGf,IAAI,IAAIY,OAAO,EAAE,CAAC;IACjD,OAAOT,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAAG,MAAAA,CAAOJ,OAAO,EAAEK,eAAe,KAAK;EACjE,IAAI;IACF,MAAMd,GAAG,GAAG,MAAMR,KAAK,CAACuB,GAAG,CAAC,GAAGlB,IAAI,IAAIY,OAAO,WAAW,EAAEK,eAAe,CAAC;IAC3E,OAAOd,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK;EACb;AACF,CAAC;;AAGD;AACA,OAAO,MAAMY,kBAAkB,GAAG,MAAAA,CAAOP,OAAO,EAAEC,WAAW,KAAK;EAChE,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMR,KAAK,CAACS,IAAI,CAAC,GAAGR,MAAM,mBAAmBgB,OAAO,EAAE,EAAEC,WAAW,EAAE;MAC7ER,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAGD;AACA,OAAO,MAAMa,mBAAmB,GAAG,MAAAA,CAAOR,OAAO,EAAES,WAAW,KAAK;EACjE,IAAI;IACF,MAAMlB,GAAG,GAAG,MAAMR,KAAK,CAACuB,GAAG,CAAC,GAAGlB,IAAI,IAAIY,OAAO,gBAAgB,EAAES,WAAW,CAAC;IAC5E,OAAOlB,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMe,UAAU,GAAG,MAAOV,OAAO,IAAK;EAC3C,IAAI;IACF,MAAMT,GAAG,GAAG,MAAMR,KAAK,CAACoB,GAAG,CAAC,GAAGf,IAAI,IAAIY,OAAO,UAAU,CAAC;IACzD,OAAOT,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,MAAMA,KAAK;EACb;AACF,CAAC;;AAGD;AACA,OAAO,MAAMgB,eAAe,GAAG,MAAOC,UAAU,IAAK;EACnD,IAAI;IACF,MAAMrB,GAAG,GAAG,MAAMR,KAAK,CAACoB,GAAG,CAAC,GAAGf,IAAI,YAAYwB,UAAU,EAAE,CAAC;IAC5D,OAAOrB,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}