{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\auth\\\\ProtectedRoute.js\";\nimport React from \"react\";\nimport { Route, Redirect } from \"react-router-dom\";\nimport { isAuthenticate, isAdmin } from \"./fetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  component: Component,\n  ...rest\n}) => /*#__PURE__*/_jsxDEV(Route, {\n  ...rest,\n  render: props => isAuthenticate() && !isAdmin() ? /*#__PURE__*/_jsxDEV(Component, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(Redirect, {\n    to: {\n      pathname: \"/\",\n      state: {\n        from: props.location\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this);\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Route", "Redirect", "isAuthenticate", "isAdmin", "jsxDEV", "_jsxDEV", "ProtectedRoute", "component", "Component", "rest", "render", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "state", "from", "location", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/auth/ProtectedRoute.js"], "sourcesContent": ["import React from \"react\";\r\nimport { Route, Redirect } from \"react-router-dom\";\r\nimport { isAuthenticate, isAdmin } from \"./fetchApi\";\r\n\r\nconst ProtectedRoute = ({ component: Component, ...rest }) => (\r\n  <Route\r\n    {...rest}\r\n    render={(props) =>\r\n      isAuthenticate() && !isAdmin() ? (\r\n        <Component {...props} />\r\n      ) : (\r\n        <Redirect\r\n          to={{\r\n            pathname: \"/\",\r\n            state: { from: props.location },\r\n          }}\r\n        />\r\n      )\r\n    }\r\n  />\r\n);\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAClD,SAASC,cAAc,EAAEC,OAAO,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,SAAS,EAAEC,SAAS;EAAE,GAAGC;AAAK,CAAC,kBACvDJ,OAAA,CAACL,KAAK;EAAA,GACAS,IAAI;EACRC,MAAM,EAAGC,KAAK,IACZT,cAAc,CAAC,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,gBAC5BE,OAAA,CAACG,SAAS;IAAA,GAAKG;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,gBAExBV,OAAA,CAACJ,QAAQ;IACPe,EAAE,EAAE;MACFC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;QAAEC,IAAI,EAAER,KAAK,CAACS;MAAS;IAChC;EAAE;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAEJ;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACF,CACF;AAACM,EAAA,GAhBIf,cAAc;AAkBpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}