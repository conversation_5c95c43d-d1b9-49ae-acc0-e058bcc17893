{"ast": null, "code": "import { getAllOrder, deleteOrder } from \"./FetchApi\";\nexport const fetchData = async dispatch => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  let responseData = await getAllOrder();\n  setTimeout(() => {\n    if (responseData && responseData.Orders) {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData.Orders\n      });\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  }, 1000);\n};\n\n/* This method call the editmodal & dispatch category context */\nexport const editOrderReq = (oId, type, status, dispatch) => {\n  if (type) {\n    console.log(\"click update\");\n    dispatch({\n      type: \"updateOrderModalOpen\",\n      oId: oId,\n      status: status\n    });\n  }\n};\nexport const deleteOrderReq = async (oId, dispatch) => {\n  let responseData = await deleteOrder(oId);\n  console.log(responseData);\n  if (responseData && responseData.success) {\n    fetchData(dispatch);\n  }\n};\n\n/* Filter All Order */\nexport const filterOrder = async (type, data, dispatch, dropdown, setDropdown) => {\n  let responseData = await getAllOrder();\n  if (responseData && responseData.Orders) {\n    let newData;\n    if (type === \"All\") {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData.Orders\n      });\n      setDropdown(!dropdown);\n    } else if (type === \"Not processed\") {\n      newData = responseData.Orders.filter(item => item.status === \"Not processed\");\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: newData\n      });\n      setDropdown(!dropdown);\n    } else if (type === \"Processing\") {\n      newData = responseData.Orders.filter(item => item.status === \"Processing\");\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: newData\n      });\n      setDropdown(!dropdown);\n    } else if (type === \"Shipped\") {\n      newData = responseData.Orders.filter(item => item.status === \"Shipped\");\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: newData\n      });\n      setDropdown(!dropdown);\n    } else if (type === \"Delivered\") {\n      newData = responseData.Orders.filter(item => item.status === \"Delivered\");\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: newData\n      });\n      setDropdown(!dropdown);\n    } else if (type === \"Cancelled\") {\n      newData = responseData.Orders.filter(item => item.status === \"Cancelled\");\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: newData\n      });\n      setDropdown(!dropdown);\n    }\n  }\n};", "map": {"version": 3, "names": ["getAllOrder", "deleteOrder", "fetchData", "dispatch", "type", "payload", "responseData", "setTimeout", "Orders", "editOrderReq", "oId", "status", "console", "log", "deleteOrderReq", "success", "filterOrder", "data", "dropdown", "setDropdown", "newData", "filter", "item"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/Actions.js"], "sourcesContent": ["import { getAllOrder, deleteOrder } from \"./FetchApi\";\r\n\r\nexport const fetchData = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let responseData = await getAllOrder();\r\n  setTimeout(() => {\r\n    if (responseData && responseData.Orders) {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData.Orders,\r\n      });\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  }, 1000);\r\n};\r\n\r\n/* This method call the editmodal & dispatch category context */\r\nexport const editOrderReq = (oId, type, status, dispatch) => {\r\n  if (type) {\r\n    console.log(\"click update\");\r\n    dispatch({ type: \"updateOrderModalOpen\", oId: oId, status: status });\r\n  }\r\n};\r\n\r\nexport const deleteOrderReq = async (oId, dispatch) => {\r\n  let responseData = await deleteOrder(oId);\r\n  console.log(responseData);\r\n  if (responseData && responseData.success) {\r\n    fetchData(dispatch);\r\n  }\r\n};\r\n\r\n/* Filter All Order */\r\nexport const filterOrder = async (\r\n  type,\r\n  data,\r\n  dispatch,\r\n  dropdown,\r\n  setDropdown\r\n) => {\r\n  let responseData = await getAllOrder();\r\n  if (responseData && responseData.Orders) {\r\n    let newData;\r\n    if (type === \"All\") {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData.Orders,\r\n      });\r\n      setDropdown(!dropdown);\r\n    } else if (type === \"Not processed\") {\r\n      newData = responseData.Orders.filter(\r\n        (item) => item.status === \"Not processed\"\r\n      );\r\n      dispatch({ type: \"fetchOrderAndChangeState\", payload: newData });\r\n      setDropdown(!dropdown);\r\n    } else if (type === \"Processing\") {\r\n      newData = responseData.Orders.filter(\r\n        (item) => item.status === \"Processing\"\r\n      );\r\n      dispatch({ type: \"fetchOrderAndChangeState\", payload: newData });\r\n      setDropdown(!dropdown);\r\n    } else if (type === \"Shipped\") {\r\n      newData = responseData.Orders.filter((item) => item.status === \"Shipped\");\r\n      dispatch({ type: \"fetchOrderAndChangeState\", payload: newData });\r\n      setDropdown(!dropdown);\r\n    } else if (type === \"Delivered\") {\r\n      newData = responseData.Orders.filter(\r\n        (item) => item.status === \"Delivered\"\r\n      );\r\n      dispatch({ type: \"fetchOrderAndChangeState\", payload: newData });\r\n      setDropdown(!dropdown);\r\n    } else if (type === \"Cancelled\") {\r\n      newData = responseData.Orders.filter(\r\n        (item) => item.status === \"Cancelled\"\r\n      );\r\n      dispatch({ type: \"fetchOrderAndChangeState\", payload: newData });\r\n      setDropdown(!dropdown);\r\n    }\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,YAAY;AAErD,OAAO,MAAMC,SAAS,GAAG,MAAOC,QAAQ,IAAK;EAC3CA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAIC,YAAY,GAAG,MAAMN,WAAW,CAAC,CAAC;EACtCO,UAAU,CAAC,MAAM;IACf,IAAID,YAAY,IAAIA,YAAY,CAACE,MAAM,EAAE;MACvCL,QAAQ,CAAC;QACPC,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEC,YAAY,CAACE;MACxB,CAAC,CAAC;MACFL,QAAQ,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA,OAAO,MAAMI,YAAY,GAAGA,CAACC,GAAG,EAAEN,IAAI,EAAEO,MAAM,EAAER,QAAQ,KAAK;EAC3D,IAAIC,IAAI,EAAE;IACRQ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BV,QAAQ,CAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEM,GAAG,EAAEA,GAAG;MAAEC,MAAM,EAAEA;IAAO,CAAC,CAAC;EACtE;AACF,CAAC;AAED,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAOJ,GAAG,EAAEP,QAAQ,KAAK;EACrD,IAAIG,YAAY,GAAG,MAAML,WAAW,CAACS,GAAG,CAAC;EACzCE,OAAO,CAACC,GAAG,CAACP,YAAY,CAAC;EACzB,IAAIA,YAAY,IAAIA,YAAY,CAACS,OAAO,EAAE;IACxCb,SAAS,CAACC,QAAQ,CAAC;EACrB;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,WAAW,GAAG,MAAAA,CACzBZ,IAAI,EACJa,IAAI,EACJd,QAAQ,EACRe,QAAQ,EACRC,WAAW,KACR;EACH,IAAIb,YAAY,GAAG,MAAMN,WAAW,CAAC,CAAC;EACtC,IAAIM,YAAY,IAAIA,YAAY,CAACE,MAAM,EAAE;IACvC,IAAIY,OAAO;IACX,IAAIhB,IAAI,KAAK,KAAK,EAAE;MAClBD,QAAQ,CAAC;QACPC,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEC,YAAY,CAACE;MACxB,CAAC,CAAC;MACFW,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAId,IAAI,KAAK,eAAe,EAAE;MACnCgB,OAAO,GAAGd,YAAY,CAACE,MAAM,CAACa,MAAM,CACjCC,IAAI,IAAKA,IAAI,CAACX,MAAM,KAAK,eAC5B,CAAC;MACDR,QAAQ,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,OAAO,EAAEe;MAAQ,CAAC,CAAC;MAChED,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAId,IAAI,KAAK,YAAY,EAAE;MAChCgB,OAAO,GAAGd,YAAY,CAACE,MAAM,CAACa,MAAM,CACjCC,IAAI,IAAKA,IAAI,CAACX,MAAM,KAAK,YAC5B,CAAC;MACDR,QAAQ,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,OAAO,EAAEe;MAAQ,CAAC,CAAC;MAChED,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAId,IAAI,KAAK,SAAS,EAAE;MAC7BgB,OAAO,GAAGd,YAAY,CAACE,MAAM,CAACa,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACX,MAAM,KAAK,SAAS,CAAC;MACzER,QAAQ,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,OAAO,EAAEe;MAAQ,CAAC,CAAC;MAChED,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAId,IAAI,KAAK,WAAW,EAAE;MAC/BgB,OAAO,GAAGd,YAAY,CAACE,MAAM,CAACa,MAAM,CACjCC,IAAI,IAAKA,IAAI,CAACX,MAAM,KAAK,WAC5B,CAAC;MACDR,QAAQ,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,OAAO,EAAEe;MAAQ,CAAC,CAAC;MAChED,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB,CAAC,MAAM,IAAId,IAAI,KAAK,WAAW,EAAE;MAC/BgB,OAAO,GAAGd,YAAY,CAACE,MAAM,CAACa,MAAM,CACjCC,IAAI,IAAKA,IAAI,CAACX,MAAM,KAAK,WAC5B,CAAC;MACDR,QAAQ,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,OAAO,EAAEe;MAAQ,CAAC,CAAC;MAChED,WAAW,CAAC,CAACD,QAAQ,CAAC;IACxB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}