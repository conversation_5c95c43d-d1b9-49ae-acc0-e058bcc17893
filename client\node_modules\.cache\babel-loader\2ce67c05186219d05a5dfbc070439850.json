{"ast": null, "code": "export const categoryState = {\n  categories: [],\n  addCategoryModal: false,\n  editCategoryModal: {\n    modal: false,\n    cId: null,\n    des: \"\",\n    status: \"\"\n  },\n  loading: false\n};\nexport const categoryReducer = (state, action) => {\n  switch (action.type) {\n    /* Get all category */\n    case \"fetchCategoryAndChangeState\":\n      return {\n        ...state,\n        categories: action.payload\n      };\n    /* Create a category */\n    case \"addCategoryModal\":\n      return {\n        ...state,\n        addCategoryModal: action.payload\n      };\n    /* Edit a category */\n    case \"editCategoryModalOpen\":\n      return {\n        ...state,\n        editCategoryModal: {\n          modal: true,\n          cId: action.cId,\n          des: action.des,\n          status: action.status\n        }\n      };\n    case \"editCategoryModalClose\":\n      return {\n        ...state,\n        editCategoryModal: {\n          modal: false,\n          cId: null,\n          des: \"\",\n          status: \"\"\n        }\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["categoryState", "categories", "addCategoryModal", "editCategoryModal", "modal", "cId", "des", "status", "loading", "categoryReducer", "state", "action", "type", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/CategoryContext.js"], "sourcesContent": ["export const categoryState = {\r\n  categories: [],\r\n  addCategoryModal: false,\r\n  editCategoryModal: {\r\n    modal: false,\r\n    cId: null,\r\n    des: \"\",\r\n    status: \"\",\r\n  },\r\n  loading: false,\r\n};\r\n\r\nexport const categoryReducer = (state, action) => {\r\n  switch (action.type) {\r\n    /* Get all category */\r\n    case \"fetchCategoryAndChangeState\":\r\n      return {\r\n        ...state,\r\n        categories: action.payload,\r\n      };\r\n    /* Create a category */\r\n    case \"addCategoryModal\":\r\n      return {\r\n        ...state,\r\n        addCategoryModal: action.payload,\r\n      };\r\n    /* Edit a category */\r\n    case \"editCategoryModalOpen\":\r\n      return {\r\n        ...state,\r\n        editCategoryModal: {\r\n          modal: true,\r\n          cId: action.cId,\r\n          des: action.des,\r\n          status: action.status,\r\n        },\r\n      };\r\n    case \"editCategoryModalClose\":\r\n      return {\r\n        ...state,\r\n        editCategoryModal: {\r\n          modal: false,\r\n          cId: null,\r\n          des: \"\",\r\n          status: \"\",\r\n        },\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG;EAC3BC,UAAU,EAAE,EAAE;EACdC,gBAAgB,EAAE,KAAK;EACvBC,iBAAiB,EAAE;IACjBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,IAAI;IACTC,GAAG,EAAE,EAAE;IACPC,MAAM,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAChD,QAAQA,MAAM,CAACC,IAAI;IACjB;IACA,KAAK,6BAA6B;MAChC,OAAO;QACL,GAAGF,KAAK;QACRT,UAAU,EAAEU,MAAM,CAACE;MACrB,CAAC;IACH;IACA,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGH,KAAK;QACRR,gBAAgB,EAAES,MAAM,CAACE;MAC3B,CAAC;IACH;IACA,KAAK,uBAAuB;MAC1B,OAAO;QACL,GAAGH,KAAK;QACRP,iBAAiB,EAAE;UACjBC,KAAK,EAAE,IAAI;UACXC,GAAG,EAAEM,MAAM,CAACN,GAAG;UACfC,GAAG,EAAEK,MAAM,CAACL,GAAG;UACfC,MAAM,EAAEI,MAAM,CAACJ;QACjB;MACF,CAAC;IACH,KAAK,wBAAwB;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRP,iBAAiB,EAAE;UACjBC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,IAAI;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGG,KAAK;QACRF,OAAO,EAAEG,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}