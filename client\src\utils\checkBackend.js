import axios from "axios";

const API_URL = process.env.REACT_APP_API_URL || "http://localhost:8080";

export const checkBackendHealth = async () => {
  try {
    console.log("🔍 Checking backend health...");
    
    // Test basic connectivity
    const response = await axios.get(`${API_URL}/api/v1/products/customer`, {
      timeout: 5000
    });
    
    console.log("✅ Backend is running!");
    console.log(`📊 Found ${response.data.length} products`);
    
    return {
      isRunning: true,
      productCount: response.data.length,
      data: response.data
    };
  } catch (error) {
    console.error("❌ Backend check failed:", error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log("💡 Backend server is not running. Please start it with: ./mvnw spring-boot:run");
    } else if (error.response?.status === 404) {
      console.log("💡 API endpoint not found. Check if the backend has the correct endpoints.");
    } else {
      console.log("💡 Unknown error. Check backend logs.");
    }
    
    return {
      isRunning: false,
      error: error.message
    };
  }
};

export const logProductSample = (products) => {
  if (products && products.length > 0) {
    console.log("📦 Sample product structure:");
    console.log(JSON.stringify(products[0], null, 2));
  }
};
