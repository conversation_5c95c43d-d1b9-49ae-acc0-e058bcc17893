{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const isAuthenticate = () => localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")) : false;\nexport const isAdmin = () => localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).user.role === 1 : false;\nexport const loginReq = async ({\n  email,\n  password\n}) => {\n  const data = {\n    email,\n    password\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/auth/login`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const signupReq = async ({\n  name,\n  email,\n  password,\n  cPassword\n}) => {\n  const data = {\n    name,\n    email,\n    password,\n    cPassword\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/signup`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "isAuthenticate", "localStorage", "getItem", "JSON", "parse", "isAdmin", "user", "role", "loginReq", "email", "password", "data", "res", "post", "error", "console", "log", "signupReq", "name", "cPassword"], "sources": ["D:/ITSS_Reference/client/src/components/shop/auth/fetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const isAuthenticate = () =>\r\n  localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")) : false;\r\n\r\nexport const isAdmin = () =>\r\n  localStorage.getItem(\"jwt\")\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).user.role === 1\r\n    : false;\r\n\r\nexport const loginReq = async ({ email, password }) => {\r\n  const data = { email, password };\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/auth/login`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const signupReq = async ({ name, email, password, cPassword }) => {\r\n  const data = { name, email, password, cPassword };\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/signup`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAC5BC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK;AAE/E,OAAO,MAAMG,OAAO,GAAGA,CAAA,KACrBJ,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GACvBC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACI,IAAI,CAACC,IAAI,KAAK,CAAC,GACvD,KAAK;AAEX,OAAO,MAAMC,QAAQ,GAAG,MAAAA,CAAO;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EACrD,MAAMC,IAAI,GAAG;IAAEF,KAAK;IAAEC;EAAS,CAAC;EAChC,IAAI;IACF,IAAIE,GAAG,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAAC,GAAGjB,MAAM,oBAAoB,EAAEe,IAAI,CAAC;IAC/D,OAAOC,GAAG,CAACD,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,SAAS,GAAG,MAAAA,CAAO;EAAEC,IAAI;EAAET,KAAK;EAAEC,QAAQ;EAAES;AAAU,CAAC,KAAK;EACvE,MAAMR,IAAI,GAAG;IAAEO,IAAI;IAAET,KAAK;IAAEC,QAAQ;IAAES;EAAU,CAAC;EACjD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAAC,GAAGjB,MAAM,aAAa,EAAEe,IAAI,CAAC;IACxD,OAAOC,GAAG,CAACD,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}