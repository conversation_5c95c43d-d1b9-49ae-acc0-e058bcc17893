{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function createMixins(breakpoints, mixins) {\n  return _extends({\n    toolbar: {\n      minHeight: 56,\n      [breakpoints.up('xs')]: {\n        '@media (orientation: landscape)': {\n          minHeight: 48\n        }\n      },\n      [breakpoints.up('sm')]: {\n        minHeight: 64\n      }\n    }\n  }, mixins);\n}", "map": {"version": 3, "names": ["_extends", "createMixins", "breakpoints", "mixins", "toolbar", "minHeight", "up"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/styles/createMixins.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function createMixins(breakpoints, mixins) {\n  return _extends({\n    toolbar: {\n      minHeight: 56,\n      [breakpoints.up('xs')]: {\n        '@media (orientation: landscape)': {\n          minHeight: 48\n        }\n      },\n      [breakpoints.up('sm')]: {\n        minHeight: 64\n      }\n    }\n  }, mixins);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,eAAe,SAASC,YAAYA,CAACC,WAAW,EAAEC,MAAM,EAAE;EACxD,OAAOH,QAAQ,CAAC;IACdI,OAAO,EAAE;MACPC,SAAS,EAAE,EAAE;MACb,CAACH,WAAW,CAACI,EAAE,CAAC,IAAI,CAAC,GAAG;QACtB,iCAAiC,EAAE;UACjCD,SAAS,EAAE;QACb;MACF,CAAC;MACD,CAACH,WAAW,CAACI,EAAE,CAAC,IAAI,CAAC,GAAG;QACtBD,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAEF,MAAM,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}