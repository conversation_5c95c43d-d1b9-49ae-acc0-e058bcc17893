package com.darian.ecommerce.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Component
public class DataLoader implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        // Check if data already exists
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM product", Integer.class);
        
        if (count == null || count == 0) {
            System.out.println("Loading initial data...");
            loadDataFromFile();
            System.out.println("Initial data loaded successfully!");
        } else {
            System.out.println("Data already exists, skipping initialization.");
        }
    }

    private void loadDataFromFile() throws Exception {
        ClassPathResource resource = new ClassPathResource("db/data-init.sql");
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            
            StringBuilder sqlBuilder = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // Skip comments and empty lines
                if (line.isEmpty() || line.startsWith("--")) {
                    continue;
                }
                
                sqlBuilder.append(line).append(" ");
                
                // Execute when we hit a semicolon
                if (line.endsWith(";")) {
                    String sql = sqlBuilder.toString().trim();
                    if (!sql.isEmpty()) {
                        try {
                            jdbcTemplate.execute(sql);
                        } catch (Exception e) {
                            System.err.println("Error executing SQL: " + sql);
                            System.err.println("Error: " + e.getMessage());
                        }
                    }
                    sqlBuilder.setLength(0);
                }
            }
        }
    }
}
