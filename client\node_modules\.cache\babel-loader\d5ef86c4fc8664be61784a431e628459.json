{"ast": null, "code": "export const subTotal = (id, price) => {\n  let subTotalCost = 0;\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\n  carts.forEach(item => {\n    if (item.id === id) {\n      subTotalCost = item.quantitiy * price;\n    }\n  });\n  return subTotalCost;\n};\nexport const quantity = id => {\n  let product = 0;\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\n  carts.forEach(item => {\n    if (item.id === id) {\n      product = item.quantitiy;\n    }\n  });\n  return product;\n};\nexport const totalCost = () => {\n  let totalCost = 0;\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\n  carts.forEach(item => {\n    totalCost += item.quantitiy * item.price;\n  });\n  return totalCost;\n};", "map": {"version": 3, "names": ["subTotal", "id", "price", "subTotalCost", "carts", "JSON", "parse", "localStorage", "getItem", "for<PERSON>ach", "item", "quantitiy", "quantity", "product", "totalCost"], "sources": ["D:/ITSS_Reference/client/src/components/shop/partials/Mixins.js"], "sourcesContent": ["export const subTotal = (id, price) => {\r\n  let subTotalCost = 0;\r\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\r\n  carts.forEach((item) => {\r\n    if (item.id === id) {\r\n      subTotalCost = item.quantitiy * price;\r\n    }\r\n  });\r\n  return subTotalCost;\r\n};\r\n\r\nexport const quantity = (id) => {\r\n  let product = 0;\r\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\r\n  carts.forEach((item) => {\r\n    if (item.id === id) {\r\n      product = item.quantitiy;\r\n    }\r\n  });\r\n  return product;\r\n};\r\n\r\nexport const totalCost = () => {\r\n  let totalCost = 0;\r\n  let carts = JSON.parse(localStorage.getItem(\"cart\"));\r\n  carts.forEach((item) => {\r\n    totalCost += item.quantitiy * item.price;\r\n  });\r\n  return totalCost;\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EACrC,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACpDJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;IACtB,IAAIA,IAAI,CAACT,EAAE,KAAKA,EAAE,EAAE;MAClBE,YAAY,GAAGO,IAAI,CAACC,SAAS,GAAGT,KAAK;IACvC;EACF,CAAC,CAAC;EACF,OAAOC,YAAY;AACrB,CAAC;AAED,OAAO,MAAMS,QAAQ,GAAIX,EAAE,IAAK;EAC9B,IAAIY,OAAO,GAAG,CAAC;EACf,IAAIT,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACpDJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;IACtB,IAAIA,IAAI,CAACT,EAAE,KAAKA,EAAE,EAAE;MAClBY,OAAO,GAAGH,IAAI,CAACC,SAAS;IAC1B;EACF,CAAC,CAAC;EACF,OAAOE,OAAO;AAChB,CAAC;AAED,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAC7B,IAAIA,SAAS,GAAG,CAAC;EACjB,IAAIV,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACpDJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;IACtBI,SAAS,IAAIJ,IAAI,CAACC,SAAS,GAAGD,IAAI,CAACR,KAAK;EAC1C,CAAC,CAAC;EACF,OAAOY,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}