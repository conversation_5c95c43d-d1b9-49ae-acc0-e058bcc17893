{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllProduct = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const getAllProductForManager = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createPorductImage = async ({\n  pImage\n}) => {\n  /* Most important part for uploading multiple image  */\n  let formData = new FormData();\n  for (const file of pImage) {\n    formData.append(\"pImage\", file);\n  }\n  /* Most important part for uploading multiple image  */\n};\nexport const createProduct = async ({\n  name,\n  description,\n  images,\n  category,\n  stockQuantity,\n  price,\n  weight,\n  rushEligible,\n  barcode,\n  specifications,\n  userId\n}) => {\n  const productData = {\n    name,\n    description,\n    images: images || [],\n    category,\n    price: parseFloat(price),\n    weight: parseFloat(weight) || 0,\n    rushEligible: rushEligible || false,\n    barcode,\n    specifications\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, productData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const editProduct = async product => {\n  console.log(product);\n  /* Most important part for updating multiple image  */\n  let formData = new FormData();\n  if (product.pEditImages) {\n    for (const file of product.pEditImages) {\n      formData.append(\"pEditImages\", file);\n    }\n  }\n  /* Most important part for updating multiple image  */\n  formData.append(\"pId\", product.pId);\n  formData.append(\"pName\", product.pName);\n  formData.append(\"pDescription\", product.pDescription);\n  formData.append(\"pStatus\", product.pStatus);\n  formData.append(\"pCategory\", product.pCategory._id);\n  formData.append(\"pQuantity\", product.pQuantity);\n  formData.append(\"pPrice\", product.pPrice);\n  formData.append(\"pOffer\", product.pOffer);\n  formData.append(\"pImages\", product.pImages);\n  try {\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteProduct = async pId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/delete-product`, {\n      pId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const productByCategory = async catId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\n      catId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const productByPrice = async price => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\n      price\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllProduct", "res", "get", "data", "error", "console", "log", "getAllProductForManager", "createPorductImage", "pImage", "formData", "FormData", "file", "append", "createProduct", "name", "description", "images", "category", "stockQuantity", "price", "weight", "rushEligible", "barcode", "specifications", "userId", "productData", "parseFloat", "post", "editProduct", "product", "pEditImages", "pId", "pName", "pDescription", "pStatus", "pCategory", "_id", "pQuantity", "pPrice", "pOffer", "pImages", "deleteProduct", "productByCategory", "catId", "productByPrice"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllProduct = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getAllProductForManager = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createPorductImage = async ({ pImage }) => {\r\n  /* Most important part for uploading multiple image  */\r\n  let formData = new FormData();\r\n  for (const file of pImage) {\r\n    formData.append(\"pImage\", file);\r\n  }\r\n  /* Most important part for uploading multiple image  */\r\n};\r\n\r\nexport const createProduct = async ({\r\n  name,\r\n  description,\r\n  images,\r\n  category,\r\n  stockQuantity,\r\n  price,\r\n  weight,\r\n  rushEligible,\r\n  barcode,\r\n  specifications,\r\n  userId\r\n}) => {\r\n  const productData = {\r\n    name,\r\n    description,\r\n    images: images || [],\r\n    category,\r\n    price: parseFloat(price),\r\n    weight: parseFloat(weight) || 0,\r\n    rushEligible: rushEligible || false,\r\n    barcode,\r\n    specifications\r\n  };\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, productData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const editProduct = async (product) => {\r\n  console.log(product);\r\n  /* Most important part for updating multiple image  */\r\n  let formData = new FormData();\r\n  if (product.pEditImages) {\r\n    for (const file of product.pEditImages) {\r\n      formData.append(\"pEditImages\", file);\r\n    }\r\n  }\r\n  /* Most important part for updating multiple image  */\r\n  formData.append(\"pId\", product.pId);\r\n  formData.append(\"pName\", product.pName);\r\n  formData.append(\"pDescription\", product.pDescription);\r\n  formData.append(\"pStatus\", product.pStatus);\r\n  formData.append(\"pCategory\", product.pCategory._id);\r\n  formData.append(\"pQuantity\", product.pQuantity);\r\n  formData.append(\"pPrice\", product.pPrice);\r\n  formData.append(\"pOffer\", product.pOffer);\r\n  formData.append(\"pImages\", product.pImages);\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteProduct = async (pId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/delete-product`, { pId });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const productByCategory = async (catId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\r\n      catId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const productByPrice = async (price) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\r\n      price,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,2BAA2B,CAAC;IAC/D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,uBAAuB,GAAG,MAAAA,CAAA,KAAY;EACjD,IAAI;IACF,IAAIN,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,0BAA0B,CAAC;IAC9D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAG,MAAAA,CAAO;EAAEC;AAAO,CAAC,KAAK;EACtD;EACA,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;EACjC;EACA;AACF,CAAC;AAED,OAAO,MAAME,aAAa,GAAG,MAAAA,CAAO;EAClCC,IAAI;EACJC,WAAW;EACXC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACbC,KAAK;EACLC,MAAM;EACNC,YAAY;EACZC,OAAO;EACPC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBX,IAAI;IACJC,WAAW;IACXC,MAAM,EAAEA,MAAM,IAAI,EAAE;IACpBC,QAAQ;IACRE,KAAK,EAAEO,UAAU,CAACP,KAAK,CAAC;IACxBC,MAAM,EAAEM,UAAU,CAACN,MAAM,CAAC,IAAI,CAAC;IAC/BC,YAAY,EAAEA,YAAY,IAAI,KAAK;IACnCC,OAAO;IACPC;EACF,CAAC;EAED,IAAI;IACF,IAAIvB,GAAG,GAAG,MAAMN,KAAK,CAACiC,IAAI,CAAC,GAAGhC,MAAM,2BAA2B6B,MAAM,EAAE,EAAEC,WAAW,CAAC;IACrF,OAAOzB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMyB,WAAW,GAAG,MAAOC,OAAO,IAAK;EAC5CzB,OAAO,CAACC,GAAG,CAACwB,OAAO,CAAC;EACpB;EACA,IAAIpB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,IAAImB,OAAO,CAACC,WAAW,EAAE;IACvB,KAAK,MAAMnB,IAAI,IAAIkB,OAAO,CAACC,WAAW,EAAE;MACtCrB,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAED,IAAI,CAAC;IACtC;EACF;EACA;EACAF,QAAQ,CAACG,MAAM,CAAC,KAAK,EAAEiB,OAAO,CAACE,GAAG,CAAC;EACnCtB,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEiB,OAAO,CAACG,KAAK,CAAC;EACvCvB,QAAQ,CAACG,MAAM,CAAC,cAAc,EAAEiB,OAAO,CAACI,YAAY,CAAC;EACrDxB,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEiB,OAAO,CAACK,OAAO,CAAC;EAC3CzB,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEiB,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC;EACnD3B,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEiB,OAAO,CAACQ,SAAS,CAAC;EAC/C5B,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEiB,OAAO,CAACS,MAAM,CAAC;EACzC7B,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEiB,OAAO,CAACU,MAAM,CAAC;EACzC9B,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEiB,OAAO,CAACW,OAAO,CAAC;EAE3C,IAAI;IACF,IAAIxC,GAAG,GAAG,MAAMN,KAAK,CAACiC,IAAI,CAAC,GAAGhC,MAAM,2BAA2B,EAAEc,QAAQ,CAAC;IAC1E,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMsC,aAAa,GAAG,MAAOV,GAAG,IAAK;EAC1C,IAAI;IACF,IAAI/B,GAAG,GAAG,MAAMN,KAAK,CAACiC,IAAI,CAAC,GAAGhC,MAAM,6BAA6B,EAAE;MAAEoC;IAAI,CAAC,CAAC;IAC3E,OAAO/B,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMuC,iBAAiB,GAAG,MAAOC,KAAK,IAAK;EAChD,IAAI;IACF,IAAI3C,GAAG,GAAG,MAAMN,KAAK,CAACiC,IAAI,CAAC,GAAGhC,MAAM,kCAAkC,EAAE;MACtEgD;IACF,CAAC,CAAC;IACF,OAAO3C,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMyC,cAAc,GAAG,MAAOzB,KAAK,IAAK;EAC7C,IAAI;IACF,IAAInB,GAAG,GAAG,MAAMN,KAAK,CAACiC,IAAI,CAAC,GAAGhC,MAAM,+BAA+B,EAAE;MACnEwB;IACF,CAAC,CAAC;IACF,OAAOnB,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}