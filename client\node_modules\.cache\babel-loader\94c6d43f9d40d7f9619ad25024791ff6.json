{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get single product details - Updated to match backend endpoint\nexport const getSingleProduct = async (productId, userId = null) => {\n  try {\n    // Get userId from localStorage if not provided\n    if (!userId) {\n      var _JSON$parse$user, _JSON$parse$user2;\n      const jwt = localStorage.getItem(\"jwt\");\n      userId = jwt ? ((_JSON$parse$user = JSON.parse(jwt).user) === null || _JSON$parse$user === void 0 ? void 0 : _JSON$parse$user.id) || ((_JSON$parse$user2 = JSON.parse(jwt).user) === null || _JSON$parse$user2 === void 0 ? void 0 : _JSON$parse$user2._id) || 1 : 1;\n    }\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get related products - Updated to match backend endpoint\nexport const getRelatedProducts = async productId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Legacy functions - these may need to be updated based on actual backend implementation\nexport const postAddReview = async formData => {\n  console.warn(\"postAddReview: This endpoint may not be available in the current backend\");\n  try {\n    let res = await axios.post(`${apiURL}/api/product/add-review`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const postDeleteReview = async formData => {\n  console.warn(\"postDeleteReview: This endpoint may not be available in the current backend\");\n  try {\n    let res = await axios.post(`${apiURL}/api/product/delete-review`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getSingleProduct", "productId", "userId", "_JSON$parse$user", "_JSON$parse$user2", "jwt", "localStorage", "getItem", "JSON", "parse", "user", "id", "_id", "res", "get", "data", "error", "console", "log", "getRelatedProducts", "postAddReview", "formData", "warn", "post", "postDeleteReview"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// Get single product details - Updated to match backend endpoint\r\nexport const getSingleProduct = async (productId, userId = null) => {\r\n  try {\r\n    // Get userId from localStorage if not provided\r\n    if (!userId) {\r\n      const jwt = localStorage.getItem(\"jwt\");\r\n      userId = jwt\r\n        ? JSON.parse(jwt).user?.id || JSON.parse(jwt).user?._id || 1\r\n        : 1;\r\n    }\r\n\r\n    let res = await axios.get(\r\n      `${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get related products - Updated to match backend endpoint\r\nexport const getRelatedProducts = async (productId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Legacy functions - these may need to be updated based on actual backend implementation\r\nexport const postAddReview = async (formData) => {\r\n  console.warn(\r\n    \"postAddReview: This endpoint may not be available in the current backend\"\r\n  );\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/add-review`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const postDeleteReview = async (formData) => {\r\n  console.warn(\r\n    \"postDeleteReview: This endpoint may not be available in the current backend\"\r\n  );\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/delete-review`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,MAAM,GAAG,IAAI,KAAK;EAClE,IAAI;IACF;IACA,IAAI,CAACA,MAAM,EAAE;MAAA,IAAAC,gBAAA,EAAAC,iBAAA;MACX,MAAMC,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;MACvCL,MAAM,GAAGG,GAAG,GACR,EAAAF,gBAAA,GAAAK,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,CAACK,IAAI,cAAAP,gBAAA,uBAApBA,gBAAA,CAAsBQ,EAAE,OAAAP,iBAAA,GAAII,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,CAACK,IAAI,cAAAN,iBAAA,uBAApBA,iBAAA,CAAsBQ,GAAG,KAAI,CAAC,GAC1D,CAAC;IACP;IAEA,IAAIC,GAAG,GAAG,MAAMlB,KAAK,CAACmB,GAAG,CACvB,GAAGlB,MAAM,6BAA6BK,SAAS,WAAWC,MAAM,EAClE,CAAC;IACD,OAAOW,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,kBAAkB,GAAG,MAAOlB,SAAS,IAAK;EACrD,IAAI;IACF,IAAIY,GAAG,GAAG,MAAMlB,KAAK,CAACmB,GAAG,CAAC,GAAGlB,MAAM,oBAAoBK,SAAS,UAAU,CAAC;IAC3E,OAAOY,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,aAAa,GAAG,MAAOC,QAAQ,IAAK;EAC/CJ,OAAO,CAACK,IAAI,CACV,0EACF,CAAC;EACD,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMlB,KAAK,CAAC4B,IAAI,CAAC,GAAG3B,MAAM,yBAAyB,EAAEyB,QAAQ,CAAC;IACxE,OAAOR,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMQ,gBAAgB,GAAG,MAAOH,QAAQ,IAAK;EAClDJ,OAAO,CAACK,IAAI,CACV,6EACF,CAAC;EACD,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMlB,KAAK,CAAC4B,IAAI,CAAC,GAAG3B,MAAM,4BAA4B,EAAEyB,QAAQ,CAAC;IAC3E,OAAOR,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}