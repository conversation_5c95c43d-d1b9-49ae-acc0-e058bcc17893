{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\layout\\\\PageNotFound.js\";\nimport React from \"react\";\nimport Layout from \"./index\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageNotFoundComponent = props => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center my-32\",\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-32 h-32 text-gray-700\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-center text-gray-700 text-4xl font-bold tracking-widest\",\n      children: \"404 not found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = PageNotFoundComponent;\nconst PageNotFound = props => {\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(PageNotFoundComponent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 28\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 10\n  }, this);\n};\n_c2 = PageNotFound;\nexport default PageNotFound;\nvar _c, _c2;\n$RefreshReg$(_c, \"PageNotFoundComponent\");\n$RefreshReg$(_c2, \"PageNotFound\");", "map": {"version": 3, "names": ["React", "Layout", "jsxDEV", "_jsxDEV", "PageNotFoundComponent", "props", "className", "children", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PageNotFound", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/layout/PageNotFound.js"], "sourcesContent": ["import React from \"react\";\r\nimport Layout from \"./index\";\r\n\r\nconst PageNotFoundComponent = (props) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center my-32\">\r\n      <span>\r\n        <svg\r\n          className=\"w-32 h-32 text-gray-700\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth={2}\r\n            d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n          />\r\n        </svg>\r\n      </span>\r\n      <span className=\"text-center text-gray-700 text-4xl font-bold tracking-widest\">\r\n        404 not found\r\n      </span>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst PageNotFound = (props) => {\r\n  return <Layout children={<PageNotFoundComponent />} />;\r\n};\r\n\r\nexport default PageNotFound;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;EACvC,oBACEF,OAAA;IAAKG,SAAS,EAAC,iDAAiD;IAAAC,QAAA,gBAC9DJ,OAAA;MAAAI,QAAA,eACEJ,OAAA;QACEG,SAAS,EAAC,yBAAyB;QACnCE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAJ,QAAA,eAElCJ,OAAA;UACES,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAE,CAAE;UACfC,CAAC,EAAC;QAAoF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACPhB,OAAA;MAAMG,SAAS,EAAC,8DAA8D;MAAAC,QAAA,EAAC;IAE/E;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACC,EAAA,GAxBIhB,qBAAqB;AA0B3B,MAAMiB,YAAY,GAAIhB,KAAK,IAAK;EAC9B,oBAAOF,OAAA,CAACF,MAAM;IAACM,QAAQ,eAAEJ,OAAA,CAACC,qBAAqB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACxD,CAAC;AAACG,GAAA,GAFID,YAAY;AAIlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}