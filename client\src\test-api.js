// Test file để kiểm tra API
import axios from "axios";

const apiURL = process.env.REACT_APP_API_URL || "http://localhost:8080";

// Test lấy tất cả sản phẩm
export const testGetAllProducts = async () => {
  try {
    console.log("Testing GET /api/v1/products/customer");
    const response = await axios.get(`${apiURL}/api/v1/products/customer`);
    console.log("Products response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching products:", error);
    return null;
  }
};

// Test lấy chi tiết sản phẩm
export const testGetProductDetails = async (productId, userId = 1) => {
  try {
    console.log(`Testing GET /api/v1/products/customer/${productId}`);
    const response = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);
    console.log("Product details response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching product details:", error);
    return null;
  }
};

// Test search sản phẩm
export const testSearchProducts = async (keyword, userId = 1) => {
  try {
    console.log(`Testing GET /api/v1/products/customer/search?keyword=${keyword}&userId=${userId}`);
    const response = await axios.get(`${apiURL}/api/v1/products/customer/search?keyword=${keyword}&userId=${userId}`);
    console.log("Search results:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error searching products:", error);
    return null;
  }
};

// Chạy tất cả tests
export const runAllTests = async () => {
  console.log("=== STARTING API TESTS ===");
  
  // Test 1: Lấy tất cả sản phẩm
  const products = await testGetAllProducts();
  
  if (products && products.length > 0) {
    console.log(`✅ Found ${products.length} products`);
    
    // Test 2: Lấy chi tiết sản phẩm đầu tiên
    const firstProduct = products[0];
    console.log("First product:", firstProduct);
    
    if (firstProduct.productId) {
      await testGetProductDetails(firstProduct.productId);
    }
    
    // Test 3: Search sản phẩm
    await testSearchProducts("phone");
  } else {
    console.log("❌ No products found");
  }
  
  console.log("=== API TESTS COMPLETED ===");
};
