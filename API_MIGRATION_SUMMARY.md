# 🔄 API Migration Summary - MongoDB to PostgreSQL

## 📋 Tổng quan
Frontend đã đượ<PERSON> cập nhật để tương thích với backend mới sử dụng PostgreSQL thay vì MongoDB.

## 🔧 Các thay đổi chính

### 1. **Product APIs**
#### Cũ (MongoDB):
- `GET /api/product/all-product` → Response: `{Products: [...]}`
- `POST /api/product/add-product` (FormData)
- `POST /api/product/delete-product`

#### Mới (PostgreSQL):
- `GET /api/v1/products/customer` → Response: `[...]` (array trực tiếp)
- `GET /api/v1/products/manager` 
- `GET /api/v1/products/customer/{id}?userId={userId}`
- `GET /api/v1/products/customer/search?keyword={keyword}&userId={userId}`
- `POST /api/v1/products?userId={userId}` (JSON)
- `DELETE /api/v1/products/{id}?userId={userId}`

### 2. **Authentication APIs**
#### Cũ:
- `POST /api/signin`
- `POST /api/signup`

#### Mới:
- `POST /api/v1/auth/login`
- `POST /api/v1/auth/register`

### 3. **Cart APIs** (Mới)
- `GET /api/v1/cart/{userId}`
- `POST /api/v1/cart/{userId}/add?productId={id}&quantity={qty}`
- `PUT /api/v1/cart/{userId}/update?productId={id}&quantity={qty}`
- `DELETE /api/v1/cart/{userId}/remove?productId={id}`
- `DELETE /api/v1/cart/{userId}/empty`

### 4. **Order APIs** (Mới)
- `POST /api/v1/order/order/create`
- `POST /api/v1/order/order/place`
- `GET /api/v1/order/{orderId}`
- `POST /api/v1/order/{orderId}/cancel`
- `POST /api/v1/order/{orderId}/pay`

## 📊 Cấu trúc dữ liệu thay đổi

### Product Object:
#### Cũ (MongoDB):
```javascript
{
  _id: "...",
  pName: "Product Name",
  pPrice: 100,
  pImages: ["image1.jpg"],
  pCategory: {_id: "...", cName: "Category"},
  pRatingsReviews: [...]
}
```

#### Mới (PostgreSQL):
```javascript
{
  productId: 1,
  name: "Product Name",
  price: 100.0,
  images: ["http://example.com/image1.jpg"],
  category: "Electronics",
  availabilityStatus: "AVAILABLE",
  weight: 0.5,
  rushEligible: true,
  specifications: "...",
  description: "..."
}
```

### User Object:
#### Cũ:
```javascript
{
  _id: "...",
  name: "User Name",
  email: "<EMAIL>",
  role: 1
}
```

#### Mới:
```javascript
{
  userId: 1,
  fullName: "User Name",
  email: "<EMAIL>",
  role: "CUSTOMER"
}
```

## 📁 Files đã cập nhật

### 1. **API Files:**
- `client/src/components/admin/products/FetchApi.js` ✅
- `client/src/components/shop/auth/fetchApi.js` ✅
- `client/src/components/shop/cart/FetchApi.js` ✅ (Mới)
- `client/src/components/shop/order/FetchApi.js` ✅

### 2. **Component Files:**
- `client/src/components/shop/home/<USER>
- `client/src/components/shop/home/<USER>
- `client/src/components/shop/home/<USER>

### 3. **New Test Components:**
- `client/src/components/ApiTestPage.js` ✅ (Mới)

## 🧪 Testing

### Trang test API:
- URL: `http://localhost:3000/api-test`
- Features:
  - ✅ Load all products
  - ✅ Search products
  - ✅ View product details
  - ✅ Add to cart
  - ✅ View cart status

### Dữ liệu mẫu có sẵn:
- **18 sản phẩm** thuộc 9 danh mục
- **2 user accounts:**
  - Customer: `<EMAIL>` / `password123`
  - Manager: `<EMAIL>` / `password123`

## 🚀 Cách chạy

### 1. Backend:
```bash
cd backend
./mvnw spring-boot:run
```
Backend chạy tại: `http://localhost:8080`

### 2. Frontend:
```bash
cd client
npm start
```
Frontend chạy tại: `http://localhost:3000`

### 3. Test API:
- Vào `http://localhost:3000/api-test`
- Hoặc vào trang chủ `http://localhost:3000` để xem sản phẩm

## ⚠️ Lưu ý

### 1. **Categories:**
- Backend chưa có CategoryController
- Frontend sử dụng mock data cho categories
- Icons emoji thay thế cho hình ảnh categories

### 2. **Images:**
- Backend trả về URL đầy đủ cho images
- Frontend có fallback cho placeholder image

### 3. **Authentication:**
- Hiện tại sử dụng mock userId = 1 cho testing
- Cần implement proper authentication flow

### 4. **Error Handling:**
- Tất cả API calls đều có try-catch
- Errors được log ra console và hiển thị cho user

## 🎯 Kết quả

✅ **Hoạt động:**
- Hiển thị danh sách sản phẩm từ PostgreSQL
- Search sản phẩm
- Xem chi tiết sản phẩm
- Thêm vào giỏ hàng
- Xem giỏ hàng

✅ **Tương thích:**
- API endpoints mới
- Cấu trúc dữ liệu mới
- Response format mới

🔄 **Cần hoàn thiện:**
- Authentication flow
- Category management
- Order management
- Payment integration
- Image upload

## 📞 Debug

Nếu có vấn đề:
1. Kiểm tra backend đã chạy tại port 8080
2. Kiểm tra database PostgreSQL đã có dữ liệu
3. Vào `/api-test` để debug API calls
4. Xem Console (F12) để check errors
5. Kiểm tra Network tab để xem API responses
