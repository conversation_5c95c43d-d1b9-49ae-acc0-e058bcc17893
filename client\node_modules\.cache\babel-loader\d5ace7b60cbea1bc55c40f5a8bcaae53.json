{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useReducer, useEffect } from \"react\";\nimport Routes from \"./components\";\nimport { LayoutContext, layoutState, layoutReducer } from \"./components/shop\";\nimport { suppressImageErrors, addGlobalImageErrorHandler } from \"./utils/errorSuppressor\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [data, dispatch] = useReducer(layoutReducer, layoutState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(LayoutContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NUfj7uIKERYurbPw3nzP3Nn276o=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Fragment", "useReducer", "useEffect", "Routes", "LayoutContext", "layoutState", "layoutReducer", "suppressImageErrors", "addGlobalImageErrorHandler", "jsxDEV", "_jsxDEV", "App", "_s", "data", "dispatch", "children", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/App.js"], "sourcesContent": ["import React, { Fragment, useReducer, useEffect } from \"react\";\r\nimport Routes from \"./components\";\r\nimport { LayoutContext, layoutState, layoutReducer } from \"./components/shop\";\r\nimport { suppressImageErrors, addGlobalImageErrorHandler } from \"./utils/errorSuppressor\";\r\n\r\nfunction App() {\r\n  const [data, dispatch] = useReducer(layoutReducer, layoutState);\r\n  return (\r\n    <Fragment>\r\n      <LayoutContext.Provider value={{ data, dispatch }}>\r\n        <Routes />\r\n      </LayoutContext.Provider>\r\n    </Fragment>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,aAAa,EAAEC,WAAW,EAAEC,aAAa,QAAQ,mBAAmB;AAC7E,SAASC,mBAAmB,EAAEC,0BAA0B,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGb,UAAU,CAACK,aAAa,EAAED,WAAW,CAAC;EAC/D,oBACEK,OAAA,CAACV,QAAQ;IAAAe,QAAA,eACPL,OAAA,CAACN,aAAa,CAACY,QAAQ;MAACC,KAAK,EAAE;QAAEJ,IAAI;QAAEC;MAAS,CAAE;MAAAC,QAAA,eAChDL,OAAA,CAACP,MAAM;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEf;AAACT,EAAA,CATQD,GAAG;AAAAW,EAAA,GAAHX,GAAG;AAWZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}