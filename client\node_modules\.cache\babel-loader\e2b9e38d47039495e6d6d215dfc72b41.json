{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport Layout from \"../layout\";\nimport Slider from \"./Slider\";\nimport ProductCategory from \"./ProductCategory\";\nimport { homeState, homeReducer } from \"./HomeContext\";\nimport SingleProduct from \"./SingleProduct\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const HomeContext = /*#__PURE__*/createContext();\nconst HomeComponent = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Slider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"m-4 md:mx-8 bg-blue-50 p-4 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-blue-800\",\n        children: [\"\\uD83D\\uDD27 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Debug Mode:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 14\n        }, this), \" Trang n\\xE0y hi\\u1EC3n th\\u1ECB s\\u1EA3n ph\\u1EA9m t\\u1EEB backend API.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), \"\\uD83D\\uDCCD \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"API URL:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 14\n        }, this), \" \", process.env.REACT_APP_API_URL, \"/api/v1/products/customer\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), \"\\uD83E\\uDDEA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Test Links:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 14\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/api-test\",\n          className: \"underline mx-2\",\n          children: \"API Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), \" |\", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/test-api.html\",\n          className: \"underline mx-2\",\n          children: \"Direct API Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), \"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Tip:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 14\n        }, this), \" M\\u1EDF Console (F12) \\u0111\\u1EC3 xem debug logs\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-8 md:my-6\",\n      children: /*#__PURE__*/_jsxDEV(ProductCategory, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-8 md:my-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\",\n      children: /*#__PURE__*/_jsxDEV(SingleProduct, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = HomeComponent;\nconst Home = props => {\n  _s();\n  const [data, dispatch] = useReducer(homeReducer, homeState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(HomeContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(HomeComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 27\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"ncxBYy9l1aoYHQSdugG6KTCXYX0=\");\n_c2 = Home;\nexport default Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomeComponent\");\n$RefreshReg$(_c2, \"Home\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "Layout", "Slide<PERSON>", "ProductCategory", "homeState", "homeReducer", "SingleProduct", "jsxDEV", "_jsxDEV", "HomeContext", "HomeComponent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "process", "env", "REACT_APP_API_URL", "href", "_c", "Home", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport Layout from \"../layout\";\r\nimport Slider from \"./Slider\";\r\nimport ProductCategory from \"./ProductCategory\";\r\nimport { homeState, homeReducer } from \"./HomeContext\";\r\nimport SingleProduct from \"./SingleProduct\";\r\n\r\nexport const HomeContext = createContext();\r\n\r\nconst HomeComponent = () => {\r\n  return (\r\n    <Fragment>\r\n      <Slider />\r\n      {/* Debug info */}\r\n      <div className=\"m-4 md:mx-8 bg-blue-50 p-4 rounded-lg\">\r\n        <div className=\"text-sm text-blue-800\">\r\n          🔧 <strong>Debug Mode:</strong> Trang này hiển thị sản phẩm từ backend API.\r\n          <br />\r\n          📍 <strong>API URL:</strong> {process.env.REACT_APP_API_URL}/api/v1/products/customer\r\n          <br />\r\n          🧪 <strong>Test Links:</strong>\r\n          <a href=\"/api-test\" className=\"underline mx-2\">API Test</a> |\r\n          <a href=\"/test-api.html\" className=\"underline mx-2\">Direct API Test</a>\r\n          <br />\r\n          💡 <strong>Tip:</strong> Mở Console (F12) để xem debug logs\r\n        </div>\r\n      </div>\r\n      {/* Category, Search & Filter Section */}\r\n      <section className=\"m-4 md:mx-8 md:my-6\">\r\n        <ProductCategory />\r\n      </section>\r\n      {/* Product Section */}\r\n      <section className=\"m-4 md:mx-8 md:my-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\r\n        <SingleProduct />\r\n      </section>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst Home = (props) => {\r\n  const [data, dispatch] = useReducer(homeReducer, homeState);\r\n  return (\r\n    <Fragment>\r\n      <HomeContext.Provider value={{ data, dispatch }}>\r\n        <Layout children={<HomeComponent />} />\r\n      </HomeContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,SAAS,EAAEC,WAAW,QAAQ,eAAe;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,OAAO,MAAMC,WAAW,gBAAGV,aAAa,CAAC,CAAC;AAE1C,MAAMW,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEF,OAAA,CAACV,QAAQ;IAAAa,QAAA,gBACPH,OAAA,CAACN,MAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVP,OAAA;MAAKQ,SAAS,EAAC,uCAAuC;MAAAL,QAAA,eACpDH,OAAA;QAAKQ,SAAS,EAAC,uBAAuB;QAAAL,QAAA,GAAC,eAClC,eAAAH,OAAA;UAAAG,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,4EAC/B,eAAAP,OAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,iBACH,eAAAP,OAAA;UAAAG,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACE,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAC,2BAC5D,eAAAX,OAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,iBACH,eAAAP,OAAA;UAAAG,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/BP,OAAA;UAAGY,IAAI,EAAC,WAAW;UAACJ,SAAS,EAAC,gBAAgB;UAAAL,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,MAC3D,eAAAP,OAAA;UAAGY,IAAI,EAAC,gBAAgB;UAACJ,SAAS,EAAC,gBAAgB;UAAAL,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvEP,OAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,iBACH,eAAAP,OAAA;UAAAG,QAAA,EAAQ;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,sDAC1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAASQ,SAAS,EAAC,qBAAqB;MAAAL,QAAA,eACtCH,OAAA,CAACL,eAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEVP,OAAA;MAASQ,SAAS,EAAC,oEAAoE;MAAAL,QAAA,eACrFH,OAAA,CAACF,aAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACM,EAAA,GA5BIX,aAAa;AA8BnB,MAAMY,IAAI,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAG1B,UAAU,CAACK,WAAW,EAAED,SAAS,CAAC;EAC3D,oBACEI,OAAA,CAACV,QAAQ;IAAAa,QAAA,eACPH,OAAA,CAACC,WAAW,CAACkB,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAf,QAAA,eAC9CH,OAAA,CAACP,MAAM;QAACU,QAAQ,eAAEH,OAAA,CAACE,aAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEf,CAAC;AAACS,EAAA,CATIF,IAAI;AAAAO,GAAA,GAAJP,IAAI;AAWV,eAAeA,IAAI;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}