{"ast": null, "code": "var isBrowser = typeof document !== 'undefined';\nfunction getRegisteredStyles(registered, registeredStyles, classNames) {\n  var rawClassName = '';\n  classNames.split(' ').forEach(function (className) {\n    if (registered[className] !== undefined) {\n      registeredStyles.push(registered[className] + \";\");\n    } else if (className) {\n      rawClassName += className + \" \";\n    }\n  });\n  return rawClassName;\n}\nvar registerStyles = function registerStyles(cache, serialized, isStringTag) {\n  var className = cache.key + \"-\" + serialized.name;\n  if (\n  // we only need to add the styles to the registered cache if the\n  // class name could be used further down\n  // the tree but if it's a string tag, we know it won't\n  // so we don't have to add it to registered cache.\n  // this improves memory usage since we can avoid storing the whole style string\n  (isStringTag === false ||\n  // we need to always store it if we're in compat mode and\n  // in node since emotion-server relies on whether a style is in\n  // the registered cache to know whether a style is global or not\n  // also, note that this check will be dead code eliminated in the browser\n  isBrowser === false && cache.compat !== undefined) && cache.registered[className] === undefined) {\n    cache.registered[className] = serialized.styles;\n  }\n};\nvar insertStyles = function insertStyles(cache, serialized, isStringTag) {\n  registerStyles(cache, serialized, isStringTag);\n  var className = cache.key + \"-\" + serialized.name;\n  if (cache.inserted[serialized.name] === undefined) {\n    var stylesForSSR = '';\n    var current = serialized;\n    do {\n      var maybeStyles = cache.insert(serialized === current ? \".\" + className : '', current, cache.sheet, true);\n      if (!isBrowser && maybeStyles !== undefined) {\n        stylesForSSR += maybeStyles;\n      }\n      current = current.next;\n    } while (current !== undefined);\n    if (!isBrowser && stylesForSSR.length !== 0) {\n      return stylesForSSR;\n    }\n  }\n};\nexport { getRegisteredStyles, insertStyles, registerStyles };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "document", "getRegisteredStyles", "registered", "registeredStyles", "classNames", "rawClassName", "split", "for<PERSON>ach", "className", "undefined", "push", "registerStyles", "cache", "serialized", "isStringTag", "key", "name", "compat", "styles", "insertStyles", "inserted", "stylesForSSR", "current", "maybeStyles", "insert", "sheet", "next", "length"], "sources": ["D:/ITSS_Reference/client/node_modules/@emotion/utils/dist/emotion-utils.esm.js"], "sourcesContent": ["var isBrowser = typeof document !== 'undefined';\n\nfunction getRegisteredStyles(registered, registeredStyles, classNames) {\n  var rawClassName = '';\n  classNames.split(' ').forEach(function (className) {\n    if (registered[className] !== undefined) {\n      registeredStyles.push(registered[className] + \";\");\n    } else if (className) {\n      rawClassName += className + \" \";\n    }\n  });\n  return rawClassName;\n}\nvar registerStyles = function registerStyles(cache, serialized, isStringTag) {\n  var className = cache.key + \"-\" + serialized.name;\n\n  if ( // we only need to add the styles to the registered cache if the\n  // class name could be used further down\n  // the tree but if it's a string tag, we know it won't\n  // so we don't have to add it to registered cache.\n  // this improves memory usage since we can avoid storing the whole style string\n  (isStringTag === false || // we need to always store it if we're in compat mode and\n  // in node since emotion-server relies on whether a style is in\n  // the registered cache to know whether a style is global or not\n  // also, note that this check will be dead code eliminated in the browser\n  isBrowser === false && cache.compat !== undefined) && cache.registered[className] === undefined) {\n    cache.registered[className] = serialized.styles;\n  }\n};\nvar insertStyles = function insertStyles(cache, serialized, isStringTag) {\n  registerStyles(cache, serialized, isStringTag);\n  var className = cache.key + \"-\" + serialized.name;\n\n  if (cache.inserted[serialized.name] === undefined) {\n    var stylesForSSR = '';\n    var current = serialized;\n\n    do {\n      var maybeStyles = cache.insert(serialized === current ? \".\" + className : '', current, cache.sheet, true);\n\n      if (!isBrowser && maybeStyles !== undefined) {\n        stylesForSSR += maybeStyles;\n      }\n\n      current = current.next;\n    } while (current !== undefined);\n\n    if (!isBrowser && stylesForSSR.length !== 0) {\n      return stylesForSSR;\n    }\n  }\n};\n\nexport { getRegisteredStyles, insertStyles, registerStyles };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAG,OAAOC,QAAQ,KAAK,WAAW;AAE/C,SAASC,mBAAmBA,CAACC,UAAU,EAAEC,gBAAgB,EAAEC,UAAU,EAAE;EACrE,IAAIC,YAAY,GAAG,EAAE;EACrBD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,SAAS,EAAE;IACjD,IAAIN,UAAU,CAACM,SAAS,CAAC,KAAKC,SAAS,EAAE;MACvCN,gBAAgB,CAACO,IAAI,CAACR,UAAU,CAACM,SAAS,CAAC,GAAG,GAAG,CAAC;IACpD,CAAC,MAAM,IAAIA,SAAS,EAAE;MACpBH,YAAY,IAAIG,SAAS,GAAG,GAAG;IACjC;EACF,CAAC,CAAC;EACF,OAAOH,YAAY;AACrB;AACA,IAAIM,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAE;EAC3E,IAAIN,SAAS,GAAGI,KAAK,CAACG,GAAG,GAAG,GAAG,GAAGF,UAAU,CAACG,IAAI;EAEjD;EAAK;EACL;EACA;EACA;EACA;EACA,CAACF,WAAW,KAAK,KAAK;EAAI;EAC1B;EACA;EACA;EACAf,SAAS,KAAK,KAAK,IAAIa,KAAK,CAACK,MAAM,KAAKR,SAAS,KAAKG,KAAK,CAACV,UAAU,CAACM,SAAS,CAAC,KAAKC,SAAS,EAAE;IAC/FG,KAAK,CAACV,UAAU,CAACM,SAAS,CAAC,GAAGK,UAAU,CAACK,MAAM;EACjD;AACF,CAAC;AACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACP,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACvEH,cAAc,CAACC,KAAK,EAAEC,UAAU,EAAEC,WAAW,CAAC;EAC9C,IAAIN,SAAS,GAAGI,KAAK,CAACG,GAAG,GAAG,GAAG,GAAGF,UAAU,CAACG,IAAI;EAEjD,IAAIJ,KAAK,CAACQ,QAAQ,CAACP,UAAU,CAACG,IAAI,CAAC,KAAKP,SAAS,EAAE;IACjD,IAAIY,YAAY,GAAG,EAAE;IACrB,IAAIC,OAAO,GAAGT,UAAU;IAExB,GAAG;MACD,IAAIU,WAAW,GAAGX,KAAK,CAACY,MAAM,CAACX,UAAU,KAAKS,OAAO,GAAG,GAAG,GAAGd,SAAS,GAAG,EAAE,EAAEc,OAAO,EAAEV,KAAK,CAACa,KAAK,EAAE,IAAI,CAAC;MAEzG,IAAI,CAAC1B,SAAS,IAAIwB,WAAW,KAAKd,SAAS,EAAE;QAC3CY,YAAY,IAAIE,WAAW;MAC7B;MAEAD,OAAO,GAAGA,OAAO,CAACI,IAAI;IACxB,CAAC,QAAQJ,OAAO,KAAKb,SAAS;IAE9B,IAAI,CAACV,SAAS,IAAIsB,YAAY,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAON,YAAY;IACrB;EACF;AACF,CAAC;AAED,SAASpB,mBAAmB,EAAEkB,YAAY,EAAER,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}