{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\productDetails\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport Layout from \"../layout\";\nimport { productDetailsState, productDetailsReducer } from \"./ProductDetailsContext\";\nimport Details from \"./Details\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ProductDetailsContext = /*#__PURE__*/createContext();\nconst DetailsComponent = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Details, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = DetailsComponent;\nconst ProductDetails = props => {\n  _s();\n  const [data, dispatch] = useReducer(productDetailsReducer, productDetailsState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ProductDetailsContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(DetailsComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 27\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetails, \"5QHEPlMDiFs30ib+BVKINEUI3DU=\");\n_c2 = ProductDetails;\nexport default ProductDetails;\nvar _c, _c2;\n$RefreshReg$(_c, \"DetailsComponent\");\n$RefreshReg$(_c2, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "Layout", "productDetailsState", "productDetailsReducer", "Details", "jsxDEV", "_jsxDEV", "ProductDetailsContext", "DetailsComponent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ProductDetails", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/index.js"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport Layout from \"../layout\";\r\nimport {\r\n  productDetailsState,\r\n  productDetailsReducer,\r\n} from \"./ProductDetailsContext\";\r\nimport Details from \"./Details\";\r\n\r\nexport const ProductDetailsContext = createContext();\r\n\r\nconst DetailsComponent = () => {\r\n  return (\r\n    <Fragment>\r\n      <Details />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst ProductDetails = (props) => {\r\n  const [data, dispatch] = useReducer(\r\n    productDetailsReducer,\r\n    productDetailsState\r\n  );\r\n  return (\r\n    <Fragment>\r\n      <ProductDetailsContext.Provider value={{ data, dispatch }}>\r\n        <Layout children={<DetailsComponent />} />\r\n      </ProductDetailsContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductDetails;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,MAAM,MAAM,WAAW;AAC9B,SACEC,mBAAmB,EACnBC,qBAAqB,QAChB,yBAAyB;AAChC,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,OAAO,MAAMC,qBAAqB,gBAAGR,aAAa,CAAC,CAAC;AAEpD,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACEF,OAAA,CAACR,QAAQ;IAAAW,QAAA,eACPH,OAAA,CAACF,OAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEf,CAAC;AAACC,EAAA,GANIN,gBAAgB;AAQtB,MAAMO,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGnB,UAAU,CACjCG,qBAAqB,EACrBD,mBACF,CAAC;EACD,oBACEI,OAAA,CAACR,QAAQ;IAAAW,QAAA,eACPH,OAAA,CAACC,qBAAqB,CAACa,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAV,QAAA,eACxDH,OAAA,CAACL,MAAM;QAACQ,QAAQ,eAAEH,OAAA,CAACE,gBAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC;AAEf,CAAC;AAACI,EAAA,CAZIF,cAAc;AAAAO,GAAA,GAAdP,cAAc;AAcpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}