{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\nvar $TypeError = require('es-errors/type');\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */function getSideChannelMap() {\n  /** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n  /** @typedef {Parameters<Channel['get']>[0]} K */\n  /** @typedef {Parameters<Channel['set']>[1]} V */\n\n  /** @type {Map<K, V> | undefined} */var $m;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      if ($m) {\n        var result = $mapDelete($m, key);\n        if ($mapSize($m) === 0) {\n          $m = void undefined;\n        }\n        return result;\n      }\n      return false;\n    },\n    get: function (key) {\n      // eslint-disable-line consistent-return\n      if ($m) {\n        return $mapGet($m, key);\n      }\n    },\n    has: function (key) {\n      if ($m) {\n        return $mapHas($m, key);\n      }\n      return false;\n    },\n    set: function (key, value) {\n      if (!$m) {\n        // @ts-expect-error TS can't handle narrowing a variable inside a closure\n        $m = new $Map();\n      }\n      $mapSet($m, key, value);\n    }\n  };\n\n  // @ts-expect-error TODO: figure out why TS is erroring here\n  return channel;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBound", "inspect", "$TypeError", "$Map", "$mapGet", "$mapSet", "$mapHas", "$mapDelete", "$mapSize", "module", "exports", "getSideChannelMap", "$m", "channel", "assert", "key", "has", "delete", "result", "undefined", "get", "set", "value"], "sources": ["D:/ITSS_Reference/client/node_modules/side-channel-map/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n\t/** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {Map<K, V> | undefined} */ var $m;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tif ($m) {\n\t\t\t\tvar result = $mapDelete($m, key);\n\t\t\t\tif ($mapSize($m) === 0) {\n\t\t\t\t\t$m = void undefined;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($m) {\n\t\t\t\treturn $mapGet($m, key);\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($m) {\n\t\t\t\treturn $mapHas($m, key);\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$m) {\n\t\t\t\t// @ts-expect-error TS can't handle narrowing a variable inside a closure\n\t\t\t\t$m = new $Map();\n\t\t\t}\n\t\t\t$mapSet($m, key, value);\n\t\t}\n\t};\n\n\t// @ts-expect-error TODO: figure out why TS is erroring here\n\treturn channel;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIE,OAAO,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAEvC,IAAIG,UAAU,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAII,IAAI,GAAGL,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;;AAEtC;AACA,IAAIM,OAAO,GAAGJ,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAClD;AACA,IAAIK,OAAO,GAAGL,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAClD;AACA,IAAIM,OAAO,GAAGN,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAClD;AACA,IAAIO,UAAU,GAAGP,SAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC;AACxD;AACA,IAAIQ,QAAQ,GAAGR,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC;;AAEpD;AACAS,MAAM,CAACC,OAAO,GAAG,CAAC,CAACP,IAAI,IAAI,0CAA2C,SAASQ,iBAAiBA,CAAA,EAAG;EAClG;EACA;EACA;;EAEA,oCAAqC,IAAIC,EAAE;;EAE3C;EACA,IAAIC,OAAO,GAAG;IACbC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAE;MACtB,IAAI,CAACF,OAAO,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIb,UAAU,CAAC,gCAAgC,GAAGD,OAAO,CAACc,GAAG,CAAC,CAAC;MACtE;IACD,CAAC;IACD,QAAQ,EAAE,SAAAE,CAAUF,GAAG,EAAE;MACxB,IAAIH,EAAE,EAAE;QACP,IAAIM,MAAM,GAAGX,UAAU,CAACK,EAAE,EAAEG,GAAG,CAAC;QAChC,IAAIP,QAAQ,CAACI,EAAE,CAAC,KAAK,CAAC,EAAE;UACvBA,EAAE,GAAG,KAAKO,SAAS;QACpB;QACA,OAAOD,MAAM;MACd;MACA,OAAO,KAAK;IACb,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUL,GAAG,EAAE;MAAE;MACrB,IAAIH,EAAE,EAAE;QACP,OAAOR,OAAO,CAACQ,EAAE,EAAEG,GAAG,CAAC;MACxB;IACD,CAAC;IACDC,GAAG,EAAE,SAAAA,CAAUD,GAAG,EAAE;MACnB,IAAIH,EAAE,EAAE;QACP,OAAON,OAAO,CAACM,EAAE,EAAEG,GAAG,CAAC;MACxB;MACA,OAAO,KAAK;IACb,CAAC;IACDM,GAAG,EAAE,SAAAA,CAAUN,GAAG,EAAEO,KAAK,EAAE;MAC1B,IAAI,CAACV,EAAE,EAAE;QACR;QACAA,EAAE,GAAG,IAAIT,IAAI,CAAC,CAAC;MAChB;MACAE,OAAO,CAACO,EAAE,EAAEG,GAAG,EAAEO,KAAK,CAAC;IACxB;EACD,CAAC;;EAED;EACA,OAAOT,OAAO;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}