import React, { useState, useEffect } from "react";
import { getAllProduct, getProductDetails, searchProducts } from "./admin/products/FetchApi";
import { getCart, addToCart } from "./shop/cart/FetchApi";

const ApiTestPage = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [searchResults, setSearchResults] = useState([]);

  // Mock user ID (trong thực tế sẽ lấy từ authentication)
  const userId = 1;

  useEffect(() => {
    fetchProducts();
    fetchCart();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await getAllProduct();
      setProducts(data || []);
      console.log("✅ Products loaded:", data);
    } catch (err) {
      setError("Failed to load products: " + err.message);
      console.error("❌ Error loading products:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchProductDetails = async (productId) => {
    try {
      setLoading(true);
      const data = await getProductDetails(productId, userId);
      setSelectedProduct(data);
      console.log("✅ Product details loaded:", data);
    } catch (err) {
      setError("Failed to load product details: " + err.message);
      console.error("❌ Error loading product details:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchCart = async () => {
    try {
      const data = await getCart(userId);
      setCart(data);
      console.log("✅ Cart loaded:", data);
    } catch (err) {
      console.log("ℹ️ Cart not found or empty:", err.message);
    }
  };

  const handleAddToCart = async (productId) => {
    try {
      setLoading(true);
      const result = await addToCart(userId, productId, 1);
      console.log("✅ Added to cart:", result);
      await fetchCart(); // Refresh cart
      alert("Product added to cart successfully!");
    } catch (err) {
      setError("Failed to add to cart: " + err.message);
      console.error("❌ Error adding to cart:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchKeyword.trim()) return;
    
    try {
      setLoading(true);
      const results = await searchProducts(searchKeyword, userId);
      setSearchResults(results || []);
      console.log("✅ Search results:", results);
    } catch (err) {
      setError("Search failed: " + err.message);
      console.error("❌ Search error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">🧪 API Test Page</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
          <button 
            onClick={() => setError(null)}
            className="float-right font-bold"
          >
            ×
          </button>
        </div>
      )}

      {loading && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          Loading...
        </div>
      )}

      {/* Search Section */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">🔍 Search Products</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            placeholder="Enter search keyword..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            onClick={handleSearch}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Search
          </button>
        </div>
        
        {searchResults.length > 0 && (
          <div className="mt-4">
            <h3 className="font-semibold">Search Results ({searchResults.length}):</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
              {searchResults.slice(0, 6).map(product => (
                <div key={product.productId} className="border p-3 rounded">
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-gray-600">{product.category}</p>
                  <p className="text-green-600 font-semibold">
                    {product.price?.toLocaleString('vi-VN')} VND
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Cart Section */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">🛒 Cart Status</h2>
        {cart ? (
          <div>
            <p><strong>User ID:</strong> {cart.userId}</p>
            <p><strong>Total Items:</strong> {cart.cartItems?.length || 0}</p>
            <p><strong>Total Value:</strong> {cart.totalValue?.toLocaleString('vi-VN')} VND</p>
            {cart.cartItems && cart.cartItems.length > 0 && (
              <div className="mt-2">
                <h3 className="font-semibold">Items:</h3>
                {cart.cartItems.map((item, index) => (
                  <div key={index} className="text-sm">
                    - {item.productName} (Qty: {item.quantity})
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <p className="text-gray-500">Cart is empty or not loaded</p>
        )}
      </div>

      {/* Products Section */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">📦 All Products ({products.length})</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {products.map(product => (
            <div key={product.productId} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="mb-2">
                <img 
                  src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.jpg'}
                  alt={product.name}
                  className="w-full h-32 object-cover rounded"
                  onError={(e) => {
                    e.target.src = '/placeholder-image.jpg';
                  }}
                />
              </div>
              
              <h3 className="font-semibold text-sm mb-1">{product.name}</h3>
              <p className="text-xs text-gray-600 mb-2">{product.category}</p>
              <p className="text-green-600 font-semibold mb-2">
                {product.price?.toLocaleString('vi-VN')} VND
              </p>
              
              <div className="flex gap-2">
                <button
                  onClick={() => fetchProductDetails(product.productId)}
                  className="flex-1 px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                >
                  Details
                </button>
                <button
                  onClick={() => handleAddToCart(product.productId)}
                  className="flex-1 px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                >
                  Add to Cart
                </button>
              </div>
            </div>
          ))}
        </div>

        {products.length === 0 && !loading && (
          <div className="text-center text-gray-500 py-8">
            No products found. Make sure the backend is running.
          </div>
        )}
      </div>

      {/* Product Details Modal */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-bold">{selectedProduct.name}</h2>
              <button
                onClick={() => setSelectedProduct(null)}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-2 text-sm">
              <p><strong>ID:</strong> {selectedProduct.productId}</p>
              <p><strong>Category:</strong> {selectedProduct.category}</p>
              <p><strong>Price:</strong> {selectedProduct.price?.toLocaleString('vi-VN')} VND</p>
              <p><strong>Description:</strong> {selectedProduct.description}</p>
              <p><strong>Weight:</strong> {selectedProduct.weight} kg</p>
              <p><strong>Rush Eligible:</strong> {selectedProduct.rushEligible ? 'Yes' : 'No'}</p>
              <p><strong>Availability:</strong> {selectedProduct.availabilityStatus}</p>
              {selectedProduct.specifications && (
                <p><strong>Specifications:</strong> {selectedProduct.specifications}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiTestPage;
