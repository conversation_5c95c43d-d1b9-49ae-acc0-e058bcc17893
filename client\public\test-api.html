<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>🧪 API Test Page</h1>
    <p>Test kết nối trực tiếp đến backend API</p>
    
    <div>
        <button onclick="testAPI()">Test API Connection</button>
        <button onclick="testProducts()">Test Products API</button>
        <button onclick="testProductDetails()">Test Product Details</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_URL = 'http://localhost:8080';
        
        function addResult(title, content, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testAPI() {
            try {
                console.log('🔍 Testing basic API connection...');
                console.log('🌐 API URL:', API_URL);
                console.log('📡 Full URL:', `${API_URL}/api/v1/products/customer`);

                const response = await fetch(`${API_URL}/api/v1/products/customer`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });

                console.log('📊 Response status:', response.status);
                console.log('📋 Response headers:', [...response.headers.entries()]);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ Error response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}\nResponse: ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ API Response:', data);
                console.log('📦 Data length:', data.length);

                addResult(
                    '✅ API Connection Success',
                    `Status: ${response.status}\nData type: ${Array.isArray(data) ? 'Array' : typeof data}\nItems count: ${data.length || 'N/A'}\n\nFirst item:\n${JSON.stringify(data[0] || {}, null, 2)}`
                );

            } catch (error) {
                console.error('❌ API Test failed:', error);
                addResult(
                    '❌ API Connection Failed',
                    `Error: ${error.message}\n\nAPI URL: ${API_URL}\nFull URL: ${API_URL}/api/v1/products/customer\n\nPossible causes:\n1. Backend not running on port 8080\n2. CORS issues\n3. Network connectivity\n4. API endpoint changed\n5. Frontend .env file wrong`,
                    true
                );
            }
        }
        
        async function testProducts() {
            try {
                console.log('🛍️ Testing products API...');
                const response = await fetch(`${API_URL}/api/v1/products/customer`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const products = await response.json();
                console.log('✅ Products loaded:', products);
                
                addResult(
                    '✅ Products API Success', 
                    `Found ${products.length} products\n\nSample products:\n${products.slice(0, 3).map(p => `- ${p.name} (${p.price} VND)`).join('\n')}\n\nFull first product:\n${JSON.stringify(products[0], null, 2)}`
                );
                
            } catch (error) {
                console.error('❌ Products test failed:', error);
                addResult('❌ Products API Failed', error.message, true);
            }
        }
        
        async function testProductDetails() {
            try {
                console.log('📦 Testing product details API...');
                const productId = 1; // Test with first product
                const userId = 1; // Mock user ID
                
                const response = await fetch(`${API_URL}/api/v1/products/customer/${productId}?userId=${userId}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const product = await response.json();
                console.log('✅ Product details loaded:', product);
                
                addResult(
                    '✅ Product Details Success', 
                    `Product: ${product.name}\nPrice: ${product.price} VND\nCategory: ${product.category}\nAvailability: ${product.availabilityStatus}\n\nFull details:\n${JSON.stringify(product, null, 2)}`
                );
                
            } catch (error) {
                console.error('❌ Product details test failed:', error);
                addResult('❌ Product Details Failed', error.message, true);
            }
        }
        
        // Auto-run basic test when page loads
        window.onload = function() {
            console.log('🚀 Page loaded, running basic API test...');
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
