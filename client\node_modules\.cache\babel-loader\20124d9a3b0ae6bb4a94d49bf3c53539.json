{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllOrders = async status => {\n  try {\n    // Backend chưa có API get all orders, tạm thời return empty array\n    // TODO: Implement when backend has order management API\n    console.log(\"⚠️ getAllOrders: Backend chưa có API get all orders\");\n    return [];\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const editCategory = async (oId, status) => {\n  let data = {\n    oId: oId,\n    status: status\n  };\n  console.log(data);\n  try {\n    let res = await axios.post(`${apiURL}/api/order/update-order`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteOrder = async oId => {\n  let data = {\n    oId: oId\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/order/delete-order`, data);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllOrders", "status", "console", "log", "error", "editCategory", "oId", "data", "res", "post", "deleteOrder"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllOrders = async (status) => {\r\n  try {\r\n    // Backend chưa có API get all orders, tạm thời return empty array\r\n    // TODO: Implement when backend has order management API\r\n    console.log(\"⚠️ getAllOrders: Backend chưa có API get all orders\");\r\n    return [];\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const editCategory = async (oId, status) => {\r\n  let data = { oId: oId, status: status };\r\n  console.log(data);\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/update-order`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteOrder = async (oId) => {\r\n  let data = { oId: oId };\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/delete-order`, data);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,YAAY,GAAG,MAAOC,MAAM,IAAK;EAC5C,IAAI;IACF;IACA;IACAC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClE,OAAO,EAAE;EACX,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,GAAG,EAAEL,MAAM,KAAK;EACjD,IAAIM,IAAI,GAAG;IAAED,GAAG,EAAEA,GAAG;IAAEL,MAAM,EAAEA;EAAO,CAAC;EACvCC,OAAO,CAACC,GAAG,CAACI,IAAI,CAAC;EACjB,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,yBAAyB,EAAEW,IAAI,CAAC;IACpE,OAAOC,GAAG,CAACD,IAAI;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMM,WAAW,GAAG,MAAOJ,GAAG,IAAK;EACxC,IAAIC,IAAI,GAAG;IAAED,GAAG,EAAEA;EAAI,CAAC;EACvB,IAAI;IACF,IAAIE,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,yBAAyB,EAAEW,IAAI,CAAC;IACpE,OAAOC,GAAG,CAACD,IAAI;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdF,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}