{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"colorSchemes\", \"components\", \"generateCssVars\", \"cssVarPrefix\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport deepmerge from '@mui/utils/deepmerge';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport ThemeProvider from '../ThemeProvider';\nimport InitColorSchemeScript, { DEFAULT_ATTRIBUTE, DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nimport useCurrentColorScheme from './useCurrentColorScheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    attribute: defaultAttribute = DEFAULT_ATTRIBUTE,\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    defaultMode: designSystemMode = 'light',\n    defaultColorScheme: designSystemColorScheme,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    resolveTheme,\n    excludeVariablesFromRoot\n  } = options;\n  if (!defaultTheme.colorSchemes || typeof designSystemColorScheme === 'string' && !defaultTheme.colorSchemes[designSystemColorScheme] || typeof designSystemColorScheme === 'object' && !defaultTheme.colorSchemes[designSystemColorScheme == null ? void 0 : designSystemColorScheme.light] || typeof designSystemColorScheme === 'object' && !defaultTheme.colorSchemes[designSystemColorScheme == null ? void 0 : designSystemColorScheme.dark]) {\n    console.error(`MUI: \\`${designSystemColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n  }\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => {\n    const value = React.useContext(ColorSchemeContext);\n    if (!value) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: \\`useColorScheme\\` must be called under <CssVarsProvider />` : _formatMuiErrorMessage(19));\n    }\n    return value;\n  };\n  function CssVarsProvider(props) {\n    const {\n      children,\n      theme: themeProp = defaultTheme,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      attribute = defaultAttribute,\n      defaultMode = designSystemMode,\n      defaultColorScheme = designSystemColorScheme,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      colorSchemeSelector = ':root',\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const scopedTheme = themeProp[themeId];\n    const _ref = scopedTheme || themeProp,\n      {\n        colorSchemes = {},\n        components = {},\n        generateCssVars = () => ({\n          vars: {},\n          css: {}\n        }),\n        cssVarPrefix\n      } = _ref,\n      restThemeProp = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const allColorSchemes = Object.keys(colorSchemes);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageWindow\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    const calculatedMode = (() => {\n      if (mode) {\n        return mode;\n      }\n      // This scope occurs on the server\n      if (defaultMode === 'system') {\n        return designSystemMode;\n      }\n      return defaultMode;\n    })();\n    const calculatedColorScheme = (() => {\n      if (!colorScheme) {\n        // This scope occurs on the server\n        if (calculatedMode === 'dark') {\n          return defaultDarkColorScheme;\n        }\n        // use light color scheme, if default mode is 'light' | 'system'\n        return defaultLightColorScheme;\n      }\n      return colorScheme;\n    })();\n\n    // 2. Create CSS variables and store them in objects (to be generated in stylesheets in the final step)\n    const {\n      css: rootCss,\n      vars: rootVars\n    } = generateCssVars();\n\n    // 3. Start composing the theme object\n    const theme = _extends({}, restThemeProp, {\n      components,\n      colorSchemes,\n      cssVarPrefix,\n      vars: rootVars,\n      getColorSchemeSelector: targetColorScheme => `[${attribute}=\"${targetColorScheme}\"] &`\n    });\n\n    // 4. Create color CSS variables and store them in objects (to be generated in stylesheets in the final step)\n    //    The default color scheme stylesheet is constructed to have the least CSS specificity.\n    //    The other color schemes uses selector, default as data attribute, to increase the CSS specificity so that they can override the default color scheme stylesheet.\n    const defaultColorSchemeStyleSheet = {};\n    const otherColorSchemesStyleSheet = {};\n    Object.entries(colorSchemes).forEach(([key, scheme]) => {\n      const {\n        css,\n        vars\n      } = generateCssVars(key);\n      theme.vars = deepmerge(theme.vars, vars);\n      if (key === calculatedColorScheme) {\n        // 4.1 Merge the selected color scheme to the theme\n        Object.keys(scheme).forEach(schemeKey => {\n          if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n            // shallow merge the 1st level structure of the theme.\n            theme[schemeKey] = _extends({}, theme[schemeKey], scheme[schemeKey]);\n          } else {\n            theme[schemeKey] = scheme[schemeKey];\n          }\n        });\n        if (theme.palette) {\n          theme.palette.colorScheme = key;\n        }\n      }\n      const resolvedDefaultColorScheme = (() => {\n        if (typeof defaultColorScheme === 'string') {\n          return defaultColorScheme;\n        }\n        if (defaultMode === 'dark') {\n          return defaultColorScheme.dark;\n        }\n        return defaultColorScheme.light;\n      })();\n      if (key === resolvedDefaultColorScheme) {\n        if (excludeVariablesFromRoot) {\n          const excludedVariables = {};\n          excludeVariablesFromRoot(cssVarPrefix).forEach(cssVar => {\n            excludedVariables[cssVar] = css[cssVar];\n            delete css[cssVar];\n          });\n          defaultColorSchemeStyleSheet[`[${attribute}=\"${key}\"]`] = excludedVariables;\n        }\n        defaultColorSchemeStyleSheet[`${colorSchemeSelector}, [${attribute}=\"${key}\"]`] = css;\n      } else {\n        otherColorSchemesStyleSheet[`${colorSchemeSelector === ':root' ? '' : colorSchemeSelector}[${attribute}=\"${key}\"]`] = css;\n      }\n    });\n    theme.vars = deepmerge(theme.vars, rootVars);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    React.useEffect(() => {\n      if (colorScheme && colorSchemeNode) {\n        // attaches attribute to <html> because the css variables are attached to :root (html)\n        colorSchemeNode.setAttribute(attribute, colorScheme);\n      }\n    }, [colorScheme, attribute, colorSchemeNode]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode,\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || nested && (upperTheme == null ? void 0 : upperTheme.cssVarPrefix) === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [shouldGenerateStyleSheet && /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [/*#__PURE__*/_jsx(GlobalStyles, {\n          styles: {\n            [colorSchemeSelector]: rootCss\n          }\n        }), /*#__PURE__*/_jsx(GlobalStyles, {\n          styles: defaultColorSchemeStyleSheet\n        }), /*#__PURE__*/_jsx(GlobalStyles, {\n          styles: otherColorSchemesStyleSheet\n        })]\n      }), /*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: resolveTheme ? resolveTheme(theme) : theme,\n        children: children\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The body attribute name to attach colorScheme.\n     */\n    attribute: PropTypes.string,\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * The CSS selector for attaching the generated custom properties\n     */\n    colorSchemeSelector: PropTypes.string,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The initial color scheme used.\n     */\n    defaultColorScheme: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n    /**\n     * The initial mode used.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof designSystemColorScheme === 'string' ? designSystemColorScheme : designSystemColorScheme.light;\n  const defaultDarkColorScheme = typeof designSystemColorScheme === 'string' ? designSystemColorScheme : designSystemColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript(_extends({\n    attribute: defaultAttribute,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultMode: designSystemMode,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey\n  }, params));\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_formatMuiErrorMessage", "_excluded", "React", "PropTypes", "deepmerge", "GlobalStyles", "useTheme", "muiUseTheme", "ThemeProvider", "InitColorSchemeScript", "DEFAULT_ATTRIBUTE", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "DEFAULT_MODE_STORAGE_KEY", "useCurrentColorScheme", "jsx", "_jsx", "jsxs", "_jsxs", "DISABLE_CSS_TRANSITION", "createCssVarsProvider", "options", "themeId", "theme", "defaultTheme", "attribute", "defaultAttribute", "modeStorageKey", "defaultModeStorageKey", "colorSchemeStorageKey", "defaultColorSchemeStorageKey", "defaultMode", "designSystemMode", "defaultColorScheme", "designSystemColorScheme", "disableTransitionOnChange", "designSystemTransitionOnChange", "resolveTheme", "excludeVariablesFromRoot", "colorSchemes", "light", "dark", "console", "error", "ColorSchemeContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useColorScheme", "value", "useContext", "Error", "CssVarsProvider", "props", "children", "themeProp", "storageWindow", "window", "documentNode", "document", "colorSchemeNode", "documentElement", "colorSchemeSelector", "disableNestedContext", "disableStyleSheetGeneration", "hasMounted", "useRef", "upperTheme", "ctx", "nested", "scopedTheme", "_ref", "components", "generateCssVars", "vars", "css", "cssVarPrefix", "restThemeProp", "allColorSchemes", "Object", "keys", "defaultLightColorScheme", "defaultDarkColorScheme", "mode", "stateMode", "setMode", "systemMode", "lightColorScheme", "darkColorScheme", "colorScheme", "stateColorScheme", "setColorScheme", "supportedColorSchemes", "calculatedMode", "calculatedColorScheme", "rootCss", "rootVars", "getColorSchemeSelector", "targetColorScheme", "defaultColorSchemeStyleSheet", "otherColorSchemesStyleSheet", "entries", "for<PERSON>ach", "key", "scheme", "<PERSON><PERSON>ey", "palette", "resolvedDefaultColorScheme", "excludedVariables", "cssVar", "useEffect", "setAttribute", "timer", "current", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getComputedStyle", "body", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "contextValue", "useMemo", "shouldGenerateStyleSheet", "element", "Fragment", "styles", "Provider", "propTypes", "string", "node", "any", "oneOfType", "object", "bool", "getInitColorSchemeScript", "params"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/system/esm/cssVars/createCssVarsProvider.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"colorSchemes\", \"components\", \"generateCssVars\", \"cssVarPrefix\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport deepmerge from '@mui/utils/deepmerge';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport ThemeProvider from '../ThemeProvider';\nimport InitColorSchemeScript, { DEFAULT_ATTRIBUTE, DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nimport useCurrentColorScheme from './useCurrentColorScheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    attribute: defaultAttribute = DEFAULT_ATTRIBUTE,\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    defaultMode: designSystemMode = 'light',\n    defaultColorScheme: designSystemColorScheme,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    resolveTheme,\n    excludeVariablesFromRoot\n  } = options;\n  if (!defaultTheme.colorSchemes || typeof designSystemColorScheme === 'string' && !defaultTheme.colorSchemes[designSystemColorScheme] || typeof designSystemColorScheme === 'object' && !defaultTheme.colorSchemes[designSystemColorScheme == null ? void 0 : designSystemColorScheme.light] || typeof designSystemColorScheme === 'object' && !defaultTheme.colorSchemes[designSystemColorScheme == null ? void 0 : designSystemColorScheme.dark]) {\n    console.error(`MUI: \\`${designSystemColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n  }\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => {\n    const value = React.useContext(ColorSchemeContext);\n    if (!value) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: \\`useColorScheme\\` must be called under <CssVarsProvider />` : _formatMuiErrorMessage(19));\n    }\n    return value;\n  };\n  function CssVarsProvider(props) {\n    const {\n      children,\n      theme: themeProp = defaultTheme,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      attribute = defaultAttribute,\n      defaultMode = designSystemMode,\n      defaultColorScheme = designSystemColorScheme,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      colorSchemeSelector = ':root',\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const scopedTheme = themeProp[themeId];\n    const _ref = scopedTheme || themeProp,\n      {\n        colorSchemes = {},\n        components = {},\n        generateCssVars = () => ({\n          vars: {},\n          css: {}\n        }),\n        cssVarPrefix\n      } = _ref,\n      restThemeProp = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const allColorSchemes = Object.keys(colorSchemes);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageWindow\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    const calculatedMode = (() => {\n      if (mode) {\n        return mode;\n      }\n      // This scope occurs on the server\n      if (defaultMode === 'system') {\n        return designSystemMode;\n      }\n      return defaultMode;\n    })();\n    const calculatedColorScheme = (() => {\n      if (!colorScheme) {\n        // This scope occurs on the server\n        if (calculatedMode === 'dark') {\n          return defaultDarkColorScheme;\n        }\n        // use light color scheme, if default mode is 'light' | 'system'\n        return defaultLightColorScheme;\n      }\n      return colorScheme;\n    })();\n\n    // 2. Create CSS variables and store them in objects (to be generated in stylesheets in the final step)\n    const {\n      css: rootCss,\n      vars: rootVars\n    } = generateCssVars();\n\n    // 3. Start composing the theme object\n    const theme = _extends({}, restThemeProp, {\n      components,\n      colorSchemes,\n      cssVarPrefix,\n      vars: rootVars,\n      getColorSchemeSelector: targetColorScheme => `[${attribute}=\"${targetColorScheme}\"] &`\n    });\n\n    // 4. Create color CSS variables and store them in objects (to be generated in stylesheets in the final step)\n    //    The default color scheme stylesheet is constructed to have the least CSS specificity.\n    //    The other color schemes uses selector, default as data attribute, to increase the CSS specificity so that they can override the default color scheme stylesheet.\n    const defaultColorSchemeStyleSheet = {};\n    const otherColorSchemesStyleSheet = {};\n    Object.entries(colorSchemes).forEach(([key, scheme]) => {\n      const {\n        css,\n        vars\n      } = generateCssVars(key);\n      theme.vars = deepmerge(theme.vars, vars);\n      if (key === calculatedColorScheme) {\n        // 4.1 Merge the selected color scheme to the theme\n        Object.keys(scheme).forEach(schemeKey => {\n          if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n            // shallow merge the 1st level structure of the theme.\n            theme[schemeKey] = _extends({}, theme[schemeKey], scheme[schemeKey]);\n          } else {\n            theme[schemeKey] = scheme[schemeKey];\n          }\n        });\n        if (theme.palette) {\n          theme.palette.colorScheme = key;\n        }\n      }\n      const resolvedDefaultColorScheme = (() => {\n        if (typeof defaultColorScheme === 'string') {\n          return defaultColorScheme;\n        }\n        if (defaultMode === 'dark') {\n          return defaultColorScheme.dark;\n        }\n        return defaultColorScheme.light;\n      })();\n      if (key === resolvedDefaultColorScheme) {\n        if (excludeVariablesFromRoot) {\n          const excludedVariables = {};\n          excludeVariablesFromRoot(cssVarPrefix).forEach(cssVar => {\n            excludedVariables[cssVar] = css[cssVar];\n            delete css[cssVar];\n          });\n          defaultColorSchemeStyleSheet[`[${attribute}=\"${key}\"]`] = excludedVariables;\n        }\n        defaultColorSchemeStyleSheet[`${colorSchemeSelector}, [${attribute}=\"${key}\"]`] = css;\n      } else {\n        otherColorSchemesStyleSheet[`${colorSchemeSelector === ':root' ? '' : colorSchemeSelector}[${attribute}=\"${key}\"]`] = css;\n      }\n    });\n    theme.vars = deepmerge(theme.vars, rootVars);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    React.useEffect(() => {\n      if (colorScheme && colorSchemeNode) {\n        // attaches attribute to <html> because the css variables are attached to :root (html)\n        colorSchemeNode.setAttribute(attribute, colorScheme);\n      }\n    }, [colorScheme, attribute, colorSchemeNode]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode,\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || nested && (upperTheme == null ? void 0 : upperTheme.cssVarPrefix) === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [shouldGenerateStyleSheet && /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [/*#__PURE__*/_jsx(GlobalStyles, {\n          styles: {\n            [colorSchemeSelector]: rootCss\n          }\n        }), /*#__PURE__*/_jsx(GlobalStyles, {\n          styles: defaultColorSchemeStyleSheet\n        }), /*#__PURE__*/_jsx(GlobalStyles, {\n          styles: otherColorSchemesStyleSheet\n        })]\n      }), /*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: resolveTheme ? resolveTheme(theme) : theme,\n        children: children\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The body attribute name to attach colorScheme.\n     */\n    attribute: PropTypes.string,\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * The CSS selector for attaching the generated custom properties\n     */\n    colorSchemeSelector: PropTypes.string,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The initial color scheme used.\n     */\n    defaultColorScheme: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n    /**\n     * The initial mode used.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof designSystemColorScheme === 'string' ? designSystemColorScheme : designSystemColorScheme.light;\n  const defaultDarkColorScheme = typeof designSystemColorScheme === 'string' ? designSystemColorScheme : designSystemColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript(_extends({\n    attribute: defaultAttribute,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultMode: designSystemMode,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey\n  }, params));\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAC;AACnF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,QAAQ,IAAIC,WAAW,QAAQ,sBAAsB;AAC9D,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,qBAAqB,IAAIC,iBAAiB,EAAEC,gCAAgC,EAAEC,wBAAwB,QAAQ,gDAAgD;AACrK,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,OAAO,MAAMC,sBAAsB,GAAG,0JAA0J;AAChM,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,OAAO;IACP;AACJ;AACA;AACA;AACA;AACA;IACIC,KAAK,EAAEC,YAAY,GAAG,CAAC,CAAC;IACxBC,SAAS,EAAEC,gBAAgB,GAAGf,iBAAiB;IAC/CgB,cAAc,EAAEC,qBAAqB,GAAGf,wBAAwB;IAChEgB,qBAAqB,EAAEC,4BAA4B,GAAGlB,gCAAgC;IACtFmB,WAAW,EAAEC,gBAAgB,GAAG,OAAO;IACvCC,kBAAkB,EAAEC,uBAAuB;IAC3CC,yBAAyB,EAAEC,8BAA8B,GAAG,KAAK;IACjEC,YAAY;IACZC;EACF,CAAC,GAAGjB,OAAO;EACX,IAAI,CAACG,YAAY,CAACe,YAAY,IAAI,OAAOL,uBAAuB,KAAK,QAAQ,IAAI,CAACV,YAAY,CAACe,YAAY,CAACL,uBAAuB,CAAC,IAAI,OAAOA,uBAAuB,KAAK,QAAQ,IAAI,CAACV,YAAY,CAACe,YAAY,CAACL,uBAAuB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACM,KAAK,CAAC,IAAI,OAAON,uBAAuB,KAAK,QAAQ,IAAI,CAACV,YAAY,CAACe,YAAY,CAACL,uBAAuB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACO,IAAI,CAAC,EAAE;IACjbC,OAAO,CAACC,KAAK,CAAC,UAAUT,uBAAuB,8CAA8C,CAAC;EAChG;EACA,MAAMU,kBAAkB,GAAG,aAAazC,KAAK,CAAC0C,aAAa,CAACC,SAAS,CAAC;EACtE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCL,kBAAkB,CAACM,WAAW,GAAG,oBAAoB;EACvD;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAGjD,KAAK,CAACkD,UAAU,CAACT,kBAAkB,CAAC;IAClD,IAAI,CAACQ,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAACP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,kEAAkE,GAAGhD,sBAAsB,CAAC,EAAE,CAAC,CAAC;IAC1J;IACA,OAAOmD,KAAK;EACd,CAAC;EACD,SAASG,eAAeA,CAACC,KAAK,EAAE;IAC9B,MAAM;MACJC,QAAQ;MACRlC,KAAK,EAAEmC,SAAS,GAAGlC,YAAY;MAC/BG,cAAc,GAAGC,qBAAqB;MACtCC,qBAAqB,GAAGC,4BAA4B;MACpDL,SAAS,GAAGC,gBAAgB;MAC5BK,WAAW,GAAGC,gBAAgB;MAC9BC,kBAAkB,GAAGC,uBAAuB;MAC5CC,yBAAyB,GAAGC,8BAA8B;MAC1DuB,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGd,SAAS,GAAGc,MAAM;MAClEC,YAAY,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGhB,SAAS,GAAGgB,QAAQ;MACrEC,eAAe,GAAG,OAAOD,QAAQ,KAAK,WAAW,GAAGhB,SAAS,GAAGgB,QAAQ,CAACE,eAAe;MACxFC,mBAAmB,GAAG,OAAO;MAC7BC,oBAAoB,GAAG,KAAK;MAC5BC,2BAA2B,GAAG;IAChC,CAAC,GAAGX,KAAK;IACT,MAAMY,UAAU,GAAGjE,KAAK,CAACkE,MAAM,CAAC,KAAK,CAAC;IACtC,MAAMC,UAAU,GAAG9D,WAAW,CAAC,CAAC;IAChC,MAAM+D,GAAG,GAAGpE,KAAK,CAACkD,UAAU,CAACT,kBAAkB,CAAC;IAChD,MAAM4B,MAAM,GAAG,CAAC,CAACD,GAAG,IAAI,CAACL,oBAAoB;IAC7C,MAAMO,WAAW,GAAGf,SAAS,CAACpC,OAAO,CAAC;IACtC,MAAMoD,IAAI,GAAGD,WAAW,IAAIf,SAAS;MACnC;QACEnB,YAAY,GAAG,CAAC,CAAC;QACjBoC,UAAU,GAAG,CAAC,CAAC;QACfC,eAAe,GAAGA,CAAA,MAAO;UACvBC,IAAI,EAAE,CAAC,CAAC;UACRC,GAAG,EAAE,CAAC;QACR,CAAC,CAAC;QACFC;MACF,CAAC,GAAGL,IAAI;MACRM,aAAa,GAAGhF,6BAA6B,CAAC0E,IAAI,EAAExE,SAAS,CAAC;IAChE,MAAM+E,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC5C,YAAY,CAAC;IACjD,MAAM6C,uBAAuB,GAAG,OAAOnD,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAACO,KAAK;IACtH,MAAM6C,sBAAsB,GAAG,OAAOpD,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAACQ,IAAI;;IAEpH;IACA,MAAM;MACJ6C,IAAI,EAAEC,SAAS;MACfC,OAAO;MACPC,UAAU;MACVC,gBAAgB;MAChBC,eAAe;MACfC,WAAW,EAAEC,gBAAgB;MAC7BC;IACF,CAAC,GAAGhF,qBAAqB,CAAC;MACxBiF,qBAAqB,EAAEd,eAAe;MACtCG,uBAAuB;MACvBC,sBAAsB;MACtB1D,cAAc;MACdE,qBAAqB;MACrBE,WAAW;MACX4B;IACF,CAAC,CAAC;IACF,IAAI2B,IAAI,GAAGC,SAAS;IACpB,IAAIK,WAAW,GAAGC,gBAAgB;IAClC,IAAIrB,MAAM,EAAE;MACVc,IAAI,GAAGf,GAAG,CAACe,IAAI;MACfM,WAAW,GAAGrB,GAAG,CAACqB,WAAW;IAC/B;IACA,MAAMI,cAAc,GAAG,CAAC,MAAM;MAC5B,IAAIV,IAAI,EAAE;QACR,OAAOA,IAAI;MACb;MACA;MACA,IAAIvD,WAAW,KAAK,QAAQ,EAAE;QAC5B,OAAOC,gBAAgB;MACzB;MACA,OAAOD,WAAW;IACpB,CAAC,EAAE,CAAC;IACJ,MAAMkE,qBAAqB,GAAG,CAAC,MAAM;MACnC,IAAI,CAACL,WAAW,EAAE;QAChB;QACA,IAAII,cAAc,KAAK,MAAM,EAAE;UAC7B,OAAOX,sBAAsB;QAC/B;QACA;QACA,OAAOD,uBAAuB;MAChC;MACA,OAAOQ,WAAW;IACpB,CAAC,EAAE,CAAC;;IAEJ;IACA,MAAM;MACJd,GAAG,EAAEoB,OAAO;MACZrB,IAAI,EAAEsB;IACR,CAAC,GAAGvB,eAAe,CAAC,CAAC;;IAErB;IACA,MAAMrD,KAAK,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAEiF,aAAa,EAAE;MACxCL,UAAU;MACVpC,YAAY;MACZwC,YAAY;MACZF,IAAI,EAAEsB,QAAQ;MACdC,sBAAsB,EAAEC,iBAAiB,IAAI,IAAI5E,SAAS,KAAK4E,iBAAiB;IAClF,CAAC,CAAC;;IAEF;IACA;IACA;IACA,MAAMC,4BAA4B,GAAG,CAAC,CAAC;IACvC,MAAMC,2BAA2B,GAAG,CAAC,CAAC;IACtCrB,MAAM,CAACsB,OAAO,CAACjE,YAAY,CAAC,CAACkE,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;MACtD,MAAM;QACJ7B,GAAG;QACHD;MACF,CAAC,GAAGD,eAAe,CAAC8B,GAAG,CAAC;MACxBnF,KAAK,CAACsD,IAAI,GAAGxE,SAAS,CAACkB,KAAK,CAACsD,IAAI,EAAEA,IAAI,CAAC;MACxC,IAAI6B,GAAG,KAAKT,qBAAqB,EAAE;QACjC;QACAf,MAAM,CAACC,IAAI,CAACwB,MAAM,CAAC,CAACF,OAAO,CAACG,SAAS,IAAI;UACvC,IAAID,MAAM,CAACC,SAAS,CAAC,IAAI,OAAOD,MAAM,CAACC,SAAS,CAAC,KAAK,QAAQ,EAAE;YAC9D;YACArF,KAAK,CAACqF,SAAS,CAAC,GAAG7G,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAACqF,SAAS,CAAC,EAAED,MAAM,CAACC,SAAS,CAAC,CAAC;UACtE,CAAC,MAAM;YACLrF,KAAK,CAACqF,SAAS,CAAC,GAAGD,MAAM,CAACC,SAAS,CAAC;UACtC;QACF,CAAC,CAAC;QACF,IAAIrF,KAAK,CAACsF,OAAO,EAAE;UACjBtF,KAAK,CAACsF,OAAO,CAACjB,WAAW,GAAGc,GAAG;QACjC;MACF;MACA,MAAMI,0BAA0B,GAAG,CAAC,MAAM;QACxC,IAAI,OAAO7E,kBAAkB,KAAK,QAAQ,EAAE;UAC1C,OAAOA,kBAAkB;QAC3B;QACA,IAAIF,WAAW,KAAK,MAAM,EAAE;UAC1B,OAAOE,kBAAkB,CAACQ,IAAI;QAChC;QACA,OAAOR,kBAAkB,CAACO,KAAK;MACjC,CAAC,EAAE,CAAC;MACJ,IAAIkE,GAAG,KAAKI,0BAA0B,EAAE;QACtC,IAAIxE,wBAAwB,EAAE;UAC5B,MAAMyE,iBAAiB,GAAG,CAAC,CAAC;UAC5BzE,wBAAwB,CAACyC,YAAY,CAAC,CAAC0B,OAAO,CAACO,MAAM,IAAI;YACvDD,iBAAiB,CAACC,MAAM,CAAC,GAAGlC,GAAG,CAACkC,MAAM,CAAC;YACvC,OAAOlC,GAAG,CAACkC,MAAM,CAAC;UACpB,CAAC,CAAC;UACFV,4BAA4B,CAAC,IAAI7E,SAAS,KAAKiF,GAAG,IAAI,CAAC,GAAGK,iBAAiB;QAC7E;QACAT,4BAA4B,CAAC,GAAGrC,mBAAmB,MAAMxC,SAAS,KAAKiF,GAAG,IAAI,CAAC,GAAG5B,GAAG;MACvF,CAAC,MAAM;QACLyB,2BAA2B,CAAC,GAAGtC,mBAAmB,KAAK,OAAO,GAAG,EAAE,GAAGA,mBAAmB,IAAIxC,SAAS,KAAKiF,GAAG,IAAI,CAAC,GAAG5B,GAAG;MAC3H;IACF,CAAC,CAAC;IACFvD,KAAK,CAACsD,IAAI,GAAGxE,SAAS,CAACkB,KAAK,CAACsD,IAAI,EAAEsB,QAAQ,CAAC;;IAE5C;IACA;IACAhG,KAAK,CAAC8G,SAAS,CAAC,MAAM;MACpB,IAAIrB,WAAW,IAAI7B,eAAe,EAAE;QAClC;QACAA,eAAe,CAACmD,YAAY,CAACzF,SAAS,EAAEmE,WAAW,CAAC;MACtD;IACF,CAAC,EAAE,CAACA,WAAW,EAAEnE,SAAS,EAAEsC,eAAe,CAAC,CAAC;;IAE7C;IACA;IACA5D,KAAK,CAAC8G,SAAS,CAAC,MAAM;MACpB,IAAIE,KAAK;MACT,IAAIhF,yBAAyB,IAAIiC,UAAU,CAACgD,OAAO,IAAIvD,YAAY,EAAE;QACnE,MAAMiB,GAAG,GAAGjB,YAAY,CAACwD,aAAa,CAAC,OAAO,CAAC;QAC/CvC,GAAG,CAACwC,WAAW,CAACzD,YAAY,CAAC0D,cAAc,CAACpG,sBAAsB,CAAC,CAAC;QACpE0C,YAAY,CAAC2D,IAAI,CAACF,WAAW,CAACxC,GAAG,CAAC;;QAElC;QACA,CAAC,MAAMlB,MAAM,CAAC6D,gBAAgB,CAAC5D,YAAY,CAAC6D,IAAI,CAAC,EAAE,CAAC;QACpDP,KAAK,GAAGQ,UAAU,CAAC,MAAM;UACvB9D,YAAY,CAAC2D,IAAI,CAACI,WAAW,CAAC9C,GAAG,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC;MACP;MACA,OAAO,MAAM;QACX+C,YAAY,CAACV,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,EAAE,CAACvB,WAAW,EAAEzD,yBAAyB,EAAE0B,YAAY,CAAC,CAAC;IAC1D1D,KAAK,CAAC8G,SAAS,CAAC,MAAM;MACpB7C,UAAU,CAACgD,OAAO,GAAG,IAAI;MACzB,OAAO,MAAM;QACXhD,UAAU,CAACgD,OAAO,GAAG,KAAK;MAC5B,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IACN,MAAMU,YAAY,GAAG3H,KAAK,CAAC4H,OAAO,CAAC,OAAO;MACxC9C,eAAe;MACfW,WAAW;MACXD,eAAe;MACfD,gBAAgB;MAChBJ,IAAI;MACJQ,cAAc;MACdN,OAAO;MACPC;IACF,CAAC,CAAC,EAAE,CAACR,eAAe,EAAEW,WAAW,EAAED,eAAe,EAAED,gBAAgB,EAAEJ,IAAI,EAAEQ,cAAc,EAAEN,OAAO,EAAEC,UAAU,CAAC,CAAC;IACjH,IAAIuC,wBAAwB,GAAG,IAAI;IACnC,IAAI7D,2BAA2B,IAAIK,MAAM,IAAI,CAACF,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,YAAY,MAAMA,YAAY,EAAE;MACrHiD,wBAAwB,GAAG,KAAK;IAClC;IACA,MAAMC,OAAO,GAAG,aAAa/G,KAAK,CAACf,KAAK,CAAC+H,QAAQ,EAAE;MACjDzE,QAAQ,EAAE,CAACuE,wBAAwB,IAAI,aAAa9G,KAAK,CAACf,KAAK,CAAC+H,QAAQ,EAAE;QACxEzE,QAAQ,EAAE,CAAC,aAAazC,IAAI,CAACV,YAAY,EAAE;UACzC6H,MAAM,EAAE;YACN,CAAClE,mBAAmB,GAAGiC;UACzB;QACF,CAAC,CAAC,EAAE,aAAalF,IAAI,CAACV,YAAY,EAAE;UAClC6H,MAAM,EAAE7B;QACV,CAAC,CAAC,EAAE,aAAatF,IAAI,CAACV,YAAY,EAAE;UAClC6H,MAAM,EAAE5B;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE,aAAavF,IAAI,CAACP,aAAa,EAAE;QACnCa,OAAO,EAAEmD,WAAW,GAAGnD,OAAO,GAAGwB,SAAS;QAC1CvB,KAAK,EAAEc,YAAY,GAAGA,YAAY,CAACd,KAAK,CAAC,GAAGA,KAAK;QACjDkC,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIe,MAAM,EAAE;MACV,OAAOyD,OAAO;IAChB;IACA,OAAO,aAAajH,IAAI,CAAC4B,kBAAkB,CAACwF,QAAQ,EAAE;MACpDhF,KAAK,EAAE0E,YAAY;MACnBrE,QAAQ,EAAEwE;IACZ,CAAC,CAAC;EACJ;EACAlF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGM,eAAe,CAAC8E,SAAS,GAAG;IAClE;AACJ;AACA;IACI5G,SAAS,EAAErB,SAAS,CAACkI,MAAM;IAC3B;AACJ;AACA;IACI7E,QAAQ,EAAErD,SAAS,CAACmI,IAAI;IACxB;AACJ;AACA;IACIxE,eAAe,EAAE3D,SAAS,CAACoI,GAAG;IAC9B;AACJ;AACA;IACIvE,mBAAmB,EAAE7D,SAAS,CAACkI,MAAM;IACrC;AACJ;AACA;IACIzG,qBAAqB,EAAEzB,SAAS,CAACkI,MAAM;IACvC;AACJ;AACA;IACIrG,kBAAkB,EAAE7B,SAAS,CAACqI,SAAS,CAAC,CAACrI,SAAS,CAACkI,MAAM,EAAElI,SAAS,CAACsI,MAAM,CAAC,CAAC;IAC7E;AACJ;AACA;IACI3G,WAAW,EAAE3B,SAAS,CAACkI,MAAM;IAC7B;AACJ;AACA;IACIpE,oBAAoB,EAAE9D,SAAS,CAACuI,IAAI;IACpC;AACJ;AACA;AACA;AACA;IACIxE,2BAA2B,EAAE/D,SAAS,CAACuI,IAAI;IAC3C;AACJ;AACA;IACIxG,yBAAyB,EAAE/B,SAAS,CAACuI,IAAI;IACzC;AACJ;AACA;IACI9E,YAAY,EAAEzD,SAAS,CAACoI,GAAG;IAC3B;AACJ;AACA;IACI7G,cAAc,EAAEvB,SAAS,CAACkI,MAAM;IAChC;AACJ;AACA;AACA;IACI3E,aAAa,EAAEvD,SAAS,CAACoI,GAAG;IAC5B;AACJ;AACA;IACIjH,KAAK,EAAEnB,SAAS,CAACsI;EACnB,CAAC,GAAG,KAAK,CAAC;EACV,MAAMtD,uBAAuB,GAAG,OAAOlD,uBAAuB,KAAK,QAAQ,GAAGA,uBAAuB,GAAGA,uBAAuB,CAACM,KAAK;EACrI,MAAM6C,sBAAsB,GAAG,OAAOnD,uBAAuB,KAAK,QAAQ,GAAGA,uBAAuB,GAAGA,uBAAuB,CAACO,IAAI;EACnI,MAAMmG,wBAAwB,GAAGC,MAAM,IAAInI,qBAAqB,CAACX,QAAQ,CAAC;IACxE0B,SAAS,EAAEC,gBAAgB;IAC3BG,qBAAqB,EAAEC,4BAA4B;IACnDC,WAAW,EAAEC,gBAAgB;IAC7BoD,uBAAuB;IACvBC,sBAAsB;IACtB1D,cAAc,EAAEC;EAClB,CAAC,EAAEiH,MAAM,CAAC,CAAC;EACX,OAAO;IACLtF,eAAe;IACfJ,cAAc;IACdyF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}