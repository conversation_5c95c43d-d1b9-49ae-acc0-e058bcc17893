{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\layout\\\\index.js\";\nimport React, { Fragment, createContext } from \"react\";\nimport { Navber, Footer, CartModal } from \"../partials\";\nimport LoginSignup from \"../auth/LoginSignup\";\nimport DebugInteraction from \"../../DebugInteraction\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LayoutContext = /*#__PURE__*/createContext();\nconst Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow\",\n      children: [/*#__PURE__*/_jsxDEV(Navber, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoginSignup, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DebugInteraction, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "<PERSON><PERSON><PERSON>", "Footer", "CartModal", "LoginSignup", "DebugInteraction", "jsxDEV", "_jsxDEV", "LayoutContext", "Layout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/layout/index.js"], "sourcesContent": ["import React, { Fragment, createContext } from \"react\";\r\nimport { Navber, Footer, CartModal } from \"../partials\";\r\nimport LoginSignup from \"../auth/LoginSignup\";\r\nimport DebugInteraction from \"../../DebugInteraction\";\r\n\r\nexport const LayoutContext = createContext();\r\n\r\nconst Layout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <div className=\"flex-grow\">\r\n        <Navber />\r\n        <LoginSignup />\r\n        <CartModal />\r\n        {/* All Children pass from here */}\r\n        {children}\r\n      </div>\r\n      <Footer />\r\n      {/* Debug panel - remove in production */}\r\n      <DebugInteraction />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AACtD,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,aAAa;AACvD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,gBAAgB,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,aAAa,gBAAGR,aAAa,CAAC,CAAC;AAE5C,MAAMS,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC/B,oBACEH,OAAA,CAACR,QAAQ;IAAAW,QAAA,gBACPH,OAAA;MAAKI,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBH,OAAA,CAACN,MAAM;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVR,OAAA,CAACH,WAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfR,OAAA,CAACJ,SAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEZL,QAAQ;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNR,OAAA,CAACL,MAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVR,OAAA,CAACF,gBAAgB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEf,CAAC;AAACC,EAAA,GAfIP,MAAM;AAiBZ,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}