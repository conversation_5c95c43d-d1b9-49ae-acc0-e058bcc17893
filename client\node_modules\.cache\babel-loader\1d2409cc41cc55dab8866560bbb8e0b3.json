{"ast": null, "code": "'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;", "map": {"version": 3, "names": ["useId"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/utils/useId.js"], "sourcesContent": ["'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,kBAAkB;AACpC,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}