{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\layout\\\\index.js\";\nimport React, { Fragment } from \"react\";\nimport AdminNavber from \"../partials/AdminNavber\";\nimport AdminSidebar from \"../partials/AdminSidebar\";\nimport AdminFooter from \"../partials/AdminFooter\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLayout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AdminNavber, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"flex bg-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-11/12 h-full\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "Fragment", "AdminNavber", "AdminSidebar", "<PERSON><PERSON><PERSON><PERSON>er", "jsxDEV", "_jsxDEV", "AdminLayout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/layout/index.js"], "sourcesContent": ["import React, { Fragment } from \"react\";\r\n\r\nimport AdminNavber from \"../partials/AdminNavber\";\r\nimport AdminSidebar from \"../partials/AdminSidebar\";\r\nimport AdminFooter from \"../partials/AdminFooter\";\r\n\r\nconst AdminLayout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <AdminNavber />\r\n      <section className=\"flex bg-gray-100\">\r\n        <AdminSidebar />\r\n        <div className=\"w-full md:w-11/12 h-full\">\r\n          {/* All Children pass from here */}\r\n          {children}\r\n        </div>\r\n      </section>\r\n      <AdminFooter />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default AdminLayout;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACpC,oBACEF,OAAA,CAACL,QAAQ;IAAAO,QAAA,gBACPF,OAAA,CAACJ,WAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfN,OAAA;MAASO,SAAS,EAAC,kBAAkB;MAAAL,QAAA,gBACnCF,OAAA,CAACH,YAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBN,OAAA;QAAKO,SAAS,EAAC,0BAA0B;QAAAL,QAAA,EAEtCA;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACVN,OAAA,CAACF,WAAW;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEf,CAAC;AAACE,EAAA,GAdIP,WAAW;AAgBjB,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}