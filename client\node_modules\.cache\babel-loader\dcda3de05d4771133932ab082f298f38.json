{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\productDetails\\\\Action.js\";\nimport React from \"react\";\nimport { postAddReview, postDeleteReview } from \"./FetchApi\";\nimport { isAuthenticate } from \"../auth/fetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Alert = (color, text) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `bg-${color}-200 px-4 py-2 my-2 rounded`,\n  children: text\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this);\n_c = Alert;\nexport const reviewSubmitHanlder = (fData, setFdata, fetchData) => {\n  if (!fData.rating || !fData.review) {\n    setFdata({\n      ...fData,\n      error: \"Rating and review must be required\"\n    });\n  } else if (!isAuthenticate()) {\n    setFdata({\n      ...fData,\n      error: \"You must need login to review\"\n    });\n  } else {\n    addReview(fData, setFdata, fetchData);\n  }\n};\nexport const deleteReview = async (reviewId, productId, fetchData, setFdata) => {\n  try {\n    let responseData = await postDeleteReview({\n      rId: reviewId,\n      pId: productId\n    });\n    if (responseData.success) {\n      fetchData();\n      setFdata({\n        success: responseData.success\n      });\n    } else if (responseData.error) {\n      fetchData();\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const addReview = async (fData, setFdata, fetchData) => {\n  let formData = {\n    rating: fData.rating,\n    review: fData.review,\n    pId: fData.pId,\n    uId: JSON.parse(localStorage.getItem(\"jwt\")).user._id\n  };\n  try {\n    let responseData = await postAddReview(formData);\n    if (responseData.success) {\n      setFdata({\n        ...fData,\n        success: responseData.success,\n        review: \"\",\n        rating: \"\"\n      });\n      fetchData();\n    } else if (responseData.error) {\n      setFdata({\n        ...fData,\n        error: responseData.error,\n        review: \"\",\n        rating: \"\"\n      });\n      fetchData();\n    }\n  } catch (error) {\n    console.log(error);\n  }\n};\nvar _c;\n$RefreshReg$(_c, \"Alert\");", "map": {"version": 3, "names": ["React", "postAddReview", "postDeleteReview", "isAuthenticate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "color", "text", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "reviewSubmitHanlder", "fData", "setFdata", "fetchData", "rating", "review", "error", "add<PERSON>eview", "deleteReview", "reviewId", "productId", "responseData", "rId", "pId", "success", "console", "log", "formData", "uId", "JSON", "parse", "localStorage", "getItem", "user", "_id", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/Action.js"], "sourcesContent": ["import React from \"react\";\r\nimport { postAddReview, postDeleteReview } from \"./FetchApi\";\r\nimport { isAuthenticate } from \"../auth/fetchApi\";\r\n\r\nexport const Alert = (color, text) => (\r\n  <div className={`bg-${color}-200 px-4 py-2 my-2 rounded`}>{text}</div>\r\n);\r\n\r\nexport const reviewSubmitHanlder = (fData, setFdata, fetchData) => {\r\n  if (!fData.rating || !fData.review) {\r\n    setFdata({ ...fData, error: \"Rating and review must be required\" });\r\n  } else if (!isAuthenticate()) {\r\n    setFdata({ ...fData, error: \"You must need login to review\" });\r\n  } else {\r\n    addReview(fData, setFdata, fetchData);\r\n  }\r\n};\r\n\r\nexport const deleteReview = async (\r\n  reviewId,\r\n  productId,\r\n  fetchData,\r\n  setFdata\r\n) => {\r\n  try {\r\n    let responseData = await postDeleteReview({\r\n      rId: reviewId,\r\n      pId: productId,\r\n    });\r\n    if (responseData.success) {\r\n      fetchData();\r\n      setFdata({ success: responseData.success });\r\n    } else if (responseData.error) {\r\n      fetchData();\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const addReview = async (fData, setFdata, fetchData) => {\r\n  let formData = {\r\n    rating: fData.rating,\r\n    review: fData.review,\r\n    pId: fData.pId,\r\n    uId: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\r\n  };\r\n  try {\r\n    let responseData = await postAddReview(formData);\r\n    if (responseData.success) {\r\n      setFdata({\r\n        ...fData,\r\n        success: responseData.success,\r\n        review: \"\",\r\n        rating: \"\",\r\n      });\r\n      fetchData();\r\n    } else if (responseData.error) {\r\n      setFdata({ ...fData, error: responseData.error, review: \"\", rating: \"\" });\r\n      fetchData();\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAY;AAC5D,SAASC,cAAc,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,OAAO,MAAMC,KAAK,GAAGA,CAACC,KAAK,EAAEC,IAAI,kBAC/BH,OAAA;EAAKI,SAAS,EAAE,MAAMF,KAAK,6BAA8B;EAAAG,QAAA,EAAEF;AAAI;EAAAG,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CACtE;AAACC,EAAA,GAFWT,KAAK;AAIlB,OAAO,MAAMU,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,KAAK;EACjE,IAAI,CAACF,KAAK,CAACG,MAAM,IAAI,CAACH,KAAK,CAACI,MAAM,EAAE;IAClCH,QAAQ,CAAC;MAAE,GAAGD,KAAK;MAAEK,KAAK,EAAE;IAAqC,CAAC,CAAC;EACrE,CAAC,MAAM,IAAI,CAACnB,cAAc,CAAC,CAAC,EAAE;IAC5Be,QAAQ,CAAC;MAAE,GAAGD,KAAK;MAAEK,KAAK,EAAE;IAAgC,CAAC,CAAC;EAChE,CAAC,MAAM;IACLC,SAAS,CAACN,KAAK,EAAEC,QAAQ,EAAEC,SAAS,CAAC;EACvC;AACF,CAAC;AAED,OAAO,MAAMK,YAAY,GAAG,MAAAA,CAC1BC,QAAQ,EACRC,SAAS,EACTP,SAAS,EACTD,QAAQ,KACL;EACH,IAAI;IACF,IAAIS,YAAY,GAAG,MAAMzB,gBAAgB,CAAC;MACxC0B,GAAG,EAAEH,QAAQ;MACbI,GAAG,EAAEH;IACP,CAAC,CAAC;IACF,IAAIC,YAAY,CAACG,OAAO,EAAE;MACxBX,SAAS,CAAC,CAAC;MACXD,QAAQ,CAAC;QAAEY,OAAO,EAAEH,YAAY,CAACG;MAAQ,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIH,YAAY,CAACL,KAAK,EAAE;MAC7BH,SAAS,CAAC,CAAC;IACb;EACF,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdS,OAAO,CAACC,GAAG,CAACV,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAON,KAAK,EAAEC,QAAQ,EAAEC,SAAS,KAAK;EAC7D,IAAIc,QAAQ,GAAG;IACbb,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;IACpBQ,GAAG,EAAEZ,KAAK,CAACY,GAAG;IACdK,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC;EACpD,CAAC;EACD,IAAI;IACF,IAAIb,YAAY,GAAG,MAAM1B,aAAa,CAACgC,QAAQ,CAAC;IAChD,IAAIN,YAAY,CAACG,OAAO,EAAE;MACxBZ,QAAQ,CAAC;QACP,GAAGD,KAAK;QACRa,OAAO,EAAEH,YAAY,CAACG,OAAO;QAC7BT,MAAM,EAAE,EAAE;QACVD,MAAM,EAAE;MACV,CAAC,CAAC;MACFD,SAAS,CAAC,CAAC;IACb,CAAC,MAAM,IAAIQ,YAAY,CAACL,KAAK,EAAE;MAC7BJ,QAAQ,CAAC;QAAE,GAAGD,KAAK;QAAEK,KAAK,EAAEK,YAAY,CAACL,KAAK;QAAED,MAAM,EAAE,EAAE;QAAED,MAAM,EAAE;MAAG,CAAC,CAAC;MACzED,SAAS,CAAC,CAAC;IACb;EACF,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdS,OAAO,CAACC,GAAG,CAACV,KAAK,CAAC;EACpB;AACF,CAAC;AAAC,IAAAP,EAAA;AAAA0B,YAAA,CAAA1B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}