{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\index.js\";\nimport React from \"react\";\nimport { Home, WishList, ProtectedRoute, AdminProtectedRoute, CartProtectedRoute, PageNotFound, ProductDetails, ProductByCategory, CheckoutPage } from \"./shop\";\nimport { DashboardAdmin, Categories, Products, Orders } from \"./admin\";\nimport { UserProfile, UserOrders, SettingUser } from \"./shop/dashboardUser\";\nimport CreateOrderPage from \"./shop/order/CreateOrderPage\";\nimport TestProducts from \"./TestProducts\";\nimport SimpleTest from \"./SimpleTest\";\nimport SafeLayout from \"./SafeLayout\";\nimport { BrowserRouter as Router, Switch, Route } from \"react-router-dom\";\n\n/* Routing All page will be here */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Routes = props => {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Switch, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/\",\n        component: Home\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/test-products\",\n        component: TestProducts\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/simple-test\",\n        component: SimpleTest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/safe-mode\",\n        render: () => /*#__PURE__*/_jsxDEV(SafeLayout, {\n          children: /*#__PURE__*/_jsxDEV(SimpleTest, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/wish-list\",\n        component: WishList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/products/:id\",\n        component: ProductDetails\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/products/category/:catId\",\n        component: ProductByCategory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartProtectedRoute, {\n        exact: true,\n        path: \"/checkout\",\n        component: CheckoutPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard\",\n        component: DashboardAdmin\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/categories\",\n        component: Categories\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/products\",\n        component: Products\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/orders\",\n        component: Orders\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/profile\",\n        component: UserProfile\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/order\",\n        component: UserOrders\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/setting\",\n        component: SettingUser\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/order/create\",\n        component: CreateOrderPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        component: PageNotFound\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "Home", "WishList", "ProtectedRoute", "AdminProtectedRoute", "CartProtectedRoute", "PageNotFound", "ProductDetails", "ProductByCategory", "CheckoutPage", "DashboardAdmin", "Categories", "Products", "Orders", "UserProfile", "UserOrders", "SettingUser", "CreateOrderPage", "TestProducts", "SimpleTest", "SafeLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Switch", "Route", "jsxDEV", "_jsxDEV", "Routes", "props", "children", "exact", "path", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "render", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  Home,\r\n  WishList,\r\n  ProtectedRoute,\r\n  AdminProtectedRoute,\r\n  CartProtectedRoute,\r\n  PageNotFound,\r\n  ProductDetails,\r\n  ProductByCategory,\r\n  CheckoutPage,\r\n} from \"./shop\";\r\nimport { DashboardAdmin, Categories, Products, Orders } from \"./admin\";\r\nimport { UserProfile, UserOrders, SettingUser } from \"./shop/dashboardUser\";\r\nimport CreateOrderPage from \"./shop/order/CreateOrderPage\";\r\nimport TestProducts from \"./TestProducts\";\r\nimport SimpleTest from \"./SimpleTest\";\r\nimport SafeLayout from \"./SafeLayout\";\r\nimport { BrowserRouter as Router, Switch, Route } from \"react-router-dom\";\r\n\r\n/* Routing All page will be here */\r\nconst Routes = (props) => {\r\n  return (\r\n    <Router>\r\n      <Switch>\r\n        {/* Shop & Public Routes */}\r\n        <Route exact path=\"/\" component={Home} />\r\n        <Route exact path=\"/test-products\" component={TestProducts} />\r\n        <Route exact path=\"/simple-test\" component={SimpleTest} />\r\n        <Route exact path=\"/safe-mode\" render={() => <SafeLayout><SimpleTest /></SafeLayout>} />\r\n        <Route exact path=\"/wish-list\" component={WishList} />\r\n        <Route exact path=\"/products/:id\" component={ProductDetails} />\r\n        <Route\r\n          exact\r\n          path=\"/products/category/:catId\"\r\n          component={ProductByCategory}\r\n        />\r\n        <CartProtectedRoute\r\n          exact={true}\r\n          path=\"/checkout\"\r\n          component={CheckoutPage}\r\n        />\r\n        {/* Shop & Public Routes End */}\r\n\r\n        {/* Admin Routes */}\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard\"\r\n          component={DashboardAdmin}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/categories\"\r\n          component={Categories}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/products\"\r\n          component={Products}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/orders\"\r\n          component={Orders}\r\n        />\r\n        {/* Admin Routes End */}\r\n\r\n        {/* User Dashboard */}\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/profile\"\r\n          component={UserProfile}\r\n        />\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/order\"\r\n          component={UserOrders}\r\n        />\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/setting\"\r\n          component={SettingUser}\r\n        />\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/order/create\"\r\n          component={CreateOrderPage}\r\n        />\r\n        {/* User Dashboard End */}\r\n\r\n        {/* 404 Page */}\r\n        <Route component={PageNotFound} />\r\n      </Switch>\r\n    </Router>\r\n  );\r\n};\r\n\r\nexport default Routes;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,YAAY,QACP,QAAQ;AACf,SAASC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,SAAS;AACtE,SAASC,WAAW,EAAEC,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;AAC3E,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;;AAEzE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAIC,KAAK,IAAK;EACxB,oBACEF,OAAA,CAACJ,MAAM;IAAAO,QAAA,eACLH,OAAA,CAACH,MAAM;MAAAM,QAAA,gBAELH,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,GAAG;QAACC,SAAS,EAAE/B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzCV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,gBAAgB;QAACC,SAAS,EAAEd;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,cAAc;QAACC,SAAS,EAAEb;MAAW;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,YAAY;QAACM,MAAM,EAAEA,CAAA,kBAAMX,OAAA,CAACN,UAAU;UAAAS,QAAA,eAACH,OAAA,CAACP,UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxFV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,YAAY;QAACC,SAAS,EAAE9B;MAAS;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,eAAe;QAACC,SAAS,EAAEzB;MAAe;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DV,OAAA,CAACF,KAAK;QACJM,KAAK;QACLC,IAAI,EAAC,2BAA2B;QAChCC,SAAS,EAAExB;MAAkB;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFV,OAAA,CAACrB,kBAAkB;QACjByB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,WAAW;QAChBC,SAAS,EAAEvB;MAAa;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAIFV,OAAA,CAACtB,mBAAmB;QAClB0B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,kBAAkB;QACvBC,SAAS,EAAEtB;MAAe;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACFV,OAAA,CAACtB,mBAAmB;QAClB0B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,6BAA6B;QAClCC,SAAS,EAAErB;MAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACFV,OAAA,CAACtB,mBAAmB;QAClB0B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,2BAA2B;QAChCC,SAAS,EAAEpB;MAAS;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACFV,OAAA,CAACtB,mBAAmB;QAClB0B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,yBAAyB;QAC9BC,SAAS,EAAEnB;MAAO;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAIFV,OAAA,CAACvB,cAAc;QACb2B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,eAAe;QACpBC,SAAS,EAAElB;MAAY;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFV,OAAA,CAACvB,cAAc;QACb2B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,aAAa;QAClBC,SAAS,EAAEjB;MAAW;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACFV,OAAA,CAACvB,cAAc;QACb2B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,eAAe;QACpBC,SAAS,EAAEhB;MAAY;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFV,OAAA,CAACvB,cAAc;QACb2B,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,oBAAoB;QACzBC,SAAS,EAAEf;MAAgB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAIFV,OAAA,CAACF,KAAK;QAACQ,SAAS,EAAE1B;MAAa;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACE,EAAA,GA1EIX,MAAM;AA4EZ,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}