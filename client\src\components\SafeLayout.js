import React, { Fragment, useState } from "react";

// Layout an toàn không có modal hoặc overlay phức tạp
const SafeLayout = ({ children }) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <Fragment>
      {/* Emergency Test Button */}
      <div 
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 99999,
          background: '#ff0000',
          color: 'white',
          padding: '10px 15px',
          borderRadius: '5px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
        onClick={() => alert('✅ CLICK HOẠT ĐỘNG! Layout an toàn đã được load.')}
      >
        🧪 TEST CLICK
      </div>

      {/* Simple Navigation */}
      <nav style={{
        background: '#2c3e50',
        color: 'white',
        padding: '1rem',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <h1 style={{ margin: 0, fontSize: '1.5rem' }}>
            <a href="/" style={{ color: 'white', textDecoration: 'none' }}>
              🛒 E-commerce (Safe Mode)
            </a>
          </h1>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <a href="/" style={{ color: 'white', textDecoration: 'none' }}>Home</a>
            <a href="/test-products" style={{ color: 'white', textDecoration: 'none' }}>Test Products</a>
            <a href="/simple-test" style={{ color: 'white', textDecoration: 'none' }}>Simple Test</a>
            <a href="/test.html" style={{ color: 'white', textDecoration: 'none' }}>Emergency Test</a>
            
            <button
              onClick={() => setShowMenu(!showMenu)}
              style={{
                background: '#3498db',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {showMenu ? 'Hide Menu' : 'Show Menu'}
            </button>
          </div>
        </div>
        
        {/* Simple dropdown menu */}
        {showMenu && (
          <div style={{
            position: 'absolute',
            top: '100%',
            right: '0',
            background: '#34495e',
            padding: '1rem',
            borderRadius: '0 0 8px 8px',
            minWidth: '200px',
            zIndex: 20
          }}>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Debug Tools:</strong>
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <button
                onClick={() => {
                  console.log('🔧 Running emergency fix...');
                  // Chạy emergency fix
                  document.querySelectorAll('.fixed').forEach(el => {
                    const style = getComputedStyle(el);
                    if (style.zIndex > 30 && !el.textContent.includes('TEST CLICK')) {
                      el.style.display = 'none';
                    }
                  });
                  alert('Emergency fix applied!');
                }}
                style={{
                  background: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  padding: '8px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🚨 Emergency Fix
              </button>
              
              <button
                onClick={() => {
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
                style={{
                  background: '#f39c12',
                  color: 'white',
                  border: 'none',
                  padding: '8px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🔄 Reset & Reload
              </button>
              
              <button
                onClick={() => {
                  console.log('Current overlays:');
                  document.querySelectorAll('.fixed, .absolute').forEach(el => {
                    console.log(el, getComputedStyle(el).zIndex);
                  });
                }}
                style={{
                  background: '#9b59b6',
                  color: 'white',
                  border: 'none',
                  padding: '8px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🔍 Debug Overlays
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main style={{
        minHeight: 'calc(100vh - 200px)',
        padding: '2rem',
        background: '#ecf0f1'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Status Banner */}
          <div style={{
            background: '#2ecc71',
            color: 'white',
            padding: '1rem',
            borderRadius: '8px',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            ✅ <strong>Safe Layout Active</strong> - Tất cả modal và overlay phức tạp đã được tắt. 
            Nếu bạn có thể click nút này và tương tác bình thường, vấn đề đã được giải quyết!
          </div>
          
          {children}
        </div>
      </main>

      {/* Simple Footer */}
      <footer style={{
        background: '#2c3e50',
        color: 'white',
        padding: '2rem',
        textAlign: 'center'
      }}>
        <p>🛒 E-commerce App - Safe Mode (No Complex Overlays)</p>
        <p style={{ fontSize: '0.9rem', opacity: 0.8 }}>
          Nếu trang này hoạt động bình thường, vấn đề overlay đã được giải quyết.
        </p>
      </footer>
    </Fragment>
  );
};

export default SafeLayout;
