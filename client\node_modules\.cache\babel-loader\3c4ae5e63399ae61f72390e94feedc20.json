{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, createContext, useReducer } from \"react\";\nimport Layout from \"../layout\";\nimport Slider from \"./Slider\";\nimport ProductCategory from \"./ProductCategory\";\nimport { homeState, homeReducer } from \"./HomeContext\";\nimport SingleProduct from \"./SingleProduct\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const HomeContext = /*#__PURE__*/createContext();\nconst HomeComponent = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Slider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-8 md:my-6\",\n      children: /*#__PURE__*/_jsxDEV(ProductCategory, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-8 md:my-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\",\n      children: /*#__PURE__*/_jsxDEV(SingleProduct, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = HomeComponent;\nconst Home = props => {\n  _s();\n  const [data, dispatch] = useReducer(homeReducer, homeState);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(HomeContext.Provider, {\n      value: {\n        data,\n        dispatch\n      },\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(HomeComponent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 27\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"ncxBYy9l1aoYHQSdugG6KTCXYX0=\");\n_c2 = Home;\nexport default Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomeComponent\");\n$RefreshReg$(_c2, \"Home\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "useReducer", "Layout", "Slide<PERSON>", "ProductCategory", "homeState", "homeReducer", "SingleProduct", "jsxDEV", "_jsxDEV", "HomeContext", "HomeComponent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "_c", "Home", "props", "_s", "data", "dispatch", "Provider", "value", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["import React, { Fragment, createContext, useReducer } from \"react\";\r\nimport Layout from \"../layout\";\r\nimport Slider from \"./Slider\";\r\nimport ProductCategory from \"./ProductCategory\";\r\nimport { homeState, homeReducer } from \"./HomeContext\";\r\nimport SingleProduct from \"./SingleProduct\";\r\n\r\nexport const HomeContext = createContext();\r\n\r\nconst HomeComponent = () => {\r\n  return (\r\n    <Fragment>\r\n      <Slider />\r\n      {/* Category, Search & Filter Section */}\r\n      <section className=\"m-4 md:mx-8 md:my-6\">\r\n        <ProductCategory />\r\n      </section>\r\n      {/* Product Section */}\r\n      <section className=\"m-4 md:mx-8 md:my-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\r\n        <SingleProduct />\r\n      </section>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst Home = (props) => {\r\n  const [data, dispatch] = useReducer(homeReducer, homeState);\r\n  return (\r\n    <Fragment>\r\n      <HomeContext.Provider value={{ data, dispatch }}>\r\n        <Layout children={<HomeComponent />} />\r\n      </HomeContext.Provider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,SAAS,EAAEC,WAAW,QAAQ,eAAe;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,OAAO,MAAMC,WAAW,gBAAGV,aAAa,CAAC,CAAC;AAE1C,MAAMW,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEF,OAAA,CAACV,QAAQ;IAAAa,QAAA,gBACPH,OAAA,CAACN,MAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVP,OAAA;MAASQ,SAAS,EAAC,qBAAqB;MAAAL,QAAA,eACtCH,OAAA,CAACL,eAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEVP,OAAA;MAASQ,SAAS,EAAC,oEAAoE;MAAAL,QAAA,eACrFH,OAAA,CAACF,aAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACE,EAAA,GAdIP,aAAa;AAgBnB,MAAMQ,IAAI,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAGtB,UAAU,CAACK,WAAW,EAAED,SAAS,CAAC;EAC3D,oBACEI,OAAA,CAACV,QAAQ;IAAAa,QAAA,eACPH,OAAA,CAACC,WAAW,CAACc,QAAQ;MAACC,KAAK,EAAE;QAAEH,IAAI;QAAEC;MAAS,CAAE;MAAAX,QAAA,eAC9CH,OAAA,CAACP,MAAM;QAACU,QAAQ,eAAEH,OAAA,CAACE,aAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEf,CAAC;AAACK,EAAA,CATIF,IAAI;AAAAO,GAAA,GAAJP,IAAI;AAWV,eAAeA,IAAI;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}