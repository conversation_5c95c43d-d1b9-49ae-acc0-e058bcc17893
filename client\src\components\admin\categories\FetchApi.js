import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

const BearerToken = () =>
  localStorage.getItem("jwt")
    ? JSON.parse(localStorage.getItem("jwt")).token
    : false;
const Headers = () => {
  return {
    headers: {
      token: `Bearer ${BearerToken()}`,
    },
  };
};

export const getAllCategory = async () => {
  try {
    // Backend chưa có CategoryController, sử dụng mock data
    const mockCategories = [
      { _id: 'electronics', cName: 'Electronics', cImage: 'electronics.jpg', cDescription: 'Electronic devices and gadgets', cStatus: 'Active' },
      { _id: 'fashion', cName: 'Fashion', cImage: 'fashion.jpg', cDescription: 'Clothing and accessories', cStatus: 'Active' },
      { _id: 'beauty', cName: 'Beauty', cImage: 'beauty.jpg', cDescription: 'Beauty and cosmetic products', cStatus: 'Active' },
      { _id: 'furniture', cName: 'Furniture', cImage: 'furniture.jpg', cDescription: 'Home and office furniture', cStatus: 'Active' },
      { _id: 'beverages', cName: 'Beverages', cImage: 'beverages.jpg', cDescription: 'Drinks and beverages', cStatus: 'Active' },
      { _id: 'food', cName: 'Food', cImage: 'food.jpg', cDescription: 'Food products', cStatus: 'Active' },
      { _id: 'household', cName: 'Household', cImage: 'household.jpg', cDescription: 'Household items', cStatus: 'Active' },
      { _id: 'toys', cName: 'Toys', cImage: 'toys.jpg', cDescription: 'Toys and games', cStatus: 'Active' },
      { _id: 'media', cName: 'Media', cImage: 'media.jpg', cDescription: 'Books, CDs, and media', cStatus: 'Active' }
    ];

    return { Categories: mockCategories };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const createCategory = async ({
  cName,
  cImage,
  cDescription,
  cStatus,
}) => {
  let formData = new FormData();
  formData.append("cImage", cImage);
  formData.append("cName", cName);
  formData.append("cDescription", cDescription);
  formData.append("cStatus", cStatus);

  try {
    let res = await axios.post(
      `${apiURL}/api/category/add-category`,
      formData,
      Headers()
    );
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const editCategory = async (cId, des, status) => {
  let data = { cId: cId, cDescription: des, cStatus: status };
  try {
    let res = await axios.post(
      `${apiURL}/api/category/edit-category`,
      data,
      Headers()
    );
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const deleteCategory = async (cId) => {
  try {
    let res = await axios.post(
      `${apiURL}/api/category/delete-category`,
      { cId },
      Headers()
    );
    return res.data;
  } catch (error) {
    console.log(error);
  }
};
