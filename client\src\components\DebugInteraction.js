import React, { useState, useContext } from "react";
import { LayoutContext } from "./shop";

const DebugInteraction = () => {
  const [clickCount, setClickCount] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { data, dispatch } = useContext(LayoutContext);

  const handleClick = () => {
    setClickCount(prev => prev + 1);
    console.log("✅ Click event working! Count:", clickCount + 1);
  };

  const handleMouseMove = (e) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  const testModalToggle = () => {
    console.log("🔄 Testing modal toggle");
    dispatch({ type: "loginSignupModalToggle", payload: !data.loginSignupModal });
  };

  const testCartToggle = () => {
    console.log("🛒 Testing cart toggle");
    dispatch({ type: "cartModalToggle", payload: !data.cartModal });
  };

  return (
    <div 
      className="fixed top-4 right-4 bg-white border-2 border-red-500 p-4 rounded-lg shadow-lg z-50 max-w-sm"
      onMouseMove={handleMouseMove}
    >
      <h3 className="text-lg font-bold text-red-600 mb-4">🔧 Debug Panel</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Click Test:</strong>
          <button 
            onClick={handleClick}
            className="ml-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Click Me ({clickCount})
          </button>
        </div>
        
        <div>
          <strong>Mouse:</strong> X: {mousePosition.x}, Y: {mousePosition.y}
        </div>
        
        <div>
          <strong>Layout State:</strong>
          <ul className="text-xs mt-1">
            <li>Login Modal: {data.loginSignupModal ? "OPEN" : "CLOSED"}</li>
            <li>Cart Modal: {data.cartModal ? "OPEN" : "CLOSED"}</li>
            <li>Hamburger: {data.navberHamburger ? "OPEN" : "CLOSED"}</li>
          </ul>
        </div>
        
        <div className="space-y-1">
          <button 
            onClick={testModalToggle}
            className="block w-full px-2 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600"
          >
            Toggle Login Modal
          </button>
          <button 
            onClick={testCartToggle}
            className="block w-full px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
          >
            Toggle Cart Modal
          </button>
        </div>
        
        <div className="text-xs text-gray-600 mt-2">
          If buttons don't work, there's an overlay issue!
        </div>
      </div>
    </div>
  );
};

export default DebugInteraction;
