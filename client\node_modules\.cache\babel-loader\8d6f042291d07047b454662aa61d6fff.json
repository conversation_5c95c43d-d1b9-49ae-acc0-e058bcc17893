{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getBrainTreeToken = async () => {\n  let uId = JSON.parse(localStorage.getItem(\"jwt\")).user._id;\n  try {\n    let res = await axios.post(`${apiURL}/api/braintree/get-token`, {\n      uId: uId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const getPaymentProcess = async paymentData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/braintree/payment`, paymentData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createOrder = async orderData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/order/create-order`, orderData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getBrainTreeToken", "uId", "JSON", "parse", "localStorage", "getItem", "user", "_id", "res", "post", "data", "error", "console", "log", "getPaymentProcess", "paymentData", "createOrder", "orderData"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getBrainTreeToken = async () => {\r\n  let uId = JSON.parse(localStorage.getItem(\"jwt\")).user._id;\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/braintree/get-token`, {\r\n      uId: uId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getPaymentProcess = async (paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/braintree/payment`, paymentData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createOrder = async (orderData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/order/create-order`, orderData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG;EAC1D,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,0BAA0B,EAAE;MAC9DK,GAAG,EAAEA;IACP,CAAC,CAAC;IACF,OAAOO,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAG,MAAOC,WAAW,IAAK;EACtD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,wBAAwB,EAAEmB,WAAW,CAAC;IAC1E,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAOC,SAAS,IAAK;EAC9C,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,yBAAyB,EAAEqB,SAAS,CAAC;IACzE,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}