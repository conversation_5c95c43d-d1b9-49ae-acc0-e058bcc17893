import React, { useState, useEffect } from "react";
import { getAllProduct } from "./admin/products/FetchApi";
import { checkBackendHealth, logProductSample } from "../utils/checkBackend";

const TestProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      console.log("🔍 Checking backend and fetching products...");

      // Check backend health first
      const healthCheck = await checkBackendHealth();
      if (!healthCheck.isRunning) {
        setError(`Backend not available: ${healthCheck.error}`);
        return;
      }

      const data = await getAllProduct();
      console.log("Raw API response:", data);

      if (Array.isArray(data)) {
        setProducts(data);
        logProductSample(data);
        console.log(`✅ Successfully loaded ${data.length} products`);
      } else {
        console.log("❌ API response is not an array:", data);
        setError("Invalid response format");
      }
    } catch (err) {
      console.error("❌ Error fetching products:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="text-xl">Loading products...</div>
        <div className="mt-4">
          <svg className="w-8 h-8 animate-spin mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center text-red-600">
        <div className="text-xl">Error: {error}</div>
        <button 
          onClick={fetchProducts}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6">Test Products Display</h1>
      <div className="mb-4 text-gray-600">
        Found {products.length} products
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product, index) => (
          <div key={index} className="border rounded-lg p-4 shadow-md">
            <div className="mb-2">
              <img 
                src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.jpg'}
                alt={product.name || 'Product'}
                className="w-full h-48 object-cover rounded"
                onError={(e) => {
                  e.target.src = '/placeholder-image.jpg';
                }}
              />
            </div>
            <h3 className="font-semibold text-lg mb-2">{product.name || 'No name'}</h3>
            <p className="text-gray-600 text-sm mb-2">{product.description || 'No description'}</p>
            <div className="text-lg font-bold text-green-600 mb-2">
              {product.price ? `${product.price.toLocaleString('vi-VN')} VND` : 'Price not available'}
            </div>
            <div className="text-sm text-gray-500">
              <div>ID: {product.productId}</div>
              <div>Category: {product.category || 'No category'}</div>
              <div>Stock: {product.stockQuantity || 'N/A'}</div>
            </div>
          </div>
        ))}
      </div>
      
      {products.length === 0 && (
        <div className="text-center text-gray-500 mt-8">
          No products found. Make sure the backend is running and has sample data.
        </div>
      )}
    </div>
  );
};

export default TestProducts;
