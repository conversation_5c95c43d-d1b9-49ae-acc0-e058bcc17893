{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from '../useThemeProps';\nimport useTheme from '../useThemeWithoutDefault';\n\n/**\n * @deprecated Not used internally. Use `MediaQueryListEvent` from lib.dom.d.ts instead.\n */\n\n/**\n * @deprecated Not used internally. Use `MediaQueryList` from lib.dom.d.ts instead.\n */\n\n/**\n * @deprecated Not used internally. Use `(event: MediaQueryListEvent) => void` instead.\n */\n\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    let active = true;\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      // Workaround Safari wrong implementation of matchMedia\n      // TODO can we remove it?\n      // https://github.com/mui/material-ui/pull/17315#issuecomment-528286677\n      if (active) {\n        setMatch(queryList.matches);\n      }\n    };\n    updateMatch();\n    // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n    queryList.addListener(updateMatch);\n    return () => {\n      active = false;\n      queryList.removeListener(updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// eslint-disable-next-line no-useless-concat -- Workaround for https://github.com/webpack/webpack/issues/14814\nconst maybeReactUseSyncExternalStore = React['useSyncExternalStore' + ''];\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n      mediaQueryList.addListener(notify);\n      return () => {\n        mediaQueryList.removeListener(notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\nexport default function useMediaQuery(queryInput, options = {}) {\n  const theme = useTheme();\n  // Wait for jsdom to support the match media feature.\n  // All the browsers MUI support have this built-in.\n  // This defensive check is here for simplicity.\n  // Most of the time, the match media logic isn't central to people tests.\n  const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n  const {\n    defaultMatches = false,\n    matchMedia = supportMatchMedia ? window.matchMedia : null,\n    ssrMatchMedia = null,\n    noSsr = false\n  } = getThemeProps({\n    name: 'MuiUseMediaQuery',\n    props: options,\n    theme\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof queryInput === 'function' && theme === null) {\n      console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n    }\n  }\n  let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n  query = query.replace(/^@media( ?)/m, '');\n\n  // TODO: Drop `useMediaQueryOld` and use  `use-sync-external-store` shim in `useMediaQueryNew` once the package is stable\n  const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n  const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue({\n      query,\n      match\n    });\n  }\n  return match;\n}", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "getThemeProps", "useTheme", "useMediaQueryOld", "query", "defaultMatches", "matchMedia", "ssrMatchMedia", "noSsr", "match", "setMatch", "useState", "matches", "active", "undefined", "queryList", "updateMatch", "addListener", "removeListener", "maybeReactUseSyncExternalStore", "useMediaQueryNew", "getDefaultSnapshot", "useCallback", "getServerSnapshot", "useMemo", "getSnapshot", "subscribe", "mediaQueryList", "notify", "useMediaQuery", "queryInput", "options", "theme", "supportMatchMedia", "window", "name", "props", "process", "env", "NODE_ENV", "console", "error", "join", "replace", "useMediaQueryImplementation", "useDebugValue"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from '../useThemeProps';\nimport useTheme from '../useThemeWithoutDefault';\n\n/**\n * @deprecated Not used internally. Use `MediaQueryListEvent` from lib.dom.d.ts instead.\n */\n\n/**\n * @deprecated Not used internally. Use `MediaQueryList` from lib.dom.d.ts instead.\n */\n\n/**\n * @deprecated Not used internally. Use `(event: MediaQueryListEvent) => void` instead.\n */\n\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    let active = true;\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      // Workaround Safari wrong implementation of matchMedia\n      // TODO can we remove it?\n      // https://github.com/mui/material-ui/pull/17315#issuecomment-528286677\n      if (active) {\n        setMatch(queryList.matches);\n      }\n    };\n    updateMatch();\n    // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n    queryList.addListener(updateMatch);\n    return () => {\n      active = false;\n      queryList.removeListener(updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// eslint-disable-next-line no-useless-concat -- Workaround for https://github.com/webpack/webpack/issues/14814\nconst maybeReactUseSyncExternalStore = React['useSyncExternalStore' + ''];\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n      mediaQueryList.addListener(notify);\n      return () => {\n        mediaQueryList.removeListener(notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\nexport default function useMediaQuery(queryInput, options = {}) {\n  const theme = useTheme();\n  // Wait for jsdom to support the match media feature.\n  // All the browsers MUI support have this built-in.\n  // This defensive check is here for simplicity.\n  // Most of the time, the match media logic isn't central to people tests.\n  const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n  const {\n    defaultMatches = false,\n    matchMedia = supportMatchMedia ? window.matchMedia : null,\n    ssrMatchMedia = null,\n    noSsr = false\n  } = getThemeProps({\n    name: 'MuiUseMediaQuery',\n    props: options,\n    theme\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof queryInput === 'function' && theme === null) {\n      console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n    }\n  }\n  let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n  query = query.replace(/^@media( ?)/m, '');\n\n  // TODO: Drop `useMediaQueryOld` and use  `use-sync-external-store` shim in `useMediaQueryNew` once the package is stable\n  const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n  const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue({\n      query,\n      match\n    });\n  }\n  return match;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,QAAQ,MAAM,2BAA2B;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAE;EACjF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAAC,MAAM;IAC7C,IAAIH,KAAK,IAAIF,UAAU,EAAE;MACvB,OAAOA,UAAU,CAACF,KAAK,CAAC,CAACQ,OAAO;IAClC;IACA,IAAIL,aAAa,EAAE;MACjB,OAAOA,aAAa,CAACH,KAAK,CAAC,CAACQ,OAAO;IACrC;;IAEA;IACA;IACA,OAAOP,cAAc;EACvB,CAAC,CAAC;EACFL,iBAAiB,CAAC,MAAM;IACtB,IAAIa,MAAM,GAAG,IAAI;IACjB,IAAI,CAACP,UAAU,EAAE;MACf,OAAOQ,SAAS;IAClB;IACA,MAAMC,SAAS,GAAGT,UAAU,CAACF,KAAK,CAAC;IACnC,MAAMY,WAAW,GAAGA,CAAA,KAAM;MACxB;MACA;MACA;MACA,IAAIH,MAAM,EAAE;QACVH,QAAQ,CAACK,SAAS,CAACH,OAAO,CAAC;MAC7B;IACF,CAAC;IACDI,WAAW,CAAC,CAAC;IACb;IACAD,SAAS,CAACE,WAAW,CAACD,WAAW,CAAC;IAClC,OAAO,MAAM;MACXH,MAAM,GAAG,KAAK;MACdE,SAAS,CAACG,cAAc,CAACF,WAAW,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACZ,KAAK,EAAEE,UAAU,CAAC,CAAC;EACvB,OAAOG,KAAK;AACd;;AAEA;AACA,MAAMU,8BAA8B,GAAGpB,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC;AACzE,SAASqB,gBAAgBA,CAAChB,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAE;EACjF,MAAMa,kBAAkB,GAAGtB,KAAK,CAACuB,WAAW,CAAC,MAAMjB,cAAc,EAAE,CAACA,cAAc,CAAC,CAAC;EACpF,MAAMkB,iBAAiB,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAC5C,IAAIhB,KAAK,IAAIF,UAAU,EAAE;MACvB,OAAO,MAAMA,UAAU,CAACF,KAAK,CAAC,CAACQ,OAAO;IACxC;IACA,IAAIL,aAAa,KAAK,IAAI,EAAE;MAC1B,MAAM;QACJK;MACF,CAAC,GAAGL,aAAa,CAACH,KAAK,CAAC;MACxB,OAAO,MAAMQ,OAAO;IACtB;IACA,OAAOS,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAEjB,KAAK,EAAEG,aAAa,EAAEC,KAAK,EAAEF,UAAU,CAAC,CAAC;EACjE,MAAM,CAACmB,WAAW,EAAEC,SAAS,CAAC,GAAG3B,KAAK,CAACyB,OAAO,CAAC,MAAM;IACnD,IAAIlB,UAAU,KAAK,IAAI,EAAE;MACvB,OAAO,CAACe,kBAAkB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC;IAC7C;IACA,MAAMM,cAAc,GAAGrB,UAAU,CAACF,KAAK,CAAC;IACxC,OAAO,CAAC,MAAMuB,cAAc,CAACf,OAAO,EAAEgB,MAAM,IAAI;MAC9C;MACAD,cAAc,CAACV,WAAW,CAACW,MAAM,CAAC;MAClC,OAAO,MAAM;QACXD,cAAc,CAACT,cAAc,CAACU,MAAM,CAAC;MACvC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,kBAAkB,EAAEf,UAAU,EAAEF,KAAK,CAAC,CAAC;EAC3C,MAAMK,KAAK,GAAGU,8BAA8B,CAACO,SAAS,EAAED,WAAW,EAAEF,iBAAiB,CAAC;EACvF,OAAOd,KAAK;AACd;AACA,eAAe,SAASoB,aAAaA,CAACC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9D,MAAMC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;EACxB;EACA;EACA;EACA;EACA,MAAM+B,iBAAiB,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAAC5B,UAAU,KAAK,WAAW;EACnG,MAAM;IACJD,cAAc,GAAG,KAAK;IACtBC,UAAU,GAAG2B,iBAAiB,GAAGC,MAAM,CAAC5B,UAAU,GAAG,IAAI;IACzDC,aAAa,GAAG,IAAI;IACpBC,KAAK,GAAG;EACV,CAAC,GAAGP,aAAa,CAAC;IAChBkC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAEL,OAAO;IACdC;EACF,CAAC,CAAC;EACF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,OAAOT,UAAU,KAAK,UAAU,IAAIE,KAAK,KAAK,IAAI,EAAE;MACtDQ,OAAO,CAACC,KAAK,CAAC,CAAC,gDAAgD,EAAE,8DAA8D,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1M;EACF;EACA,IAAItC,KAAK,GAAG,OAAO0B,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACE,KAAK,CAAC,GAAGF,UAAU;EAC7E1B,KAAK,GAAGA,KAAK,CAACuC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;;EAEzC;EACA,MAAMC,2BAA2B,GAAGzB,8BAA8B,KAAKL,SAAS,GAAGM,gBAAgB,GAAGjB,gBAAgB;EACtH,MAAMM,KAAK,GAAGmC,2BAA2B,CAACxC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,CAAC;EAClG,IAAI6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAxC,KAAK,CAAC8C,aAAa,CAAC;MAClBzC,KAAK;MACLK;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}