{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get cart by user ID - Updated to match backend endpoint\nexport const getCartByUser = async userId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Add product to cart - Updated to match backend endpoint\nexport const addToCart = async (userId, productData) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/add`, productData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Update cart item - Updated to match backend endpoint\nexport const updateCartItem = async (userId, cartData) => {\n  try {\n    let res = await axios.put(`${apiURL}/api/v1/cart/${userId}/update`, cartData, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Remove item from cart - Updated to match backend endpoint\nexport const removeFromCart = async (userId, productId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/remove`, {\n      data: {\n        productId\n      },\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Empty cart - Updated to match backend endpoint\nexport const emptyCart = async userId => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/empty`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Legacy function - Updated to use new cart endpoints\nexport const cartListProduct = async () => {\n  console.warn(\"cartListProduct: Using new cart endpoint instead of legacy /api/product/cart-product\");\n  try {\n    var _JSON$parse$user, _JSON$parse$user2;\n    // Get current user ID from localStorage\n    const jwt = localStorage.getItem(\"jwt\");\n    const userId = jwt ? ((_JSON$parse$user = JSON.parse(jwt).user) === null || _JSON$parse$user === void 0 ? void 0 : _JSON$parse$user.id) || ((_JSON$parse$user2 = JSON.parse(jwt).user) === null || _JSON$parse$user2 === void 0 ? void 0 : _JSON$parse$user2._id) : null;\n    if (userId) {\n      // Use new cart endpoint\n      let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${jwt ? JSON.parse(jwt).token : \"\"}`\n        }\n      });\n      if (res.data && res.data.success && res.data.data) {\n        // Map backend cart format to frontend expected format\n        const cartItems = res.data.data.items || [];\n        return {\n          success: true,\n          Products: cartItems.map(item => ({\n            _id: item.productId,\n            pName: item.productName,\n            pPrice: item.productPrice,\n            pImages: [item.productImageUrl || '/placeholder-product.jpg'],\n            quantity: item.quantity\n          }))\n        };\n      }\n    }\n\n    // Fallback to localStorage cart\n    let carts = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    return {\n      success: true,\n      Products: carts\n    };\n  } catch (error) {\n    console.log(\"Error in cartListProduct:\", error);\n    // Fallback to localStorage cart\n    let carts = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    return {\n      success: true,\n      Products: carts\n    };\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getCartByUser", "userId", "res", "get", "data", "error", "console", "log", "addToCart", "productData", "post", "headers", "updateCartItem", "cartData", "put", "removeFromCart", "productId", "delete", "emptyCart", "cartListProduct", "warn", "_JSON$parse$user", "_JSON$parse$user2", "jwt", "localStorage", "getItem", "JSON", "parse", "user", "id", "_id", "token", "success", "cartItems", "items", "Products", "map", "item", "pName", "productName", "pPrice", "productPrice", "pImages", "productImageUrl", "quantity", "carts"], "sources": ["D:/ITSS_Reference/client/src/components/shop/partials/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// Get cart by user ID - Updated to match backend endpoint\r\nexport const getCartByUser = async (userId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Add product to cart - Updated to match backend endpoint\r\nexport const addToCart = async (userId, productData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/cart/${userId}/add`, productData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update cart item - Updated to match backend endpoint\r\nexport const updateCartItem = async (userId, cartData) => {\r\n  try {\r\n    let res = await axios.put(`${apiURL}/api/v1/cart/${userId}/update`, cartData, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Remove item from cart - Updated to match backend endpoint\r\nexport const removeFromCart = async (userId, productId) => {\r\n  try {\r\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/remove`, {\r\n      data: { productId },\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Empty cart - Updated to match backend endpoint\r\nexport const emptyCart = async (userId) => {\r\n  try {\r\n    let res = await axios.delete(`${apiURL}/api/v1/cart/${userId}/empty`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Legacy function - Updated to use new cart endpoints\r\nexport const cartListProduct = async () => {\r\n  console.warn(\"cartListProduct: Using new cart endpoint instead of legacy /api/product/cart-product\");\r\n  try {\r\n    // Get current user ID from localStorage\r\n    const jwt = localStorage.getItem(\"jwt\");\r\n    const userId = jwt ? JSON.parse(jwt).user?.id || JSON.parse(jwt).user?._id : null;\r\n\r\n    if (userId) {\r\n      // Use new cart endpoint\r\n      let res = await axios.get(`${apiURL}/api/v1/cart/${userId}`, {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${jwt ? JSON.parse(jwt).token : \"\"}`\r\n        }\r\n      });\r\n\r\n      if (res.data && res.data.success && res.data.data) {\r\n        // Map backend cart format to frontend expected format\r\n        const cartItems = res.data.data.items || [];\r\n        return {\r\n          success: true,\r\n          Products: cartItems.map(item => ({\r\n            _id: item.productId,\r\n            pName: item.productName,\r\n            pPrice: item.productPrice,\r\n            pImages: [item.productImageUrl || '/placeholder-product.jpg'],\r\n            quantity: item.quantity\r\n          }))\r\n        };\r\n      }\r\n    }\r\n\r\n    // Fallback to localStorage cart\r\n    let carts = JSON.parse(localStorage.getItem(\"cart\")) || [];\r\n    return {\r\n      success: true,\r\n      Products: carts\r\n    };\r\n  } catch (error) {\r\n    console.log(\"Error in cartListProduct:\", error);\r\n    // Fallback to localStorage cart\r\n    let carts = JSON.parse(localStorage.getItem(\"cart\")) || [];\r\n    return {\r\n      success: true,\r\n      Products: carts\r\n    };\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,aAAa,GAAG,MAAOC,MAAM,IAAK;EAC7C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,GAAGP,MAAM,gBAAgBK,MAAM,EAAE,CAAC;IAC5D,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,SAAS,GAAG,MAAAA,CAAOP,MAAM,EAAEQ,WAAW,KAAK;EACtD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACe,IAAI,CAAC,GAAGd,MAAM,gBAAgBK,MAAM,MAAM,EAAEQ,WAAW,EAAE;MAC7EE,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAOX,MAAM,EAAEY,QAAQ,KAAK;EACxD,IAAI;IACF,IAAIX,GAAG,GAAG,MAAMP,KAAK,CAACmB,GAAG,CAAC,GAAGlB,MAAM,gBAAgBK,MAAM,SAAS,EAAEY,QAAQ,EAAE;MAC5EF,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,cAAc,GAAG,MAAAA,CAAOd,MAAM,EAAEe,SAAS,KAAK;EACzD,IAAI;IACF,IAAId,GAAG,GAAG,MAAMP,KAAK,CAACsB,MAAM,CAAC,GAAGrB,MAAM,gBAAgBK,MAAM,SAAS,EAAE;MACrEG,IAAI,EAAE;QAAEY;MAAU,CAAC;MACnBL,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,SAAS,GAAG,MAAOjB,MAAM,IAAK;EACzC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACsB,MAAM,CAAC,GAAGrB,MAAM,gBAAgBK,MAAM,QAAQ,CAAC;IACrE,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;EACzCb,OAAO,CAACc,IAAI,CAAC,sFAAsF,CAAC;EACpG,IAAI;IAAA,IAAAC,gBAAA,EAAAC,iBAAA;IACF;IACA,MAAMC,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;IACvC,MAAMxB,MAAM,GAAGsB,GAAG,GAAG,EAAAF,gBAAA,GAAAK,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,CAACK,IAAI,cAAAP,gBAAA,uBAApBA,gBAAA,CAAsBQ,EAAE,OAAAP,iBAAA,GAAII,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,CAACK,IAAI,cAAAN,iBAAA,uBAApBA,iBAAA,CAAsBQ,GAAG,IAAG,IAAI;IAEjF,IAAI7B,MAAM,EAAE;MACV;MACA,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,GAAGP,MAAM,gBAAgBK,MAAM,EAAE,EAAE;QAC3DU,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUY,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,CAACQ,KAAK,GAAG,EAAE;QAC7D;MACF,CAAC,CAAC;MAEF,IAAI7B,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACE,IAAI,CAAC4B,OAAO,IAAI9B,GAAG,CAACE,IAAI,CAACA,IAAI,EAAE;QACjD;QACA,MAAM6B,SAAS,GAAG/B,GAAG,CAACE,IAAI,CAACA,IAAI,CAAC8B,KAAK,IAAI,EAAE;QAC3C,OAAO;UACLF,OAAO,EAAE,IAAI;UACbG,QAAQ,EAAEF,SAAS,CAACG,GAAG,CAACC,IAAI,KAAK;YAC/BP,GAAG,EAAEO,IAAI,CAACrB,SAAS;YACnBsB,KAAK,EAAED,IAAI,CAACE,WAAW;YACvBC,MAAM,EAAEH,IAAI,CAACI,YAAY;YACzBC,OAAO,EAAE,CAACL,IAAI,CAACM,eAAe,IAAI,0BAA0B,CAAC;YAC7DC,QAAQ,EAAEP,IAAI,CAACO;UACjB,CAAC,CAAC;QACJ,CAAC;MACH;IACF;;IAEA;IACA,IAAIC,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;IAC1D,OAAO;MACLO,OAAO,EAAE,IAAI;MACbG,QAAQ,EAAEU;IACZ,CAAC;EACH,CAAC,CAAC,OAAOxC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,KAAK,CAAC;IAC/C;IACA,IAAIwC,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;IAC1D,OAAO;MACLO,OAAO,EAAE,IAAI;MACbG,QAAQ,EAAEU;IACZ,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}