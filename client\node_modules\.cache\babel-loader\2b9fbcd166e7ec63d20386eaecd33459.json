{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\partials\\\\Footer.js\";\nimport React, { Fragment } from \"react\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"footer\", {\n      style: {\n        background: \"#303031\",\n        color: \"#87898A\"\n      },\n      className: \"z-10 py-6 px-4 md:px-12 text-center\",\n      children: [\"Develop & Design Hasan-py \\xA9 Copyright \", moment().format(\"YYYY\")]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Fragment", "moment", "jsxDEV", "_jsxDEV", "Footer", "props", "children", "style", "background", "color", "className", "format", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/partials/Footer.js"], "sourcesContent": ["import React, { Fragment } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nconst Footer = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <footer\r\n        style={{ background: \"#303031\", color: \"#87898A\" }}\r\n        className=\"z-10 py-6 px-4 md:px-12 text-center\"\r\n      >\r\n        Develop & Design Hasan-py © Copyright {moment().format(\"YYYY\")}\r\n      </footer>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,MAAM,GAAIC,KAAK,IAAK;EACxB,oBACEF,OAAA,CAACH,QAAQ;IAAAM,QAAA,eACPH,OAAA;MACEI,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAU,CAAE;MACnDC,SAAS,EAAC,qCAAqC;MAAAJ,QAAA,GAChD,2CACuC,EAACL,MAAM,CAAC,CAAC,CAACU,MAAM,CAAC,MAAM,CAAC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEf,CAAC;AAACC,EAAA,GAXIZ,MAAM;AAaZ,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}