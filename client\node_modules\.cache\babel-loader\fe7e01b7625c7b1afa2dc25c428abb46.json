{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\index.js\";\nimport React from \"react\";\nimport { Home, WishList, ProtectedRoute, AdminProtectedRoute, CartProtectedRoute, PageNotFound, ProductDetails, ProductByCategory, CheckoutPage } from \"./shop\";\nimport { DashboardAdmin, Categories, Products, Orders } from \"./admin\";\nimport { UserProfile, UserOrders, SettingUser } from \"./shop/dashboardUser\";\nimport ApiTestPage from \"./ApiTestPage\";\nimport DebugProducts from \"./debug/DebugProducts\";\nimport { BrowserRouter as Router, Switch, Route } from \"react-router-dom\";\n\n/* Routing All page will be here */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Routes = props => {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Switch, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/\",\n        component: Home\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/api-test\",\n        component: ApiTestPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/debug-products\",\n        component: DebugProducts\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/wish-list\",\n        component: WishList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/products/:id\",\n        component: ProductDetails\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/products/category/:catId\",\n        component: ProductByCategory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartProtectedRoute, {\n        exact: true,\n        path: \"/checkout\",\n        component: CheckoutPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard\",\n        component: DashboardAdmin\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/categories\",\n        component: Categories\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/products\",\n        component: Products\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n        exact: true,\n        path: \"/admin/dashboard/orders\",\n        component: Orders\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/profile\",\n        component: UserProfile\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/orders\",\n        component: UserOrders\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        exact: true,\n        path: \"/user/setting\",\n        component: SettingUser\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        component: PageNotFound\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "Home", "WishList", "ProtectedRoute", "AdminProtectedRoute", "CartProtectedRoute", "PageNotFound", "ProductDetails", "ProductByCategory", "CheckoutPage", "DashboardAdmin", "Categories", "Products", "Orders", "UserProfile", "UserOrders", "SettingUser", "ApiTestPage", "DebugProducts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Switch", "Route", "jsxDEV", "_jsxDEV", "Routes", "props", "children", "exact", "path", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  Home,\r\n  WishList,\r\n  ProtectedRoute,\r\n  AdminProtectedRoute,\r\n  CartProtectedRoute,\r\n  PageNotFound,\r\n  ProductDetails,\r\n  ProductByCategory,\r\n  CheckoutPage,\r\n} from \"./shop\";\r\nimport { DashboardAdmin, Categories, Products, Orders } from \"./admin\";\r\nimport { UserProfile, UserOrders, SettingUser } from \"./shop/dashboardUser\";\r\nimport ApiTestPage from \"./ApiTestPage\";\r\nimport DebugProducts from \"./debug/DebugProducts\";\r\n\r\nimport { BrowserRouter as Router, Switch, Route } from \"react-router-dom\";\r\n\r\n/* Routing All page will be here */\r\nconst Routes = (props) => {\r\n  return (\r\n    <Router>\r\n      <Switch>\r\n        {/* Shop & Public Routes */}\r\n        <Route exact path=\"/\" component={Home} />\r\n        <Route exact path=\"/api-test\" component={ApiTestPage} />\r\n        <Route exact path=\"/debug-products\" component={DebugProducts} />\r\n        <Route exact path=\"/wish-list\" component={WishList} />\r\n        <Route exact path=\"/products/:id\" component={ProductDetails} />\r\n        <Route\r\n          exact\r\n          path=\"/products/category/:catId\"\r\n          component={ProductByCategory}\r\n        />\r\n        <CartProtectedRoute\r\n          exact={true}\r\n          path=\"/checkout\"\r\n          component={CheckoutPage}\r\n        />\r\n        {/* Shop & Public Routes End */}\r\n\r\n        {/* Admin Routes */}\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard\"\r\n          component={DashboardAdmin}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/categories\"\r\n          component={Categories}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/products\"\r\n          component={Products}\r\n        />\r\n        <AdminProtectedRoute\r\n          exact={true}\r\n          path=\"/admin/dashboard/orders\"\r\n          component={Orders}\r\n        />\r\n        {/* Admin Routes End */}\r\n\r\n        {/* User Dashboard */}\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/profile\"\r\n          component={UserProfile}\r\n        />\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/orders\"\r\n          component={UserOrders}\r\n        />\r\n        <ProtectedRoute\r\n          exact={true}\r\n          path=\"/user/setting\"\r\n          component={SettingUser}\r\n        />\r\n        {/* User Dashboard End */}\r\n\r\n        {/* 404 Page */}\r\n        <Route component={PageNotFound} />\r\n      </Switch>\r\n    </Router>\r\n  );\r\n};\r\n\r\nexport default Routes;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,YAAY,QACP,QAAQ;AACf,SAASC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,SAAS;AACtE,SAASC,WAAW,EAAEC,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;AAC3E,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,uBAAuB;AAEjD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;;AAEzE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAIC,KAAK,IAAK;EACxB,oBACEF,OAAA,CAACJ,MAAM;IAAAO,QAAA,eACLH,OAAA,CAACH,MAAM;MAAAM,QAAA,gBAELH,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,GAAG;QAACC,SAAS,EAAE7B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzCV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,WAAW;QAACC,SAAS,EAAEb;MAAY;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,iBAAiB;QAACC,SAAS,EAAEZ;MAAc;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,YAAY;QAACC,SAAS,EAAE5B;MAAS;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDV,OAAA,CAACF,KAAK;QAACM,KAAK;QAACC,IAAI,EAAC,eAAe;QAACC,SAAS,EAAEvB;MAAe;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DV,OAAA,CAACF,KAAK;QACJM,KAAK;QACLC,IAAI,EAAC,2BAA2B;QAChCC,SAAS,EAAEtB;MAAkB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFV,OAAA,CAACnB,kBAAkB;QACjBuB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,WAAW;QAChBC,SAAS,EAAErB;MAAa;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAIFV,OAAA,CAACpB,mBAAmB;QAClBwB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,kBAAkB;QACvBC,SAAS,EAAEpB;MAAe;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACFV,OAAA,CAACpB,mBAAmB;QAClBwB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,6BAA6B;QAClCC,SAAS,EAAEnB;MAAW;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACFV,OAAA,CAACpB,mBAAmB;QAClBwB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,2BAA2B;QAChCC,SAAS,EAAElB;MAAS;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACFV,OAAA,CAACpB,mBAAmB;QAClBwB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,yBAAyB;QAC9BC,SAAS,EAAEjB;MAAO;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAIFV,OAAA,CAACrB,cAAc;QACbyB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,eAAe;QACpBC,SAAS,EAAEhB;MAAY;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFV,OAAA,CAACrB,cAAc;QACbyB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,cAAc;QACnBC,SAAS,EAAEf;MAAW;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACFV,OAAA,CAACrB,cAAc;QACbyB,KAAK,EAAE,IAAK;QACZC,IAAI,EAAC,eAAe;QACpBC,SAAS,EAAEd;MAAY;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAIFV,OAAA,CAACF,KAAK;QAACQ,SAAS,EAAExB;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACC,EAAA,GApEIV,MAAM;AAsEZ,eAAeA,MAAM;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}