import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const getAllOrders = async (status) => {
  try {
    // Backend chưa có API get all orders, tạm thời return empty array
    // TODO: Implement when backend has order management API
    console.log("⚠️ getAllOrders: Backend chưa có API get all orders");
    return [];
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateOrderStatus = async (orderId, status) => {
  try {
    // Backend chưa có API update order status
    // TODO: Implement when backend has order management API
    console.log("⚠️ updateOrderStatus: Backend chưa có API update order status");
    return { success: false, message: "API chưa có" };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const deleteOrder = async (orderId, userId) => {
  try {
    // Sử dụng cancel order thay vì delete
    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
