{"ast": null, "code": "export const layoutState = {\n  navberHamburger: false,\n  loginSignupModal: false,\n  loginSignupError: false,\n  cartModal: false,\n  cartProduct: null,\n  singleProductDetail: null,\n  inCart: null,\n  cartTotalCost: null,\n  orderSuccess: false,\n  loading: false\n};\nexport const layoutReducer = (state, action) => {\n  switch (action.type) {\n    case \"hamburgerToggle\":\n      return {\n        ...state,\n        navberHamburger: action.payload\n      };\n    case \"loginSignupModalToggle\":\n      return {\n        ...state,\n        loginSignupModal: action.payload\n      };\n    case \"cartModalToggle\":\n      return {\n        ...state,\n        cartModal: action.payload\n      };\n    case \"cartProduct\":\n      return {\n        ...state,\n        cartProduct: action.payload\n      };\n    case \"singleProductDetail\":\n      return {\n        ...state,\n        singleProductDetail: action.payload\n      };\n    case \"inCart\":\n      return {\n        ...state,\n        inCart: action.payload\n      };\n    case \"cartTotalCost\":\n      return {\n        ...state,\n        cartTotalCost: action.payload\n      };\n    case \"loginSignupError\":\n      return {\n        ...state,\n        loginSignupError: action.payload\n      };\n    case \"orderSuccess\":\n      return {\n        ...state,\n        orderSuccess: action.payload\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["layoutState", "navberHamburger", "loginSignupModal", "loginSignupError", "cartModal", "cartProduct", "singleProductDetail", "inCart", "cartTotalCost", "orderSuccess", "loading", "layoutReducer", "state", "action", "type", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/shop/layout/layoutContext.js"], "sourcesContent": ["export const layoutState = {\r\n  navberHamburger: false,\r\n  loginSignupModal: false,\r\n  loginSignupError: false,\r\n  cartModal: false,\r\n  cartProduct: null,\r\n  singleProductDetail: null,\r\n  inCart: null,\r\n  cartTotalCost: null,\r\n  orderSuccess: false,\r\n  loading: false,\r\n};\r\n\r\nexport const layoutReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"hamburgerToggle\":\r\n      return {\r\n        ...state,\r\n        navberHamburger: action.payload,\r\n      };\r\n    case \"loginSignupModalToggle\":\r\n      return {\r\n        ...state,\r\n        loginSignupModal: action.payload,\r\n      };\r\n    case \"cartModalToggle\":\r\n      return {\r\n        ...state,\r\n        cartModal: action.payload,\r\n      };\r\n    case \"cartProduct\":\r\n      return {\r\n        ...state,\r\n        cartProduct: action.payload,\r\n      };\r\n    case \"singleProductDetail\":\r\n      return {\r\n        ...state,\r\n        singleProductDetail: action.payload,\r\n      };\r\n    case \"inCart\":\r\n      return {\r\n        ...state,\r\n        inCart: action.payload,\r\n      };\r\n    case \"cartTotalCost\":\r\n      return {\r\n        ...state,\r\n        cartTotalCost: action.payload,\r\n      };\r\n    case \"loginSignupError\":\r\n      return {\r\n        ...state,\r\n        loginSignupError: action.payload,\r\n      };\r\n    case \"orderSuccess\":\r\n      return {\r\n        ...state,\r\n        orderSuccess: action.payload,\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,eAAe,EAAE,KAAK;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,gBAAgB,EAAE,KAAK;EACvBC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,IAAI;EACjBC,mBAAmB,EAAE,IAAI;EACzBC,MAAM,EAAE,IAAI;EACZC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,KAAK;EACnBC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC9C,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGF,KAAK;QACRX,eAAe,EAAEY,MAAM,CAACE;MAC1B,CAAC;IACH,KAAK,wBAAwB;MAC3B,OAAO;QACL,GAAGH,KAAK;QACRV,gBAAgB,EAAEW,MAAM,CAACE;MAC3B,CAAC;IACH,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGH,KAAK;QACRR,SAAS,EAAES,MAAM,CAACE;MACpB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRP,WAAW,EAAEQ,MAAM,CAACE;MACtB,CAAC;IACH,KAAK,qBAAqB;MACxB,OAAO;QACL,GAAGH,KAAK;QACRN,mBAAmB,EAAEO,MAAM,CAACE;MAC9B,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRL,MAAM,EAAEM,MAAM,CAACE;MACjB,CAAC;IACH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGH,KAAK;QACRJ,aAAa,EAAEK,MAAM,CAACE;MACxB,CAAC;IACH,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGH,KAAK;QACRT,gBAAgB,EAAEU,MAAM,CAACE;MAC3B,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGH,KAAK;QACRH,YAAY,EAAEI,MAAM,CAACE;MACvB,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGH,KAAK;QACRF,OAAO,EAAEG,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}