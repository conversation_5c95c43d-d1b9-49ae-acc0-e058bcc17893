{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getAllProduct = async () => {\n  try {\n    console.log(\"🌐 FetchApi: Making request to:\", `${apiURL}/api/v1/products/customer`);\n    console.log(\"🔧 FetchApi: API URL:\", apiURL);\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\n    console.log(\"📡 FetchApi: Response status:\", res.status);\n    console.log(\"📦 FetchApi: Response data:\", res.data);\n    console.log(\"📊 FetchApi: Data type:\", typeof res.data);\n    console.log(\"📋 FetchApi: Is array:\", Array.isArray(res.data));\n    return res.data;\n  } catch (error) {\n    var _error$response, _error$response2;\n    console.error(\"❌ FetchApi: Error in getAllProduct:\", error);\n    console.error(\"❌ FetchApi: Error message:\", error.message);\n    console.error(\"❌ FetchApi: Error response:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    console.error(\"❌ FetchApi: Error status:\", (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n    throw error;\n  }\n};\nexport const getAllProductForManager = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createPorductImage = async ({\n  pImage\n}) => {\n  /* Most important part for uploading multiple image  */\n  let formData = new FormData();\n  for (const file of pImage) {\n    formData.append(\"pImage\", file);\n  }\n  /* Most important part for uploading multiple image  */\n};\nexport const createProduct = async ({\n  name,\n  description,\n  images,\n  category,\n  stockQuantity,\n  price,\n  weight,\n  rushEligible,\n  barcode,\n  specifications,\n  userId\n}) => {\n  const productData = {\n    name,\n    description,\n    images: images || [],\n    category,\n    price: parseFloat(price),\n    weight: parseFloat(weight) || 0,\n    rushEligible: rushEligible || false,\n    barcode,\n    specifications\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, productData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const editProduct = async product => {\n  console.log(product);\n  /* Most important part for updating multiple image  */\n  let formData = new FormData();\n  if (product.pEditImages) {\n    for (const file of product.pEditImages) {\n      formData.append(\"pEditImages\", file);\n    }\n  }\n  /* Most important part for updating multiple image  */\n  formData.append(\"pId\", product.pId);\n  formData.append(\"pName\", product.pName);\n  formData.append(\"pDescription\", product.pDescription);\n  formData.append(\"pStatus\", product.pStatus);\n  formData.append(\"pCategory\", product.pCategory._id);\n  formData.append(\"pQuantity\", product.pQuantity);\n  formData.append(\"pPrice\", product.pPrice);\n  formData.append(\"pOffer\", product.pOffer);\n  formData.append(\"pImages\", product.pImages);\n  try {\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteProduct = async (productId, userId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/products/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const productByCategory = async catId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\n      catId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const productByPrice = async price => {\n  try {\n    // Tạm thời filter client-side vì backend chưa có API filter by price\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\n    const products = res.data;\n    if (price === \"all\") {\n      return products;\n    }\n\n    // Filter theo price range\n    const priceRanges = {\n      \"0-100\": [0, 100000],\n      \"100-500\": [100000, 500000],\n      \"500-1000\": [500000, 1000000],\n      \"1000+\": [1000000, Infinity]\n    };\n    const range = priceRanges[price];\n    if (range) {\n      return products.filter(product => product.price >= range[0] && product.price < range[1]);\n    }\n    return products;\n  } catch (error) {\n    console.log(error);\n  }\n};\n\n// Thêm API mới cho product details\nexport const getProductDetails = async (productId, userId = 1) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Thêm API search products\nexport const searchProducts = async (keyword, userId = 1) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/search?keyword=${keyword}&userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Thêm API get related products\nexport const getRelatedProducts = async productId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllProduct", "console", "log", "res", "get", "status", "data", "Array", "isArray", "error", "_error$response", "_error$response2", "message", "response", "getAllProductForManager", "createPorductImage", "pImage", "formData", "FormData", "file", "append", "createProduct", "name", "description", "images", "category", "stockQuantity", "price", "weight", "rushEligible", "barcode", "specifications", "userId", "productData", "parseFloat", "post", "editProduct", "product", "pEditImages", "pId", "pName", "pDescription", "pStatus", "pCategory", "_id", "pQuantity", "pPrice", "pOffer", "pImages", "deleteProduct", "productId", "delete", "productByCategory", "catId", "productByPrice", "products", "priceRanges", "Infinity", "range", "filter", "getProductDetails", "searchProducts", "keyword", "getRelatedProducts"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getAllProduct = async () => {\r\n  try {\r\n    console.log(\"🌐 FetchApi: Making request to:\", `${apiURL}/api/v1/products/customer`);\r\n    console.log(\"🔧 FetchApi: API URL:\", apiURL);\r\n\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\r\n    console.log(\"📡 FetchApi: Response status:\", res.status);\r\n    console.log(\"📦 FetchApi: Response data:\", res.data);\r\n    console.log(\"📊 FetchApi: Data type:\", typeof res.data);\r\n    console.log(\"📋 FetchApi: Is array:\", Array.isArray(res.data));\r\n\r\n    return res.data;\r\n  } catch (error) {\r\n    console.error(\"❌ FetchApi: Error in getAllProduct:\", error);\r\n    console.error(\"❌ FetchApi: Error message:\", error.message);\r\n    console.error(\"❌ FetchApi: Error response:\", error.response?.data);\r\n    console.error(\"❌ FetchApi: Error status:\", error.response?.status);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getAllProductForManager = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createPorductImage = async ({ pImage }) => {\r\n  /* Most important part for uploading multiple image  */\r\n  let formData = new FormData();\r\n  for (const file of pImage) {\r\n    formData.append(\"pImage\", file);\r\n  }\r\n  /* Most important part for uploading multiple image  */\r\n};\r\n\r\nexport const createProduct = async ({\r\n  name,\r\n  description,\r\n  images,\r\n  category,\r\n  stockQuantity,\r\n  price,\r\n  weight,\r\n  rushEligible,\r\n  barcode,\r\n  specifications,\r\n  userId\r\n}) => {\r\n  const productData = {\r\n    name,\r\n    description,\r\n    images: images || [],\r\n    category,\r\n    price: parseFloat(price),\r\n    weight: parseFloat(weight) || 0,\r\n    rushEligible: rushEligible || false,\r\n    barcode,\r\n    specifications\r\n  };\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, productData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const editProduct = async (product) => {\r\n  console.log(product);\r\n  /* Most important part for updating multiple image  */\r\n  let formData = new FormData();\r\n  if (product.pEditImages) {\r\n    for (const file of product.pEditImages) {\r\n      formData.append(\"pEditImages\", file);\r\n    }\r\n  }\r\n  /* Most important part for updating multiple image  */\r\n  formData.append(\"pId\", product.pId);\r\n  formData.append(\"pName\", product.pName);\r\n  formData.append(\"pDescription\", product.pDescription);\r\n  formData.append(\"pStatus\", product.pStatus);\r\n  formData.append(\"pCategory\", product.pCategory._id);\r\n  formData.append(\"pQuantity\", product.pQuantity);\r\n  formData.append(\"pPrice\", product.pPrice);\r\n  formData.append(\"pOffer\", product.pOffer);\r\n  formData.append(\"pImages\", product.pImages);\r\n\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/edit-product`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteProduct = async (productId, userId) => {\r\n  try {\r\n    let res = await axios.delete(`${apiURL}/api/v1/products/${productId}?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const productByCategory = async (catId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\r\n      catId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const productByPrice = async (price) => {\r\n  try {\r\n    // Tạm thời filter client-side vì backend chưa có API filter by price\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\r\n    const products = res.data;\r\n\r\n    if (price === \"all\") {\r\n      return products;\r\n    }\r\n\r\n    // Filter theo price range\r\n    const priceRanges = {\r\n      \"0-100\": [0, 100000],\r\n      \"100-500\": [100000, 500000],\r\n      \"500-1000\": [500000, 1000000],\r\n      \"1000+\": [1000000, Infinity]\r\n    };\r\n\r\n    const range = priceRanges[price];\r\n    if (range) {\r\n      return products.filter(product =>\r\n        product.price >= range[0] && product.price < range[1]\r\n      );\r\n    }\r\n\r\n    return products;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\n// Thêm API mới cho product details\r\nexport const getProductDetails = async (productId, userId = 1) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Thêm API search products\r\nexport const searchProducts = async (keyword, userId = 1) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/search?keyword=${keyword}&userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Thêm API get related products\r\nexport const getRelatedProducts = async (productId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,GAAGN,MAAM,2BAA2B,CAAC;IACpFK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEN,MAAM,CAAC;IAE5C,IAAIO,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,2BAA2B,CAAC;IAC/DK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,GAAG,CAACE,MAAM,CAAC;IACxDJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,GAAG,CAACG,IAAI,CAAC;IACpDL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOC,GAAG,CAACG,IAAI,CAAC;IACvDL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEK,KAAK,CAACC,OAAO,CAACL,GAAG,CAACG,IAAI,CAAC,CAAC;IAE9D,OAAOH,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IAAA,IAAAC,eAAA,EAAAC,gBAAA;IACdV,OAAO,CAACQ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC3DR,OAAO,CAACQ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAACG,OAAO,CAAC;IAC1DX,OAAO,CAACQ,KAAK,CAAC,6BAA6B,GAAAC,eAAA,GAAED,KAAK,CAACI,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAgBJ,IAAI,CAAC;IAClEL,OAAO,CAACQ,KAAK,CAAC,2BAA2B,GAAAE,gBAAA,GAAEF,KAAK,CAACI,QAAQ,cAAAF,gBAAA,uBAAdA,gBAAA,CAAgBN,MAAM,CAAC;IAClE,MAAMI,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,uBAAuB,GAAG,MAAAA,CAAA,KAAY;EACjD,IAAI;IACF,IAAIX,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,0BAA0B,CAAC;IAC9D,OAAOO,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMM,kBAAkB,GAAG,MAAAA,CAAO;EAAEC;AAAO,CAAC,KAAK;EACtD;EACA,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;EACjC;EACA;AACF,CAAC;AAED,OAAO,MAAME,aAAa,GAAG,MAAAA,CAAO;EAClCC,IAAI;EACJC,WAAW;EACXC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACbC,KAAK;EACLC,MAAM;EACNC,YAAY;EACZC,OAAO;EACPC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBX,IAAI;IACJC,WAAW;IACXC,MAAM,EAAEA,MAAM,IAAI,EAAE;IACpBC,QAAQ;IACRE,KAAK,EAAEO,UAAU,CAACP,KAAK,CAAC;IACxBC,MAAM,EAAEM,UAAU,CAACN,MAAM,CAAC,IAAI,CAAC;IAC/BC,YAAY,EAAEA,YAAY,IAAI,KAAK;IACnCC,OAAO;IACPC;EACF,CAAC;EAED,IAAI;IACF,IAAI5B,GAAG,GAAG,MAAMR,KAAK,CAACwC,IAAI,CAAC,GAAGvC,MAAM,2BAA2BoC,MAAM,EAAE,EAAEC,WAAW,CAAC;IACrF,OAAO9B,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2B,WAAW,GAAG,MAAOC,OAAO,IAAK;EAC5CpC,OAAO,CAACC,GAAG,CAACmC,OAAO,CAAC;EACpB;EACA,IAAIpB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,IAAImB,OAAO,CAACC,WAAW,EAAE;IACvB,KAAK,MAAMnB,IAAI,IAAIkB,OAAO,CAACC,WAAW,EAAE;MACtCrB,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAED,IAAI,CAAC;IACtC;EACF;EACA;EACAF,QAAQ,CAACG,MAAM,CAAC,KAAK,EAAEiB,OAAO,CAACE,GAAG,CAAC;EACnCtB,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEiB,OAAO,CAACG,KAAK,CAAC;EACvCvB,QAAQ,CAACG,MAAM,CAAC,cAAc,EAAEiB,OAAO,CAACI,YAAY,CAAC;EACrDxB,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEiB,OAAO,CAACK,OAAO,CAAC;EAC3CzB,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEiB,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC;EACnD3B,QAAQ,CAACG,MAAM,CAAC,WAAW,EAAEiB,OAAO,CAACQ,SAAS,CAAC;EAC/C5B,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEiB,OAAO,CAACS,MAAM,CAAC;EACzC7B,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEiB,OAAO,CAACU,MAAM,CAAC;EACzC9B,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEiB,OAAO,CAACW,OAAO,CAAC;EAE3C,IAAI;IACF,IAAI7C,GAAG,GAAG,MAAMR,KAAK,CAACwC,IAAI,CAAC,GAAGvC,MAAM,2BAA2B,EAAEqB,QAAQ,CAAC;IAC1E,OAAOd,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMwC,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAElB,MAAM,KAAK;EACxD,IAAI;IACF,IAAI7B,GAAG,GAAG,MAAMR,KAAK,CAACwD,MAAM,CAAC,GAAGvD,MAAM,oBAAoBsD,SAAS,WAAWlB,MAAM,EAAE,CAAC;IACvF,OAAO7B,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2C,iBAAiB,GAAG,MAAOC,KAAK,IAAK;EAChD,IAAI;IACF,IAAIlD,GAAG,GAAG,MAAMR,KAAK,CAACwC,IAAI,CAAC,GAAGvC,MAAM,kCAAkC,EAAE;MACtEyD;IACF,CAAC,CAAC;IACF,OAAOlD,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAM6C,cAAc,GAAG,MAAO3B,KAAK,IAAK;EAC7C,IAAI;IACF;IACA,IAAIxB,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,2BAA2B,CAAC;IAC/D,MAAM2D,QAAQ,GAAGpD,GAAG,CAACG,IAAI;IAEzB,IAAIqB,KAAK,KAAK,KAAK,EAAE;MACnB,OAAO4B,QAAQ;IACjB;;IAEA;IACA,MAAMC,WAAW,GAAG;MAClB,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;MACpB,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAC3B,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;MAC7B,OAAO,EAAE,CAAC,OAAO,EAAEC,QAAQ;IAC7B,CAAC;IAED,MAAMC,KAAK,GAAGF,WAAW,CAAC7B,KAAK,CAAC;IAChC,IAAI+B,KAAK,EAAE;MACT,OAAOH,QAAQ,CAACI,MAAM,CAACtB,OAAO,IAC5BA,OAAO,CAACV,KAAK,IAAI+B,KAAK,CAAC,CAAC,CAAC,IAAIrB,OAAO,CAACV,KAAK,GAAG+B,KAAK,CAAC,CAAC,CACtD,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMmD,iBAAiB,GAAG,MAAAA,CAAOV,SAAS,EAAElB,MAAM,GAAG,CAAC,KAAK;EAChE,IAAI;IACF,IAAI7B,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,6BAA6BsD,SAAS,WAAWlB,MAAM,EAAE,CAAC;IAC7F,OAAO7B,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMoD,cAAc,GAAG,MAAAA,CAAOC,OAAO,EAAE9B,MAAM,GAAG,CAAC,KAAK;EAC3D,IAAI;IACF,IAAI7B,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,4CAA4CkE,OAAO,WAAW9B,MAAM,EAAE,CAAC;IAC1G,OAAO7B,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMsD,kBAAkB,GAAG,MAAOb,SAAS,IAAK;EACrD,IAAI;IACF,IAAI/C,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,oBAAoBsD,SAAS,UAAU,CAAC;IAC3E,OAAO/C,GAAG,CAACG,IAAI;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}