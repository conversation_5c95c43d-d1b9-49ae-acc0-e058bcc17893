{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n    if ((utils.isBlob(requestData) || utils.isFile(requestData)) && requestData.type) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = unescape(encodeURIComponent(config.auth.password)) || '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    // Listen for ready state\n    request.onreadystatechange = function handleLoad() {\n      if (!request || request.readyState !== 4) {\n        return;\n      }\n\n      // The request errored out and we didn't get a response, this will be\n      // handled by onerror instead\n      // With one exception: request that using file: protocol, most browsers\n      // will return status as 0 even though it's a successful request\n      if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n        return;\n      }\n\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(timeoutErrorMessage, config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ? cookies.read(config.xsrfCookieName) : undefined;\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (config.responseType) {\n      try {\n        request.responseType = config.responseType;\n      } catch (e) {\n        // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.\n        // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.\n        if (config.responseType !== 'json') {\n          throw e;\n        }\n      }\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};", "map": {"version": 3, "names": ["utils", "require", "settle", "cookies", "buildURL", "buildFullPath", "parseHeaders", "isURLSameOrigin", "createError", "module", "exports", "xhrAdapter", "config", "Promise", "dispatchXhrRequest", "resolve", "reject", "requestData", "data", "requestHeaders", "headers", "isFormData", "isBlob", "isFile", "type", "request", "XMLHttpRequest", "auth", "username", "password", "unescape", "encodeURIComponent", "Authorization", "btoa", "fullPath", "baseURL", "url", "open", "method", "toUpperCase", "params", "paramsSerializer", "timeout", "onreadystatechange", "handleLoad", "readyState", "status", "responseURL", "indexOf", "responseHeaders", "getAllResponseHeaders", "responseData", "responseType", "responseText", "response", "statusText", "<PERSON>ab<PERSON>", "handleAbort", "onerror", "handleError", "ontimeout", "handleTimeout", "timeoutErrorMessage", "isStandardBrowserEnv", "xsrfValue", "withCredentials", "xsrfCookieName", "read", "undefined", "xsrfHeaderName", "for<PERSON>ach", "setRequestHeader", "val", "key", "toLowerCase", "isUndefined", "e", "onDownloadProgress", "addEventListener", "onUploadProgress", "upload", "cancelToken", "promise", "then", "onCanceled", "cancel", "abort", "send"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/adapters/xhr.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\n\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    if (\n      (utils.isBlob(requestData) || utils.isFile(requestData)) &&\n      requestData.type\n    ) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = unescape(encodeURIComponent(config.auth.password)) || '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    // Listen for ready state\n    request.onreadystatechange = function handleLoad() {\n      if (!request || request.readyState !== 4) {\n        return;\n      }\n\n      // The request errored out and we didn't get a response, this will be\n      // handled by onerror instead\n      // With one exception: request that using file: protocol, most browsers\n      // will return status as 0 even though it's a successful request\n      if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n        return;\n      }\n\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(timeoutErrorMessage, config, 'ECONNABORTED',\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?\n        cookies.read(config.xsrfCookieName) :\n        undefined;\n\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (config.responseType) {\n      try {\n        request.responseType = config.responseType;\n      } catch (e) {\n        // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.\n        // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.\n        if (config.responseType !== 'json') {\n          throw e;\n        }\n      }\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,MAAM,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACxC,IAAIE,OAAO,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIG,QAAQ,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAC/C,IAAII,aAAa,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AACpD,IAAIK,YAAY,GAAGL,OAAO,CAAC,2BAA2B,CAAC;AACvD,IAAIM,eAAe,GAAGN,OAAO,CAAC,8BAA8B,CAAC;AAC7D,IAAIO,WAAW,GAAGP,OAAO,CAAC,qBAAqB,CAAC;AAEhDQ,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAIC,OAAO,CAAC,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAC9D,IAAIC,WAAW,GAAGL,MAAM,CAACM,IAAI;IAC7B,IAAIC,cAAc,GAAGP,MAAM,CAACQ,OAAO;IAEnC,IAAIpB,KAAK,CAACqB,UAAU,CAACJ,WAAW,CAAC,EAAE;MACjC,OAAOE,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC;IACzC;IAEA,IACE,CAACnB,KAAK,CAACsB,MAAM,CAACL,WAAW,CAAC,IAAIjB,KAAK,CAACuB,MAAM,CAACN,WAAW,CAAC,KACvDA,WAAW,CAACO,IAAI,EAChB;MACA,OAAOL,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC;IACzC;IAEA,IAAIM,OAAO,GAAG,IAAIC,cAAc,CAAC,CAAC;;IAElC;IACA,IAAId,MAAM,CAACe,IAAI,EAAE;MACf,IAAIC,QAAQ,GAAGhB,MAAM,CAACe,IAAI,CAACC,QAAQ,IAAI,EAAE;MACzC,IAAIC,QAAQ,GAAGC,QAAQ,CAACC,kBAAkB,CAACnB,MAAM,CAACe,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAI,EAAE;MACvEV,cAAc,CAACa,aAAa,GAAG,QAAQ,GAAGC,IAAI,CAACL,QAAQ,GAAG,GAAG,GAAGC,QAAQ,CAAC;IAC3E;IAEA,IAAIK,QAAQ,GAAG7B,aAAa,CAACO,MAAM,CAACuB,OAAO,EAAEvB,MAAM,CAACwB,GAAG,CAAC;IACxDX,OAAO,CAACY,IAAI,CAACzB,MAAM,CAAC0B,MAAM,CAACC,WAAW,CAAC,CAAC,EAAEnC,QAAQ,CAAC8B,QAAQ,EAAEtB,MAAM,CAAC4B,MAAM,EAAE5B,MAAM,CAAC6B,gBAAgB,CAAC,EAAE,IAAI,CAAC;;IAE3G;IACAhB,OAAO,CAACiB,OAAO,GAAG9B,MAAM,CAAC8B,OAAO;;IAEhC;IACAjB,OAAO,CAACkB,kBAAkB,GAAG,SAASC,UAAUA,CAAA,EAAG;MACjD,IAAI,CAACnB,OAAO,IAAIA,OAAO,CAACoB,UAAU,KAAK,CAAC,EAAE;QACxC;MACF;;MAEA;MACA;MACA;MACA;MACA,IAAIpB,OAAO,CAACqB,MAAM,KAAK,CAAC,IAAI,EAAErB,OAAO,CAACsB,WAAW,IAAItB,OAAO,CAACsB,WAAW,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAChG;MACF;;MAEA;MACA,IAAIC,eAAe,GAAG,uBAAuB,IAAIxB,OAAO,GAAGnB,YAAY,CAACmB,OAAO,CAACyB,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;MAC/G,IAAIC,YAAY,GAAG,CAACvC,MAAM,CAACwC,YAAY,IAAIxC,MAAM,CAACwC,YAAY,KAAK,MAAM,GAAG3B,OAAO,CAAC4B,YAAY,GAAG5B,OAAO,CAAC6B,QAAQ;MACnH,IAAIA,QAAQ,GAAG;QACbpC,IAAI,EAAEiC,YAAY;QAClBL,MAAM,EAAErB,OAAO,CAACqB,MAAM;QACtBS,UAAU,EAAE9B,OAAO,CAAC8B,UAAU;QAC9BnC,OAAO,EAAE6B,eAAe;QACxBrC,MAAM,EAAEA,MAAM;QACda,OAAO,EAAEA;MACX,CAAC;MAEDvB,MAAM,CAACa,OAAO,EAAEC,MAAM,EAAEsC,QAAQ,CAAC;;MAEjC;MACA7B,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACAA,OAAO,CAAC+B,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;MACvC,IAAI,CAAChC,OAAO,EAAE;QACZ;MACF;MAEAT,MAAM,CAACR,WAAW,CAAC,iBAAiB,EAAEI,MAAM,EAAE,cAAc,EAAEa,OAAO,CAAC,CAAC;;MAEvE;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACAA,OAAO,CAACiC,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;MACvC;MACA;MACA3C,MAAM,CAACR,WAAW,CAAC,eAAe,EAAEI,MAAM,EAAE,IAAI,EAAEa,OAAO,CAAC,CAAC;;MAE3D;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACAA,OAAO,CAACmC,SAAS,GAAG,SAASC,aAAaA,CAAA,EAAG;MAC3C,IAAIC,mBAAmB,GAAG,aAAa,GAAGlD,MAAM,CAAC8B,OAAO,GAAG,aAAa;MACxE,IAAI9B,MAAM,CAACkD,mBAAmB,EAAE;QAC9BA,mBAAmB,GAAGlD,MAAM,CAACkD,mBAAmB;MAClD;MACA9C,MAAM,CAACR,WAAW,CAACsD,mBAAmB,EAAElD,MAAM,EAAE,cAAc,EAC5Da,OAAO,CAAC,CAAC;;MAEX;MACAA,OAAO,GAAG,IAAI;IAChB,CAAC;;IAED;IACA;IACA;IACA,IAAIzB,KAAK,CAAC+D,oBAAoB,CAAC,CAAC,EAAE;MAChC;MACA,IAAIC,SAAS,GAAG,CAACpD,MAAM,CAACqD,eAAe,IAAI1D,eAAe,CAAC2B,QAAQ,CAAC,KAAKtB,MAAM,CAACsD,cAAc,GAC5F/D,OAAO,CAACgE,IAAI,CAACvD,MAAM,CAACsD,cAAc,CAAC,GACnCE,SAAS;MAEX,IAAIJ,SAAS,EAAE;QACb7C,cAAc,CAACP,MAAM,CAACyD,cAAc,CAAC,GAAGL,SAAS;MACnD;IACF;;IAEA;IACA,IAAI,kBAAkB,IAAIvC,OAAO,EAAE;MACjCzB,KAAK,CAACsE,OAAO,CAACnD,cAAc,EAAE,SAASoD,gBAAgBA,CAACC,GAAG,EAAEC,GAAG,EAAE;QAChE,IAAI,OAAOxD,WAAW,KAAK,WAAW,IAAIwD,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE;UAC9E;UACA,OAAOvD,cAAc,CAACsD,GAAG,CAAC;QAC5B,CAAC,MAAM;UACL;UACAhD,OAAO,CAAC8C,gBAAgB,CAACE,GAAG,EAAED,GAAG,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACxE,KAAK,CAAC2E,WAAW,CAAC/D,MAAM,CAACqD,eAAe,CAAC,EAAE;MAC9CxC,OAAO,CAACwC,eAAe,GAAG,CAAC,CAACrD,MAAM,CAACqD,eAAe;IACpD;;IAEA;IACA,IAAIrD,MAAM,CAACwC,YAAY,EAAE;MACvB,IAAI;QACF3B,OAAO,CAAC2B,YAAY,GAAGxC,MAAM,CAACwC,YAAY;MAC5C,CAAC,CAAC,OAAOwB,CAAC,EAAE;QACV;QACA;QACA,IAAIhE,MAAM,CAACwC,YAAY,KAAK,MAAM,EAAE;UAClC,MAAMwB,CAAC;QACT;MACF;IACF;;IAEA;IACA,IAAI,OAAOhE,MAAM,CAACiE,kBAAkB,KAAK,UAAU,EAAE;MACnDpD,OAAO,CAACqD,gBAAgB,CAAC,UAAU,EAAElE,MAAM,CAACiE,kBAAkB,CAAC;IACjE;;IAEA;IACA,IAAI,OAAOjE,MAAM,CAACmE,gBAAgB,KAAK,UAAU,IAAItD,OAAO,CAACuD,MAAM,EAAE;MACnEvD,OAAO,CAACuD,MAAM,CAACF,gBAAgB,CAAC,UAAU,EAAElE,MAAM,CAACmE,gBAAgB,CAAC;IACtE;IAEA,IAAInE,MAAM,CAACqE,WAAW,EAAE;MACtB;MACArE,MAAM,CAACqE,WAAW,CAACC,OAAO,CAACC,IAAI,CAAC,SAASC,UAAUA,CAACC,MAAM,EAAE;QAC1D,IAAI,CAAC5D,OAAO,EAAE;UACZ;QACF;QAEAA,OAAO,CAAC6D,KAAK,CAAC,CAAC;QACftE,MAAM,CAACqE,MAAM,CAAC;QACd;QACA5D,OAAO,GAAG,IAAI;MAChB,CAAC,CAAC;IACJ;IAEA,IAAI,CAACR,WAAW,EAAE;MAChBA,WAAW,GAAG,IAAI;IACpB;;IAEA;IACAQ,OAAO,CAAC8D,IAAI,CAACtE,WAAW,CAAC;EAC3B,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}