import React, { Fragment, createContext, useReducer } from "react";
import Layout from "../layout";
import Slider from "./Slider";
import ProductCategory from "./ProductCategory";
import { homeState, homeReducer } from "./HomeContext";
import SingleProduct from "./SingleProduct";

export const HomeContext = createContext();

const HomeComponent = () => {
  return (
    <Fragment>
      <Slider />
      {/* Debug info */}
      <div className="m-4 md:mx-8 bg-blue-50 p-4 rounded-lg">
        <div className="text-sm text-blue-800">
          🔧 <strong>Debug Mode:</strong> Trang này hiển thị sản phẩm từ backend API.
          <br />
          📍 <strong>API URL:</strong> {process.env.REACT_APP_API_URL}/api/v1/products/customer
          <br />
          🧪 <strong>Test Links:</strong>
          <a href="/api-test" className="underline mx-2">API Test</a> |
          <a href="/test-api.html" className="underline mx-2">Direct API Test</a>
          <br />
          💡 <strong>Tip:</strong> Mở Console (F12) để xem debug logs
        </div>
      </div>
      {/* Category, Search & Filter Section */}
      <section className="m-4 md:mx-8 md:my-6">
        <ProductCategory />
      </section>
      {/* Product Section */}
      <section className="m-4 md:mx-8 md:my-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        <SingleProduct />
      </section>
    </Fragment>
  );
};

const Home = (props) => {
  const [data, dispatch] = useReducer(homeReducer, homeState);
  return (
    <Fragment>
      <HomeContext.Provider value={{ data, dispatch }}>
        <Layout children={<HomeComponent />} />
      </HomeContext.Provider>
    </Fragment>
  );
};

export default Home;
