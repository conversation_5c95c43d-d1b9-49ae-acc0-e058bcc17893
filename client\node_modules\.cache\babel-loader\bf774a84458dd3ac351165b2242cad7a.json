{"ast": null, "code": "export const isWish = (id, wList) => {\n  if (wList !== null && wList.includes(id) === true) {\n    return true;\n  }\n  return false;\n};\nexport const isWishReq = (e, id, setWlist) => {\n  let list = localStorage.getItem(\"wishList\") ? JSON.parse(localStorage.getItem(\"wishList\")) : [];\n  if (list.length > 0) {\n    if (list.includes(id) !== true) {\n      list.push(id);\n      localStorage.setItem(\"wishList\", JSON.stringify(list));\n      setWlist(list);\n    }\n  } else {\n    list.push(id);\n    localStorage.setItem(\"wishList\", JSON.stringify(list));\n    setWlist(list);\n  }\n};\nexport const unWishReq = (e, id, setWlist) => {\n  let list = localStorage.getItem(\"wishList\") ? JSON.parse(localStorage.getItem(\"wishList\")) : [];\n  if (list.length > 0) {\n    if (list.includes(id) === true) {\n      list.splice(list.indexOf(id), 1);\n      localStorage.setItem(\"wishList\", JSON.stringify(list));\n      setWlist(list);\n    }\n  }\n};\nexport const nextSlide = (totalImg, slide, setSlide) => {\n  if (slide === totalImg - 1) {\n    setSlide(0);\n  } else if (slide < totalImg) {\n    setSlide(slide + 1);\n  }\n};\nexport const prevSlide = (totalImg, slide, setSlide) => {\n  if (slide === 0) {\n    setSlide(totalImg - 1);\n  } else if (slide === totalImg - 1) {\n    setSlide(0);\n  }\n};", "map": {"version": 3, "names": ["isWish", "id", "wList", "includes", "isWishReq", "e", "setWlist", "list", "localStorage", "getItem", "JSON", "parse", "length", "push", "setItem", "stringify", "unWishReq", "splice", "indexOf", "nextSlide", "totalImg", "slide", "setSlide", "prevSlide"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["export const isWish = (id, wList) => {\r\n  if (wList !== null && wList.includes(id) === true) {\r\n    return true;\r\n  }\r\n  return false;\r\n};\r\n\r\nexport const isWishReq = (e, id, setWlist) => {\r\n  let list = localStorage.getItem(\"wishList\")\r\n    ? JSON.parse(localStorage.getItem(\"wishList\"))\r\n    : [];\r\n  if (list.length > 0) {\r\n    if (list.includes(id) !== true) {\r\n      list.push(id);\r\n      localStorage.setItem(\"wishList\", JSON.stringify(list));\r\n      setWlist(list);\r\n    }\r\n  } else {\r\n    list.push(id);\r\n    localStorage.setItem(\"wishList\", JSON.stringify(list));\r\n    setWlist(list);\r\n  }\r\n};\r\n\r\nexport const unWishReq = (e, id, setWlist) => {\r\n  let list = localStorage.getItem(\"wishList\")\r\n    ? JSON.parse(localStorage.getItem(\"wishList\"))\r\n    : [];\r\n  if (list.length > 0) {\r\n    if (list.includes(id) === true) {\r\n      list.splice(list.indexOf(id), 1);\r\n      localStorage.setItem(\"wishList\", JSON.stringify(list));\r\n      setWlist(list);\r\n    }\r\n  }\r\n};\r\n\r\nexport const nextSlide = (totalImg, slide, setSlide) => {\r\n  if (slide === totalImg - 1) {\r\n    setSlide(0);\r\n  } else if (slide < totalImg) {\r\n    setSlide(slide + 1);\r\n  }\r\n};\r\n\r\nexport const prevSlide = (totalImg, slide, setSlide) => {\r\n  if (slide === 0) {\r\n    setSlide(totalImg - 1);\r\n  } else if (slide === totalImg - 1) {\r\n    setSlide(0);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EACnC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACC,QAAQ,CAACF,EAAE,CAAC,KAAK,IAAI,EAAE;IACjD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AAED,OAAO,MAAMG,SAAS,GAAGA,CAACC,CAAC,EAAEJ,EAAE,EAAEK,QAAQ,KAAK;EAC5C,IAAIC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GACvCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,EAAE;EACN,IAAIF,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;IACnB,IAAIL,IAAI,CAACJ,QAAQ,CAACF,EAAE,CAAC,KAAK,IAAI,EAAE;MAC9BM,IAAI,CAACM,IAAI,CAACZ,EAAE,CAAC;MACbO,YAAY,CAACM,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACR,IAAI,CAAC,CAAC;MACtDD,QAAQ,CAACC,IAAI,CAAC;IAChB;EACF,CAAC,MAAM;IACLA,IAAI,CAACM,IAAI,CAACZ,EAAE,CAAC;IACbO,YAAY,CAACM,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACR,IAAI,CAAC,CAAC;IACtDD,QAAQ,CAACC,IAAI,CAAC;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,SAAS,GAAGA,CAACX,CAAC,EAAEJ,EAAE,EAAEK,QAAQ,KAAK;EAC5C,IAAIC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GACvCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,EAAE;EACN,IAAIF,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;IACnB,IAAIL,IAAI,CAACJ,QAAQ,CAACF,EAAE,CAAC,KAAK,IAAI,EAAE;MAC9BM,IAAI,CAACU,MAAM,CAACV,IAAI,CAACW,OAAO,CAACjB,EAAE,CAAC,EAAE,CAAC,CAAC;MAChCO,YAAY,CAACM,OAAO,CAAC,UAAU,EAAEJ,IAAI,CAACK,SAAS,CAACR,IAAI,CAAC,CAAC;MACtDD,QAAQ,CAACC,IAAI,CAAC;IAChB;EACF;AACF,CAAC;AAED,OAAO,MAAMY,SAAS,GAAGA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,KAAK;EACtD,IAAID,KAAK,KAAKD,QAAQ,GAAG,CAAC,EAAE;IAC1BE,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC,MAAM,IAAID,KAAK,GAAGD,QAAQ,EAAE;IAC3BE,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC;EACrB;AACF,CAAC;AAED,OAAO,MAAME,SAAS,GAAGA,CAACH,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,KAAK;EACtD,IAAID,KAAK,KAAK,CAAC,EAAE;IACfC,QAAQ,CAACF,QAAQ,GAAG,CAAC,CAAC;EACxB,CAAC,MAAM,IAAIC,KAAK,KAAKD,QAAQ,GAAG,CAAC,EAAE;IACjCE,QAAQ,CAAC,CAAC,CAAC;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}