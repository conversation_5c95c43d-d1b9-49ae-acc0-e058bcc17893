{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getSingleProduct = async (productId, userId = 1) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\n\n    // Convert new format to old format for compatibility\n    const product = res.data;\n    const convertedProduct = {\n      _id: product.productId,\n      pName: product.name,\n      pDescription: product.description,\n      pPrice: product.price,\n      pImages: product.images || [],\n      pCategory: {\n        cName: product.category\n      },\n      pRatingsReviews: [],\n      // Backend chưa có reviews\n      pOffer: 0,\n      pStatus: product.availabilityStatus === \"AVAILABLE\" ? \"Active\" : \"Inactive\",\n      pQuantity: product.stockQuantity || 0,\n      specifications: product.specifications,\n      weight: product.weight,\n      rushEligible: product.rushEligible\n    };\n    return {\n      Product: convertedProduct\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const postAddReview = async formData => {\n  try {\n    // Backend chưa có review system\n    console.log(\"⚠️ postAddReview: Backend chưa có review system\");\n    return {\n      success: false,\n      message: \"Review system chưa được implement\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const postDeleteReview = async formData => {\n  try {\n    // Backend chưa có review system\n    console.log(\"⚠️ postDeleteReview: Backend chưa có review system\");\n    return {\n      success: false,\n      message: \"Review system chưa được implement\"\n    };\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getSingleProduct", "productId", "userId", "res", "get", "product", "data", "convertedProduct", "_id", "pName", "name", "pDescription", "description", "pPrice", "price", "pImages", "images", "pCategory", "cName", "category", "pRatingsReviews", "pOffer", "pStatus", "availabilityStatus", "pQuantity", "stockQuantity", "specifications", "weight", "rushEligible", "Product", "error", "console", "log", "postAddReview", "formData", "success", "message", "postDeleteReview"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getSingleProduct = async (productId, userId = 1) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\r\n\r\n    // Convert new format to old format for compatibility\r\n    const product = res.data;\r\n    const convertedProduct = {\r\n      _id: product.productId,\r\n      pName: product.name,\r\n      pDescription: product.description,\r\n      pPrice: product.price,\r\n      pImages: product.images || [],\r\n      pCategory: { cName: product.category },\r\n      pRatingsReviews: [], // Backend chưa có reviews\r\n      pOffer: 0,\r\n      pStatus: product.availabilityStatus === \"AVAILABLE\" ? \"Active\" : \"Inactive\",\r\n      pQuantity: product.stockQuantity || 0,\r\n      specifications: product.specifications,\r\n      weight: product.weight,\r\n      rushEligible: product.rushEligible\r\n    };\r\n\r\n    return { Product: convertedProduct };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const postAddReview = async (formData) => {\r\n  try {\r\n    // Backend chưa có review system\r\n    console.log(\"⚠️ postAddReview: Backend chưa có review system\");\r\n    return { success: false, message: \"Review system chưa được implement\" };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const postDeleteReview = async (formData) => {\r\n  try {\r\n    // Backend chưa có review system\r\n    console.log(\"⚠️ postDeleteReview: Backend chưa có review system\");\r\n    return { success: false, message: \"Review system chưa được implement\" };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,MAAM,GAAG,CAAC,KAAK;EAC/D,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,MAAM,6BAA6BK,SAAS,WAAWC,MAAM,EAAE,CAAC;;IAE7F;IACA,MAAMG,OAAO,GAAGF,GAAG,CAACG,IAAI;IACxB,MAAMC,gBAAgB,GAAG;MACvBC,GAAG,EAAEH,OAAO,CAACJ,SAAS;MACtBQ,KAAK,EAAEJ,OAAO,CAACK,IAAI;MACnBC,YAAY,EAAEN,OAAO,CAACO,WAAW;MACjCC,MAAM,EAAER,OAAO,CAACS,KAAK;MACrBC,OAAO,EAAEV,OAAO,CAACW,MAAM,IAAI,EAAE;MAC7BC,SAAS,EAAE;QAAEC,KAAK,EAAEb,OAAO,CAACc;MAAS,CAAC;MACtCC,eAAe,EAAE,EAAE;MAAE;MACrBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAEjB,OAAO,CAACkB,kBAAkB,KAAK,WAAW,GAAG,QAAQ,GAAG,UAAU;MAC3EC,SAAS,EAAEnB,OAAO,CAACoB,aAAa,IAAI,CAAC;MACrCC,cAAc,EAAErB,OAAO,CAACqB,cAAc;MACtCC,MAAM,EAAEtB,OAAO,CAACsB,MAAM;MACtBC,YAAY,EAAEvB,OAAO,CAACuB;IACxB,CAAC;IAED,OAAO;MAAEC,OAAO,EAAEtB;IAAiB,CAAC;EACtC,CAAC,CAAC,OAAOuB,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAOC,QAAQ,IAAK;EAC/C,IAAI;IACF;IACAH,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAO;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAoC,CAAC;EACzE,CAAC,CAAC,OAAON,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMO,gBAAgB,GAAG,MAAOH,QAAQ,IAAK;EAClD,IAAI;IACF;IACAH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjE,OAAO;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAoC,CAAC;EACzE,CAAC,CAAC,OAAON,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}