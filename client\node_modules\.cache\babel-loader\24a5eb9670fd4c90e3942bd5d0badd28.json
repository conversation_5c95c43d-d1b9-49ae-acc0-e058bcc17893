{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\auth\\\\CartProtectedRoute.js\";\nimport React from \"react\";\nimport { Route, Redirect } from \"react-router-dom\";\nimport { isAuthenticate } from \"./fetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartProtectedRoute = ({\n  component: Component,\n  ...rest\n}) => /*#__PURE__*/_jsxDEV(Route, {\n  ...rest,\n  render: props => JSON.parse(localStorage.getItem(\"cart\")).length !== 0 && isAuthenticate() ? /*#__PURE__*/_jsxDEV(Component, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(Redirect, {\n    to: {\n      pathname: \"/\",\n      state: {\n        from: props.location\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this);\n_c = CartProtectedRoute;\nexport default CartProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"CartProtectedRoute\");", "map": {"version": 3, "names": ["React", "Route", "Redirect", "isAuthenticate", "jsxDEV", "_jsxDEV", "CartProtectedRoute", "component", "Component", "rest", "render", "props", "JSON", "parse", "localStorage", "getItem", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "state", "from", "location", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/auth/CartProtectedRoute.js"], "sourcesContent": ["import React from \"react\";\r\nimport { Route, Redirect } from \"react-router-dom\";\r\nimport { isAuthenticate } from \"./fetchApi\";\r\n\r\nconst CartProtectedRoute = ({ component: Component, ...rest }) => (\r\n  <Route\r\n    {...rest}\r\n    render={(props) =>\r\n      JSON.parse(localStorage.getItem(\"cart\")).length !== 0 &&\r\n      isAuthenticate() ? (\r\n        <Component {...props} />\r\n      ) : (\r\n        <Redirect\r\n          to={{\r\n            pathname: \"/\",\r\n            state: { from: props.location },\r\n          }}\r\n        />\r\n      )\r\n    }\r\n  />\r\n);\r\n\r\nexport default CartProtectedRoute;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,SAAS,EAAEC,SAAS;EAAE,GAAGC;AAAK,CAAC,kBAC3DJ,OAAA,CAACJ,KAAK;EAAA,GACAQ,IAAI;EACRC,MAAM,EAAGC,KAAK,IACZC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,IACrDb,cAAc,CAAC,CAAC,gBACdE,OAAA,CAACG,SAAS;IAAA,GAAKG;EAAK;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,gBAExBf,OAAA,CAACH,QAAQ;IACPmB,EAAE,EAAE;MACFC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;QAAEC,IAAI,EAAEb,KAAK,CAACc;MAAS;IAChC;EAAE;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAEJ;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACF,CACF;AAACM,EAAA,GAjBIpB,kBAAkB;AAmBxB,eAAeA,kBAAkB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}