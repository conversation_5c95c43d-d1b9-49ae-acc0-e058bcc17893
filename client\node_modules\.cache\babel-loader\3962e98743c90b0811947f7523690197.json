{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\auth\\\\LoginSignup.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useState, useContext } from \"react\";\nimport Login from \"./Login\";\nimport Signup from \"./Signup\";\nimport { LayoutContext } from \"../index\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginSignup = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(LayoutContext);\n  const [login, setLogin] = useState(true);\n  const [loginValue, setLoginValue] = useState(\"Create an account\");\n  const loginSignupModalToggle = () => data.loginSignupModal ? dispatch({\n    type: \"loginSignupModalToggle\",\n    payload: false\n  }) : dispatch({\n    type: \"loginSignupModalToggle\",\n    payload: true\n  });\n  const changeLoginSignup = () => {\n    if (login) {\n      setLogin(false);\n      setLoginValue(\"Login\");\n    } else {\n      setLogin(true);\n      setLoginValue(\"Create an account\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: e => loginSignupModalToggle(),\n      className: ` ${data.loginSignupModal ? \"\" : \"hidden\"} fixed top-0 z-40 w-full h-screen bg-black opacity-50 cursor-pointer`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: ` ${data.loginSignupModal ? \"\" : \"hidden\"} fixed z-40 inset-0 my-8 md:my-20 flex items-start justify-center overflow-auto`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-11/12 md:w-3/5 lg:w-2/4 relative space-y-4 bg-white p-6 md:px-12 md:py-6\",\n        children: [login ? /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 20\n        }, this) : /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 32\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"border-b border-gray-500 w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"or\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"border-b border-gray-500 w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: e => changeLoginSignup(),\n          style: {\n            color: \"#303031\",\n            border: \"1px solid #303031\"\n          },\n          className: \"px-4 py-2 font-medium text-center cursor-pointer\",\n          children: loginValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 mx-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            onClick: e => {\n              loginSignupModalToggle();\n              dispatch({\n                type: \"loginSignupError\",\n                payload: false\n              });\n            },\n            className: \"w-6 h-6 cursor-pointer\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginSignup, \"GvLjPkijmglzHeQZ5piOe1R8Uvo=\");\n_c = LoginSignup;\nexport default LoginSignup;\nvar _c;\n$RefreshReg$(_c, \"LoginSignup\");", "map": {"version": 3, "names": ["React", "Fragment", "useState", "useContext", "<PERSON><PERSON>", "Signup", "LayoutContext", "jsxDEV", "_jsxDEV", "LoginSignup", "props", "_s", "data", "dispatch", "login", "<PERSON><PERSON><PERSON><PERSON>", "loginValue", "setLoginValue", "loginSignupModalToggle", "loginSignupModal", "type", "payload", "changeLoginSignup", "children", "onClick", "e", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "border", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/auth/LoginSignup.js"], "sourcesContent": ["import React, { Fragment, useState, useContext } from \"react\";\r\nimport Login from \"./Login\";\r\nimport Signup from \"./Signup\";\r\nimport { LayoutContext } from \"../index\";\r\n\r\nconst LoginSignup = (props) => {\r\n  const { data, dispatch } = useContext(LayoutContext);\r\n\r\n  const [login, setLogin] = useState(true);\r\n  const [loginValue, setLoginValue] = useState(\"Create an account\");\r\n\r\n  const loginSignupModalToggle = () =>\r\n    data.loginSignupModal\r\n      ? dispatch({ type: \"loginSignupModalToggle\", payload: false })\r\n      : dispatch({ type: \"loginSignupModalToggle\", payload: true });\r\n\r\n  const changeLoginSignup = () => {\r\n    if (login) {\r\n      setLogin(false);\r\n      setLoginValue(\"Login\");\r\n    } else {\r\n      setLogin(true);\r\n      setLoginValue(\"Create an account\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Fragment>\r\n      {/* Black Overlay  */}\r\n      <div\r\n        onClick={(e) => loginSignupModalToggle()}\r\n        className={` ${\r\n          data.loginSignupModal ? \"\" : \"hidden\"\r\n        } fixed top-0 z-40 w-full h-screen bg-black opacity-50 cursor-pointer`}\r\n      ></div>\r\n      {/* Signup Login Component Render */}\r\n      <section\r\n        className={` ${\r\n          data.loginSignupModal ? \"\" : \"hidden\"\r\n        } fixed z-40 inset-0 my-8 md:my-20 flex items-start justify-center overflow-auto`}\r\n      >\r\n        <div className=\"w-11/12 md:w-3/5 lg:w-2/4 relative space-y-4 bg-white p-6 md:px-12 md:py-6\">\r\n          {login ? <Login /> : <Signup />}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span className=\"border-b border-gray-500 w-full\" />\r\n            <span className=\"font-medium\">or</span>\r\n            <span className=\"border-b border-gray-500 w-full\" />\r\n          </div>\r\n          <div\r\n            onClick={(e) => changeLoginSignup()}\r\n            style={{ color: \"#303031\", border: \"1px solid #303031\" }}\r\n            className=\"px-4 py-2 font-medium text-center cursor-pointer\"\r\n          >\r\n            {loginValue}\r\n          </div>\r\n          {/*  Modal Close Button */}\r\n          <div className=\"absolute top-0 right-0 mx-4\">\r\n            <svg\r\n              onClick={(e) => {\r\n                loginSignupModalToggle();\r\n                dispatch({ type: \"loginSignupError\", payload: false });\r\n              }}\r\n              className=\"w-6 h-6 cursor-pointer\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default LoginSignup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC7D,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,aAAa,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGV,UAAU,CAACG,aAAa,CAAC;EAEpD,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,mBAAmB,CAAC;EAEjE,MAAMgB,sBAAsB,GAAGA,CAAA,KAC7BN,IAAI,CAACO,gBAAgB,GACjBN,QAAQ,CAAC;IAAEO,IAAI,EAAE,wBAAwB;IAAEC,OAAO,EAAE;EAAM,CAAC,CAAC,GAC5DR,QAAQ,CAAC;IAAEO,IAAI,EAAE,wBAAwB;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAEjE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIR,KAAK,EAAE;MACTC,QAAQ,CAAC,KAAK,CAAC;MACfE,aAAa,CAAC,OAAO,CAAC;IACxB,CAAC,MAAM;MACLF,QAAQ,CAAC,IAAI,CAAC;MACdE,aAAa,CAAC,mBAAmB,CAAC;IACpC;EACF,CAAC;EAED,oBACET,OAAA,CAACP,QAAQ;IAAAsB,QAAA,gBAEPf,OAAA;MACEgB,OAAO,EAAGC,CAAC,IAAKP,sBAAsB,CAAC,CAAE;MACzCQ,SAAS,EAAE,IACTd,IAAI,CAACO,gBAAgB,GAAG,EAAE,GAAG,QAAQ;IACgC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eAEPtB,OAAA;MACEkB,SAAS,EAAE,IACTd,IAAI,CAACO,gBAAgB,GAAG,EAAE,GAAG,QAAQ,iFAC2C;MAAAI,QAAA,eAElFf,OAAA;QAAKkB,SAAS,EAAC,4EAA4E;QAAAH,QAAA,GACxFT,KAAK,gBAAGN,OAAA,CAACJ,KAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACH,MAAM;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BtB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAH,QAAA,gBAC1Cf,OAAA;YAAMkB,SAAS,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDtB,OAAA;YAAMkB,SAAS,EAAC,aAAa;YAAAH,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCtB,OAAA;YAAMkB,SAAS,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNtB,OAAA;UACEgB,OAAO,EAAGC,CAAC,IAAKH,iBAAiB,CAAC,CAAE;UACpCS,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAoB,CAAE;UACzDP,SAAS,EAAC,kDAAkD;UAAAH,QAAA,EAE3DP;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENtB,OAAA;UAAKkB,SAAS,EAAC,6BAA6B;UAAAH,QAAA,eAC1Cf,OAAA;YACEgB,OAAO,EAAGC,CAAC,IAAK;cACdP,sBAAsB,CAAC,CAAC;cACxBL,QAAQ,CAAC;gBAAEO,IAAI,EAAE,kBAAkB;gBAAEC,OAAO,EAAE;cAAM,CAAC,CAAC;YACxD,CAAE;YACFK,SAAS,EAAC,wBAAwB;YAClCQ,IAAI,EAAC,cAAc;YACnBC,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAb,QAAA,eAElCf,OAAA;cACE6B,QAAQ,EAAC,SAAS;cAClBC,CAAC,EAAC,oMAAoM;cACtMC,QAAQ,EAAC;YAAS;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACnB,EAAA,CAzEIF,WAAW;AAAA+B,EAAA,GAAX/B,WAAW;AA2EjB,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}