{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI.\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // You can also log the error to an error reporting service\n    console.error('🚨 Error caught by ErrorBoundary:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // You can render any custom fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-red-50 flex items-center justify-center p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border border-red-200 rounded-lg p-6 max-w-2xl w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-red-600 mb-4\",\n            children: \"\\uD83D\\uDEA8 Something went wrong!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 mb-4\",\n            children: \"An error occurred that prevented the app from working properly. This might be why you can't interact with the interface.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 p-4 rounded mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-800 mb-2\",\n              children: \"Error Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-sm text-red-600 whitespace-pre-wrap\",\n              children: this.state.error && this.state.error.toString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 p-4 rounded mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-800 mb-2\",\n              children: \"Stack Trace:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-xs text-gray-600 whitespace-pre-wrap overflow-auto max-h-40\",\n              children: this.state.errorInfo.componentStack\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n              children: \"\\uD83D\\uDD04 Reload Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/simple-test',\n              className: \"w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n              children: \"\\uD83E\\uDDEA Go to Simple Test Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold text-yellow-800\",\n              children: \"Troubleshooting Tips:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-yellow-700 text-sm mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Check browser console (F12) for more errors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Try refreshing the page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Clear browser cache and cookies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Make sure backend server is running\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "console", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toString", "componentStack", "onClick", "window", "location", "reload", "href"], "sources": ["D:/ITSS_Reference/client/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI.\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // You can also log the error to an error reporting service\n    console.error('🚨 Error caught by ErrorBoundary:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // You can render any custom fallback UI\n      return (\n        <div className=\"min-h-screen bg-red-50 flex items-center justify-center p-8\">\n          <div className=\"bg-white border border-red-200 rounded-lg p-6 max-w-2xl w-full\">\n            <h1 className=\"text-2xl font-bold text-red-600 mb-4\">🚨 Something went wrong!</h1>\n            <p className=\"text-gray-700 mb-4\">\n              An error occurred that prevented the app from working properly. This might be why you can't interact with the interface.\n            </p>\n            \n            <div className=\"bg-gray-100 p-4 rounded mb-4\">\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Error Details:</h3>\n              <pre className=\"text-sm text-red-600 whitespace-pre-wrap\">\n                {this.state.error && this.state.error.toString()}\n              </pre>\n            </div>\n\n            <div className=\"bg-gray-100 p-4 rounded mb-4\">\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Stack Trace:</h3>\n              <pre className=\"text-xs text-gray-600 whitespace-pre-wrap overflow-auto max-h-40\">\n                {this.state.errorInfo.componentStack}\n              </pre>\n            </div>\n\n            <div className=\"space-y-2\">\n              <button \n                onClick={() => window.location.reload()}\n                className=\"w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n              >\n                🔄 Reload Page\n              </button>\n              <button \n                onClick={() => window.location.href = '/simple-test'}\n                className=\"w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n              >\n                🧪 Go to Simple Test Page\n              </button>\n            </div>\n\n            <div className=\"mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded\">\n              <h4 className=\"font-semibold text-yellow-800\">Troubleshooting Tips:</h4>\n              <ul className=\"text-yellow-700 text-sm mt-1\">\n                <li>• Check browser console (F12) for more errors</li>\n                <li>• Try refreshing the page</li>\n                <li>• Clear browser cache and cookies</li>\n                <li>• Make sure backend server is running</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,SAASH,KAAK,CAACI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAG,OAAO,CAACJ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IACpE,IAAI,CAACI,QAAQ,CAAC;MACZL,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAEAK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB;MACA,oBACEN,OAAA;QAAKc,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1Ef,OAAA;UAAKc,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7Ef,OAAA;YAAIc,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFnB,OAAA;YAAGc,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJnB,OAAA;YAAKc,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3Cf,OAAA;cAAIc,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEnB,OAAA;cAAKc,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACtD,IAAI,CAACV,KAAK,CAACE,KAAK,IAAI,IAAI,CAACF,KAAK,CAACE,KAAK,CAACa,QAAQ,CAAC;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnB,OAAA;YAAKc,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3Cf,OAAA;cAAIc,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEnB,OAAA;cAAKc,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC9E,IAAI,CAACV,KAAK,CAACG,SAAS,CAACa;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnB,OAAA;YAAKc,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBf,OAAA;cACEsB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCX,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC9E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnB,OAAA;cACEsB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,cAAe;cACrDZ,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAChF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnB,OAAA;YAAKc,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtEf,OAAA;cAAIc,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEnB,OAAA;cAAIc,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC1Cf,OAAA;gBAAAe,QAAA,EAAI;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDnB,OAAA;gBAAAe,QAAA,EAAI;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCnB,OAAA;gBAAAe,QAAA,EAAI;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1CnB,OAAA;gBAAAe,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACf,KAAK,CAACW,QAAQ;EAC5B;AACF;AAEA,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}