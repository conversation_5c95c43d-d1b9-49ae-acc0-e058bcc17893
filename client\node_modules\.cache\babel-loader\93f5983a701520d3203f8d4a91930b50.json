{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SvgIcon from '../SvgIcon';\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, _extends({\n      \"data-testid\": `${displayName}Icon`,\n      ref: ref\n    }, props, {\n      children: path\n    }));\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(Component));\n}", "map": {"version": 3, "names": ["_extends", "React", "SvgIcon", "jsx", "_jsx", "createSvgIcon", "path", "displayName", "Component", "props", "ref", "children", "process", "env", "NODE_ENV", "mui<PERSON><PERSON>", "memo", "forwardRef"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/utils/createSvgIcon.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SvgIcon from '../SvgIcon';\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, _extends({\n      \"data-testid\": `${displayName}Icon`,\n      ref: ref\n    }, props, {\n      children: path\n    }));\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(Component));\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;;AAEhC;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACvD,SAASC,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC7B,OAAO,aAAaN,IAAI,CAACF,OAAO,EAAEF,QAAQ,CAAC;MACzC,aAAa,EAAE,GAAGO,WAAW,MAAM;MACnCG,GAAG,EAAEA;IACP,CAAC,EAAED,KAAK,EAAE;MACRE,QAAQ,EAAEL;IACZ,CAAC,CAAC,CAAC;EACL;EACA,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAN,SAAS,CAACD,WAAW,GAAG,GAAGA,WAAW,MAAM;EAC9C;EACAC,SAAS,CAACO,OAAO,GAAGb,OAAO,CAACa,OAAO;EACnC,OAAO,aAAad,KAAK,CAACe,IAAI,CAAE,aAAaf,KAAK,CAACgB,UAAU,CAACT,SAAS,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module"}