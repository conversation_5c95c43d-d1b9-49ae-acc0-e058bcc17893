{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst BearerToken = () => localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : false;\n_c = BearerToken;\nconst Headers = () => {\n  return {\n    headers: {\n      token: `Bearer ${BearerToken()}`\n    }\n  };\n};\n_c2 = Headers;\nexport const getAllCategory = async () => {\n  try {\n    let res = await axios.get(`${apiURL}/api/category/all-category`, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createCategory = async ({\n  cName,\n  cImage,\n  cDescription,\n  cStatus\n}) => {\n  let formData = new FormData();\n  formData.append(\"cImage\", cImage);\n  formData.append(\"cName\", cName);\n  formData.append(\"cDescription\", cDescription);\n  formData.append(\"cStatus\", cStatus);\n  try {\n    let res = await axios.post(`${apiURL}/api/category/add-category`, formData, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const editCategory = async (cId, des, status) => {\n  let data = {\n    cId: cId,\n    cDescription: des,\n    cStatus: status\n  };\n  try {\n    let res = await axios.post(`${apiURL}/api/category/edit-category`, data, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const deleteCategory = async cId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/category/delete-category`, {\n      cId\n    }, Headers());\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nvar _c, _c2;\n$RefreshReg$(_c, \"BearerToken\");\n$RefreshReg$(_c2, \"Headers\");", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "BearerToken", "localStorage", "getItem", "JSON", "parse", "token", "_c", "Headers", "headers", "_c2", "getAllCategory", "res", "get", "data", "error", "console", "log", "createCategory", "cName", "cImage", "cDescription", "cStatus", "formData", "FormData", "append", "post", "editCategory", "cId", "des", "status", "deleteCategory", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/categories/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst BearerToken = () =>\r\n  localStorage.getItem(\"jwt\")\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).token\r\n    : false;\r\nconst Headers = () => {\r\n  return {\r\n    headers: {\r\n      token: `Bearer ${BearerToken()}`,\r\n    },\r\n  };\r\n};\r\n\r\nexport const getAllCategory = async () => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/category/all-category`, Headers());\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createCategory = async ({\r\n  cName,\r\n  cImage,\r\n  cDescription,\r\n  cStatus,\r\n}) => {\r\n  let formData = new FormData();\r\n  formData.append(\"cImage\", cImage);\r\n  formData.append(\"cName\", cName);\r\n  formData.append(\"cDescription\", cDescription);\r\n  formData.append(\"cStatus\", cStatus);\r\n\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/add-category`,\r\n      formData,\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const editCategory = async (cId, des, status) => {\r\n  let data = { cId: cId, cDescription: des, cStatus: status };\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/edit-category`,\r\n      data,\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const deleteCategory = async (cId) => {\r\n  try {\r\n    let res = await axios.post(\r\n      `${apiURL}/api/category/delete-category`,\r\n      { cId },\r\n      Headers()\r\n    );\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,WAAW,GAAGA,CAAA,KAClBC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GACvBC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACG,KAAK,GAC7C,KAAK;AAACC,EAAA,GAHNN,WAAW;AAIjB,MAAMO,OAAO,GAAGA,CAAA,KAAM;EACpB,OAAO;IACLC,OAAO,EAAE;MACPH,KAAK,EAAE,UAAUL,WAAW,CAAC,CAAC;IAChC;EACF,CAAC;AACH,CAAC;AAACS,GAAA,GANIF,OAAO;AAQb,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,GAAGhB,MAAM,4BAA4B,EAAEW,OAAO,CAAC,CAAC,CAAC;IAC3E,OAAOI,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAO;EACnCC,KAAK;EACLC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEL,MAAM,CAAC;EACjCG,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,KAAK,CAAC;EAC/BI,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEJ,YAAY,CAAC;EAC7CE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;EAEnC,IAAI;IACF,IAAIV,GAAG,GAAG,MAAMhB,KAAK,CAAC8B,IAAI,CACxB,GAAG7B,MAAM,4BAA4B,EACrC0B,QAAQ,EACRf,OAAO,CAAC,CACV,CAAC;IACD,OAAOI,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMY,YAAY,GAAG,MAAAA,CAAOC,GAAG,EAAEC,GAAG,EAAEC,MAAM,KAAK;EACtD,IAAIhB,IAAI,GAAG;IAAEc,GAAG,EAAEA,GAAG;IAAEP,YAAY,EAAEQ,GAAG;IAAEP,OAAO,EAAEQ;EAAO,CAAC;EAC3D,IAAI;IACF,IAAIlB,GAAG,GAAG,MAAMhB,KAAK,CAAC8B,IAAI,CACxB,GAAG7B,MAAM,6BAA6B,EACtCiB,IAAI,EACJN,OAAO,CAAC,CACV,CAAC;IACD,OAAOI,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAG,MAAOH,GAAG,IAAK;EAC3C,IAAI;IACF,IAAIhB,GAAG,GAAG,MAAMhB,KAAK,CAAC8B,IAAI,CACxB,GAAG7B,MAAM,+BAA+B,EACxC;MAAE+B;IAAI,CAAC,EACPpB,OAAO,CAAC,CACV,CAAC;IACD,OAAOI,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAAC,IAAAR,EAAA,EAAAG,GAAA;AAAAsB,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAtB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}