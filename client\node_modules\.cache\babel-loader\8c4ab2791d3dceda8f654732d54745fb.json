{"ast": null, "code": "'use strict';\n\n/**\n * Update an Error with the specified config, error code, and response.\n *\n * @param {Error} error The error to update.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The error.\n */\nmodule.exports = function enhanceError(error, config, code, request, response) {\n  error.config = config;\n  if (code) {\n    error.code = code;\n  }\n  error.request = request;\n  error.response = response;\n  error.isAxiosError = true;\n  error.toJSON = function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code\n    };\n  };\n  return error;\n};", "map": {"version": 3, "names": ["module", "exports", "enhanceError", "error", "config", "code", "request", "response", "isAxiosError", "toJSON", "message", "name", "description", "number", "fileName", "lineNumber", "columnNumber", "stack"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/enhanceError.js"], "sourcesContent": ["'use strict';\n\n/**\n * Update an Error with the specified config, error code, and response.\n *\n * @param {Error} error The error to update.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The error.\n */\nmodule.exports = function enhanceError(error, config, code, request, response) {\n  error.config = config;\n  if (code) {\n    error.code = code;\n  }\n\n  error.request = request;\n  error.response = response;\n  error.isAxiosError = true;\n\n  error.toJSON = function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code\n    };\n  };\n  return error;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC7EJ,KAAK,CAACC,MAAM,GAAGA,MAAM;EACrB,IAAIC,IAAI,EAAE;IACRF,KAAK,CAACE,IAAI,GAAGA,IAAI;EACnB;EAEAF,KAAK,CAACG,OAAO,GAAGA,OAAO;EACvBH,KAAK,CAACI,QAAQ,GAAGA,QAAQ;EACzBJ,KAAK,CAACK,YAAY,GAAG,IAAI;EAEzBL,KAAK,CAACM,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC/B,OAAO;MACL;MACAC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACf;MACAC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB;MACAC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB;MACAb,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,IAAI,EAAE,IAAI,CAACA;IACb,CAAC;EACH,CAAC;EACD,OAAOF,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}