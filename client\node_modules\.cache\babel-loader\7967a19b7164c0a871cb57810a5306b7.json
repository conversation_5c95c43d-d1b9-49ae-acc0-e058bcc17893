{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\products\\\\ProductTable.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext, useEffect, useState } from \"react\";\nimport { getAllProduct, deleteProduct } from \"./FetchApi\";\nimport moment from \"moment\";\nimport { ProductContext } from \"./index\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst AllProduct = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(ProductContext);\n  const {\n    products\n  } = data;\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    fetchData();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchData = async () => {\n    setLoading(true);\n    let responseData = await getAllProduct();\n    setTimeout(() => {\n      if (responseData && responseData.Products) {\n        dispatch({\n          type: \"fetchProductsAndChangeState\",\n          payload: responseData.Products\n        });\n        setLoading(false);\n      }\n    }, 1000);\n  };\n  const deleteProductReq = async pId => {\n    let deleteC = await deleteProduct(pId);\n    if (deleteC.error) {\n      console.log(deleteC.error);\n    } else if (deleteC.success) {\n      console.log(deleteC.success);\n      fetchData();\n    }\n  };\n\n  /* This method call the editmodal & dispatch product context */\n  const editProduct = (pId, product, type) => {\n    if (type) {\n      dispatch({\n        type: \"editProductModalOpen\",\n        product: {\n          ...product,\n          pId: pId\n        }\n      });\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 animate-spin text-gray-600\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-1 overflow-auto bg-white shadow-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table-auto border w-full my-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Offer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Created at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Updated at\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-4 py-2 border\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: products && products.length > 0 ? products.map((item, key) => {\n            return /*#__PURE__*/_jsxDEV(ProductTable, {\n              product: item,\n              editProduct: (pId, product, type) => editProduct(pId, product, type),\n              deleteProduct: pId => deleteProductReq(pId)\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"10\",\n              className: \"text-xl text-center font-semibold py-8\",\n              children: \"No product found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 mt-2\",\n        children: [\"Total \", products && products.length, \" product found\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n\n/* Single Product Component */\n_s(AllProduct, \"Zn547btWVcaVecV67MybZIKf/Xk=\");\n_c = AllProduct;\nconst ProductTable = ({\n  product,\n  deleteProduct,\n  editProduct\n}) => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-left\",\n        children: product.pName.length > 15 ? product.pDescription.substring(1, 15) + \"...\" : product.pName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-left\",\n        children: [product.pDescription.slice(0, 15), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"w-12 h-12 object-cover object-center\",\n          src: `${apiURL}/uploads/products/${product.pImages[0]}`,\n          alt: \"pic\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: product.pStatus === \"Active\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bg-green-200 rounded-full text-center text-xs px-2 font-semibold\",\n          children: product.pStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bg-red-200 rounded-full text-center text-xs px-2 font-semibold\",\n          children: product.pStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: product.pQuantity\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: product.pCategory.cName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: product.pOffer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: moment(product.createdAt).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: moment(product.updatedAt).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => editProduct(product._id, product, true),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 fill-current text-green-500\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: e => deleteProduct(product._id),\n          className: \"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 fill-current text-red-500\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ProductTable;\nexport default AllProduct;\nvar _c, _c2;\n$RefreshReg$(_c, \"AllProduct\");\n$RefreshReg$(_c2, \"ProductTable\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useEffect", "useState", "getAllProduct", "deleteProduct", "moment", "ProductContext", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "AllProduct", "props", "_s", "data", "dispatch", "products", "loading", "setLoading", "fetchData", "responseData", "setTimeout", "Products", "type", "payload", "deleteProductReq", "pId", "deleteC", "error", "console", "log", "success", "editProduct", "product", "className", "children", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "item", "key", "ProductTable", "colSpan", "_c", "pName", "pDescription", "substring", "slice", "src", "pImages", "alt", "pStatus", "pQuantity", "pCategory", "cName", "pOffer", "createdAt", "format", "updatedAt", "onClick", "e", "_id", "fillRule", "clipRule", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/ProductTable.js"], "sourcesContent": ["import React, { Fragment, useContext, useEffect, useState } from \"react\";\r\nimport { getAllProduct, deleteProduct } from \"./FetchApi\";\r\nimport moment from \"moment\";\r\nimport { ProductContext } from \"./index\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst AllProduct = (props) => {\r\n  const { data, dispatch } = useContext(ProductContext);\r\n  const { products } = data;\r\n\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    setLoading(true);\r\n    let responseData = await getAllProduct();\r\n    setTimeout(() => {\r\n      if (responseData && responseData.Products) {\r\n        dispatch({\r\n          type: \"fetchProductsAndChangeState\",\r\n          payload: responseData.Products,\r\n        });\r\n        setLoading(false);\r\n      }\r\n    }, 1000);\r\n  };\r\n\r\n  const deleteProductReq = async (pId) => {\r\n    let deleteC = await deleteProduct(pId);\r\n    if (deleteC.error) {\r\n      console.log(deleteC.error);\r\n    } else if (deleteC.success) {\r\n      console.log(deleteC.success);\r\n      fetchData();\r\n    }\r\n  };\r\n\r\n  /* This method call the editmodal & dispatch product context */\r\n  const editProduct = (pId, product, type) => {\r\n    if (type) {\r\n      dispatch({\r\n        type: \"editProductModalOpen\",\r\n        product: { ...product, pId: pId },\r\n      });\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Fragment>\r\n      <div className=\"col-span-1 overflow-auto bg-white shadow-lg p-4\">\r\n        <table className=\"table-auto border w-full my-2\">\r\n          <thead>\r\n            <tr>\r\n              <th className=\"px-4 py-2 border\">Product</th>\r\n              <th className=\"px-4 py-2 border\">Description</th>\r\n              <th className=\"px-4 py-2 border\">Image</th>\r\n              <th className=\"px-4 py-2 border\">Status</th>\r\n              <th className=\"px-4 py-2 border\">Stock</th>\r\n              <th className=\"px-4 py-2 border\">Category</th>\r\n              <th className=\"px-4 py-2 border\">Offer</th>\r\n              <th className=\"px-4 py-2 border\">Created at</th>\r\n              <th className=\"px-4 py-2 border\">Updated at</th>\r\n              <th className=\"px-4 py-2 border\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {products && products.length > 0 ? (\r\n              products.map((item, key) => {\r\n                return (\r\n                  <ProductTable\r\n                    product={item}\r\n                    editProduct={(pId, product, type) =>\r\n                      editProduct(pId, product, type)\r\n                    }\r\n                    deleteProduct={(pId) => deleteProductReq(pId)}\r\n                    key={key}\r\n                  />\r\n                );\r\n              })\r\n            ) : (\r\n              <tr>\r\n                <td\r\n                  colSpan=\"10\"\r\n                  className=\"text-xl text-center font-semibold py-8\"\r\n                >\r\n                  No product found\r\n                </td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </table>\r\n        <div className=\"text-sm text-gray-600 mt-2\">\r\n          Total {products && products.length} product found\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\n/* Single Product Component */\r\nconst ProductTable = ({ product, deleteProduct, editProduct }) => {\r\n  return (\r\n    <Fragment>\r\n      <tr>\r\n        <td className=\"p-2 text-left\">\r\n          {product.pName.length > 15\r\n            ? product.pDescription.substring(1, 15) + \"...\"\r\n            : product.pName}\r\n        </td>\r\n        <td className=\"p-2 text-left\">\r\n          {product.pDescription.slice(0, 15)}...\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          <img\r\n            className=\"w-12 h-12 object-cover object-center\"\r\n            src={`${apiURL}/uploads/products/${product.pImages[0]}`}\r\n            alt=\"pic\"\r\n          />\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {product.pStatus === \"Active\" ? (\r\n            <span className=\"bg-green-200 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {product.pStatus}\r\n            </span>\r\n          ) : (\r\n            <span className=\"bg-red-200 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {product.pStatus}\r\n            </span>\r\n          )}\r\n        </td>\r\n        <td className=\"p-2 text-center\">{product.pQuantity}</td>\r\n        <td className=\"p-2 text-center\">{product.pCategory.cName}</td>\r\n        <td className=\"p-2 text-center\">{product.pOffer}</td>\r\n        <td className=\"p-2 text-center\">\r\n          {moment(product.createdAt).format(\"lll\")}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {moment(product.updatedAt).format(\"lll\")}\r\n        </td>\r\n        <td className=\"p-2 flex items-center justify-center\">\r\n          <span\r\n            onClick={(e) => editProduct(product._id, product, true)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 fill-current text-green-500\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z\" />\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </span>\r\n          <span\r\n            onClick={(e) => deleteProduct(product._id)}\r\n            className=\"cursor-pointer hover:bg-gray-200 rounded-lg p-2 mx-1\"\r\n          >\r\n            <svg\r\n              className=\"w-6 h-6 fill-current text-red-500\"\r\n              fill=\"currentColor\"\r\n              viewBox=\"0 0 20 20\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </span>\r\n        </td>\r\n      </tr>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default AllProduct;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACxE,SAASC,aAAa,EAAEC,aAAa,QAAQ,YAAY;AACzD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,cAAc,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,UAAU,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC5B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGjB,UAAU,CAACM,cAAc,CAAC;EACrD,MAAM;IAAEY;EAAS,CAAC,GAAGF,IAAI;EAEzB,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACdoB,SAAS,CAAC,CAAC;IACX;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIE,YAAY,GAAG,MAAMnB,aAAa,CAAC,CAAC;IACxCoB,UAAU,CAAC,MAAM;MACf,IAAID,YAAY,IAAIA,YAAY,CAACE,QAAQ,EAAE;QACzCP,QAAQ,CAAC;UACPQ,IAAI,EAAE,6BAA6B;UACnCC,OAAO,EAAEJ,YAAY,CAACE;QACxB,CAAC,CAAC;QACFJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMO,gBAAgB,GAAG,MAAOC,GAAG,IAAK;IACtC,IAAIC,OAAO,GAAG,MAAMzB,aAAa,CAACwB,GAAG,CAAC;IACtC,IAAIC,OAAO,CAACC,KAAK,EAAE;MACjBC,OAAO,CAACC,GAAG,CAACH,OAAO,CAACC,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAID,OAAO,CAACI,OAAO,EAAE;MAC1BF,OAAO,CAACC,GAAG,CAACH,OAAO,CAACI,OAAO,CAAC;MAC5BZ,SAAS,CAAC,CAAC;IACb;EACF,CAAC;;EAED;EACA,MAAMa,WAAW,GAAGA,CAACN,GAAG,EAAEO,OAAO,EAAEV,IAAI,KAAK;IAC1C,IAAIA,IAAI,EAAE;MACRR,QAAQ,CAAC;QACPQ,IAAI,EAAE,sBAAsB;QAC5BU,OAAO,EAAE;UAAE,GAAGA,OAAO;UAAEP,GAAG,EAAEA;QAAI;MAClC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK4B,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnD7B,OAAA;QACE4B,SAAS,EAAC,sCAAsC;QAChDE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAJ,QAAA,eAElC7B,OAAA;UACEkC,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAC,GAAG;UACfC,CAAC,EAAC;QAA6G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzC,OAAA,CAACT,QAAQ;IAAAsC,QAAA,eACP7B,OAAA;MAAK4B,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9D7B,OAAA;QAAO4B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC9C7B,OAAA;UAAA6B,QAAA,eACE7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDzC,OAAA;cAAI4B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRzC,OAAA;UAAA6B,QAAA,EACGnB,QAAQ,IAAIA,QAAQ,CAACgC,MAAM,GAAG,CAAC,GAC9BhC,QAAQ,CAACiC,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;YAC1B,oBACE7C,OAAA,CAAC8C,YAAY;cACXnB,OAAO,EAAEiB,IAAK;cACdlB,WAAW,EAAEA,CAACN,GAAG,EAAEO,OAAO,EAAEV,IAAI,KAC9BS,WAAW,CAACN,GAAG,EAAEO,OAAO,EAAEV,IAAI,CAC/B;cACDrB,aAAa,EAAGwB,GAAG,IAAKD,gBAAgB,CAACC,GAAG;YAAE,GACzCyB,GAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAEN,CAAC,CAAC,gBAEFzC,OAAA;YAAA6B,QAAA,eACE7B,OAAA;cACE+C,OAAO,EAAC,IAAI;cACZnB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACnD;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACRzC,OAAA;QAAK4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,QACpC,EAACnB,QAAQ,IAAIA,QAAQ,CAACgC,MAAM,EAAC,gBACrC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;;AAED;AAAAlC,EAAA,CAtHMF,UAAU;AAAA2C,EAAA,GAAV3C,UAAU;AAuHhB,MAAMyC,YAAY,GAAGA,CAAC;EAAEnB,OAAO;EAAE/B,aAAa;EAAE8B;AAAY,CAAC,KAAK;EAChE,oBACE1B,OAAA,CAACT,QAAQ;IAAAsC,QAAA,eACP7B,OAAA;MAAA6B,QAAA,gBACE7B,OAAA;QAAI4B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC1BF,OAAO,CAACsB,KAAK,CAACP,MAAM,GAAG,EAAE,GACtBf,OAAO,CAACuB,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAC7CxB,OAAO,CAACsB;MAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC1BF,OAAO,CAACuB,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACrC;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC7B7B,OAAA;UACE4B,SAAS,EAAC,sCAAsC;UAChDyB,GAAG,EAAE,GAAGpD,MAAM,qBAAqB0B,OAAO,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAG;UACxDC,GAAG,EAAC;QAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5BF,OAAO,CAAC6B,OAAO,KAAK,QAAQ,gBAC3BxD,OAAA;UAAM4B,SAAS,EAAC,kEAAkE;UAAAC,QAAA,EAC/EF,OAAO,CAAC6B;QAAO;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,gBAEPzC,OAAA;UAAM4B,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAC7EF,OAAO,CAAC6B;QAAO;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEF,OAAO,CAAC8B;MAAS;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxDzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEF,OAAO,CAAC+B,SAAS,CAACC;MAAK;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9DzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEF,OAAO,CAACiC;MAAM;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrDzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5BhC,MAAM,CAAC8B,OAAO,CAACkC,SAAS,CAAC,CAACC,MAAM,CAAC,KAAK;MAAC;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5BhC,MAAM,CAAC8B,OAAO,CAACoC,SAAS,CAAC,CAACD,MAAM,CAAC,KAAK;MAAC;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACLzC,OAAA;QAAI4B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBAClD7B,OAAA;UACEgE,OAAO,EAAGC,CAAC,IAAKvC,WAAW,CAACC,OAAO,CAACuC,GAAG,EAAEvC,OAAO,EAAE,IAAI,CAAE;UACxDC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhE7B,OAAA;YACE4B,SAAS,EAAC,qCAAqC;YAC/CE,IAAI,EAAC,cAAc;YACnBE,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,gBAElC7B,OAAA;cAAMqC,CAAC,EAAC;YAA+E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FzC,OAAA;cACEmE,QAAQ,EAAC,SAAS;cAClB9B,CAAC,EAAC,wFAAwF;cAC1F+B,QAAQ,EAAC;YAAS;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzC,OAAA;UACEgE,OAAO,EAAGC,CAAC,IAAKrE,aAAa,CAAC+B,OAAO,CAACuC,GAAG,CAAE;UAC3CtC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhE7B,OAAA;YACE4B,SAAS,EAAC,mCAAmC;YAC7CE,IAAI,EAAC,cAAc;YACnBE,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,4BAA4B;YAAAJ,QAAA,eAElC7B,OAAA;cACEmE,QAAQ,EAAC,SAAS;cAClB9B,CAAC,EAAC,6MAA6M;cAC/M+B,QAAQ,EAAC;YAAS;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEf,CAAC;AAAC4B,GAAA,GA/EIvB,YAAY;AAiFlB,eAAezC,UAAU;AAAC,IAAA2C,EAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}