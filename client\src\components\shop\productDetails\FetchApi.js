import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const getSingleProduct = async (productId, userId = 1) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);

    // Convert new format to old format for compatibility
    const product = res.data;
    const convertedProduct = {
      _id: product.productId,
      pName: product.name,
      pDescription: product.description,
      pPrice: product.price,
      pImages: product.images || [],
      pCategory: { cName: product.category },
      pRatingsReviews: [], // Backend chưa có reviews
      pOffer: 0,
      pStatus: product.availabilityStatus === "AVAILABLE" ? "Active" : "Inactive",
      pQuantity: product.stockQuantity || 0,
      specifications: product.specifications,
      weight: product.weight,
      rushEligible: product.rushEligible
    };

    return { Product: convertedProduct };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const postAddReview = async (formData) => {
  try {
    // Backend chưa có review system
    console.log("⚠️ postAddReview: Backend chưa có review system");
    return { success: false, message: "Review system chưa được implement" };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const postDeleteReview = async (formData) => {
  try {
    // Backend chưa có review system
    console.log("⚠️ postDeleteReview: Backend chưa có review system");
    return { success: false, message: "Review system chưa được implement" };
  } catch (error) {
    console.log(error);
    throw error;
  }
};
