import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

// VNPay payment methods (thay thế BrainTree)
export const createVNPayPayment = async (orderId, amount, orderInfo) => {
  try {
    // Backend sử dụng VNPay thay vì BrainTree
    let res = await axios.post(`${apiURL}/api/v1/payment/${orderId}`, {
      amount: amount,
      orderInfo: orderInfo,
      returnUrl: `${window.location.origin}/payment/return`,
      cancelUrl: `${window.location.origin}/payment/cancel`
    });
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getPaymentStatus = async (orderId) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/payment/${orderId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Legacy methods for compatibility (deprecated)
export const getBrainTreeToken = async () => {
  console.log("⚠️ getBrainTreeToken: Deprecated - Backend sử dụng VNPay");
  return { error: "BrainTree không được hỗ trợ, sử dụng VNPay" };
};

export const getPaymentProcess = async (paymentData) => {
  console.log("⚠️ getPaymentProcess: Deprecated - Backend sử dụng VNPay");
  return { error: "BrainTree không được hỗ trợ, sử dụng VNPay" };
};

export const createOrder = async (cartData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Place order (confirm order)
export const placeOrder = async (orderData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Get order by ID
export const getOrderById = async (orderId) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Cancel order
export const cancelOrder = async (orderId) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Pay for order
export const payOrder = async (orderId, paymentData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
