import axios from "axios";
const apiURL = process.env.REACT_APP_API_URL;

export const getBrainTreeToken = async () => {
  let uId = JSON.parse(localStorage.getItem("jwt")).user._id;
  try {
    let res = await axios.post(`${apiURL}/api/braintree/get-token`, {
      uId: uId,
    });
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const getPaymentProcess = async (paymentData) => {
  try {
    let res = await axios.post(`${apiURL}/api/braintree/payment`, paymentData);
    return res.data;
  } catch (error) {
    console.log(error);
  }
};

export const createOrder = async (cartData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Place order (confirm order)
export const placeOrder = async (orderData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Get order by ID
export const getOrderById = async (orderId) => {
  try {
    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Cancel order
export const cancelOrder = async (orderId) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Pay for order
export const payOrder = async (orderId, paymentData) => {
  try {
    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);
    return res.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
