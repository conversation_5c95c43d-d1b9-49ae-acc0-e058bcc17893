{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\home\\\\OrderSuccessMessage.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext } from \"react\";\nimport { LayoutContext } from \"../layout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderSuccessMessage = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(LayoutContext);\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${data.orderSuccess ? \"\" : \"hidden\"} fixed bottom-0 flex justify-between items-center z-30 w-full bg-gray-800 text-white text-lg py-8 md:py-16 md:text-xl px-4 text-center`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"w-10/12 md:w-full\",\n        children: \"Your Order in process. Wait 2 days to deliver.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        onClick: e => dispatch({\n          type: \"orderSuccess\",\n          payload: false\n        }),\n        className: \"hover:bg-gray-400 hover:text-gray-800 p-2 rounded-full cursor-pointer\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderSuccessMessage, \"u1Wyv5LQ4WIhOvcjeGjdBzbywRs=\");\n_c = OrderSuccessMessage;\nexport default OrderSuccessMessage;\nvar _c;\n$RefreshReg$(_c, \"OrderSuccessMessage\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "LayoutContext", "jsxDEV", "_jsxDEV", "OrderSuccessMessage", "props", "_s", "data", "dispatch", "children", "className", "orderSuccess", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "e", "type", "payload", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/home/<USER>"], "sourcesContent": ["import React, { Fragment, useContext } from \"react\";\r\nimport { LayoutContext } from \"../layout\";\r\n\r\nconst OrderSuccessMessage = (props) => {\r\n  const { data, dispatch } = useContext(LayoutContext);\r\n  return (\r\n    <Fragment>\r\n      <div\r\n        className={`${\r\n          data.orderSuccess ? \"\" : \"hidden\"\r\n        } fixed bottom-0 flex justify-between items-center z-30 w-full bg-gray-800 text-white text-lg py-8 md:py-16 md:text-xl px-4 text-center`}\r\n      >\r\n        <span className=\"w-10/12 md:w-full\">\r\n          Your Order in process. Wait 2 days to deliver.\r\n        </span>\r\n        <span\r\n          onClick={(e) => dispatch({ type: \"orderSuccess\", payload: false })}\r\n          className=\"hover:bg-gray-400 hover:text-gray-800 p-2 rounded-full cursor-pointer\"\r\n        >\r\n          <svg\r\n            className=\"w-6 h-6\"\r\n            fill=\"currentColor\"\r\n            viewBox=\"0 0 20 20\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\r\n              clipRule=\"evenodd\"\r\n            />\r\n          </svg>\r\n        </span>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default OrderSuccessMessage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,aAAa,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,mBAAmB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACrC,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGR,UAAU,CAACC,aAAa,CAAC;EACpD,oBACEE,OAAA,CAACJ,QAAQ;IAAAU,QAAA,eACPN,OAAA;MACEO,SAAS,EAAE,GACTH,IAAI,CAACI,YAAY,GAAG,EAAE,GAAG,QAAQ,wIACsG;MAAAF,QAAA,gBAEzIN,OAAA;QAAMO,SAAS,EAAC,mBAAmB;QAAAD,QAAA,EAAC;MAEpC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPZ,OAAA;QACEa,OAAO,EAAGC,CAAC,IAAKT,QAAQ,CAAC;UAAEU,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAE;QACnET,SAAS,EAAC,uEAAuE;QAAAD,QAAA,eAEjFN,OAAA;UACEO,SAAS,EAAC,SAAS;UACnBU,IAAI,EAAC,cAAc;UACnBC,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,4BAA4B;UAAAb,QAAA,eAElCN,OAAA;YACEoB,QAAQ,EAAC,SAAS;YAClBC,CAAC,EAAC,oMAAoM;YACtMC,QAAQ,EAAC;UAAS;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACT,EAAA,CAhCIF,mBAAmB;AAAAsB,EAAA,GAAnBtB,mBAAmB;AAkCzB,eAAeA,mBAAmB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}