{"ast": null, "code": "import { getAllOrders, deleteOrder } from \"./FetchApi\";\nexport const fetchData = async dispatch => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  let responseData = await getAllOrders();\n  setTimeout(() => {\n    if (responseData && Array.isArray(responseData)) {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData\n      });\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  }, 1000);\n};\n\n/* This method call the editmodal & dispatch category context */\nexport const editOrderReq = (orderId, type, status, dispatch) => {\n  if (type) {\n    console.log(\"click update\");\n    dispatch({\n      type: \"updateOrderModalOpen\",\n      orderId: orderId,\n      status: status\n    });\n  }\n};\n\n/* Filter All Order */\nexport const filterOrder = async (type, data, dispatch, dropdown, setDropdown) => {\n  let status = type === \"All\" ? undefined : type;\n  let responseData = await getAllOrders(status);\n  if (responseData && Array.isArray(responseData)) {\n    dispatch({\n      type: \"fetchOrderAndChangeState\",\n      payload: responseData\n    });\n    setDropdown(!dropdown);\n  }\n};\n\n/* Delete Order */\nexport const deleteOrderReq = async (orderId, dispatch) => {\n  try {\n    const result = await deleteOrder(orderId);\n    if (result) {\n      // Refresh the orders list after successful deletion\n      fetchData(dispatch);\n      console.log(\"Order deleted successfully\");\n    }\n  } catch (error) {\n    console.error(\"Failed to delete order:\", error);\n  }\n};", "map": {"version": 3, "names": ["getAllOrders", "deleteOrder", "fetchData", "dispatch", "type", "payload", "responseData", "setTimeout", "Array", "isArray", "editOrderReq", "orderId", "status", "console", "log", "filterOrder", "data", "dropdown", "setDropdown", "undefined", "deleteOrderReq", "result", "error"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/Actions.js"], "sourcesContent": ["import { getAllOrders, deleteOrder } from \"./FetchApi\";\r\n\r\nexport const fetchData = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let responseData = await getAllOrders();\r\n  setTimeout(() => {\r\n    if (responseData && Array.isArray(responseData)) {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData,\r\n      });\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  }, 1000);\r\n};\r\n\r\n/* This method call the editmodal & dispatch category context */\r\nexport const editOrderReq = (orderId, type, status, dispatch) => {\r\n  if (type) {\r\n    console.log(\"click update\");\r\n    dispatch({ type: \"updateOrderModalOpen\", orderId: orderId, status: status });\r\n  }\r\n};\r\n\r\n/* Filter All Order */\r\nexport const filterOrder = async (\r\n  type,\r\n  data,\r\n  dispatch,\r\n  dropdown,\r\n  setDropdown\r\n) => {\r\n  let status = type === \"All\" ? undefined : type;\r\n  let responseData = await getAllOrders(status);\r\n  if (responseData && Array.isArray(responseData)) {\r\n    dispatch({\r\n      type: \"fetchOrderAndChangeState\",\r\n      payload: responseData,\r\n    });\r\n    setDropdown(!dropdown);\r\n  }\r\n};\r\n\r\n/* Delete Order */\r\nexport const deleteOrderReq = async (orderId, dispatch) => {\r\n  try {\r\n    const result = await deleteOrder(orderId);\r\n    if (result) {\r\n      // Refresh the orders list after successful deletion\r\n      fetchData(dispatch);\r\n      console.log(\"Order deleted successfully\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to delete order:\", error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,WAAW,QAAQ,YAAY;AAEtD,OAAO,MAAMC,SAAS,GAAG,MAAOC,QAAQ,IAAK;EAC3CA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAIC,YAAY,GAAG,MAAMN,YAAY,CAAC,CAAC;EACvCO,UAAU,CAAC,MAAM;IACf,IAAID,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;MAC/CH,QAAQ,CAAC;QACPC,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEC;MACX,CAAC,CAAC;MACFH,QAAQ,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA,OAAO,MAAMK,YAAY,GAAGA,CAACC,OAAO,EAAEP,IAAI,EAAEQ,MAAM,EAAET,QAAQ,KAAK;EAC/D,IAAIC,IAAI,EAAE;IACRS,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BX,QAAQ,CAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEO,OAAO,EAAEA,OAAO;MAAEC,MAAM,EAAEA;IAAO,CAAC,CAAC;EAC9E;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,WAAW,GAAG,MAAAA,CACzBX,IAAI,EACJY,IAAI,EACJb,QAAQ,EACRc,QAAQ,EACRC,WAAW,KACR;EACH,IAAIN,MAAM,GAAGR,IAAI,KAAK,KAAK,GAAGe,SAAS,GAAGf,IAAI;EAC9C,IAAIE,YAAY,GAAG,MAAMN,YAAY,CAACY,MAAM,CAAC;EAC7C,IAAIN,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;IAC/CH,QAAQ,CAAC;MACPC,IAAI,EAAE,0BAA0B;MAChCC,OAAO,EAAEC;IACX,CAAC,CAAC;IACFY,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,cAAc,GAAG,MAAAA,CAAOT,OAAO,EAAER,QAAQ,KAAK;EACzD,IAAI;IACF,MAAMkB,MAAM,GAAG,MAAMpB,WAAW,CAACU,OAAO,CAAC;IACzC,IAAIU,MAAM,EAAE;MACV;MACAnB,SAAS,CAACC,QAAQ,CAAC;MACnBU,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdT,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;EACjD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}