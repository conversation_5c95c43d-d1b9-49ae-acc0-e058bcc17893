{"ast": null, "code": "import DashboardAdmin from \"./dashboardAdmin\";\nimport Categories from \"./categories\";\nimport Products from \"./products\";\nimport Orders from \"./orders\";\nexport { DashboardAdmin, Categories, Products, Orders };", "map": {"version": 3, "names": ["DashboardAdmin", "Categories", "Products", "Orders"], "sources": ["D:/ITSS_Reference/client/src/components/admin/index.js"], "sourcesContent": ["import DashboardAdmin from \"./dashboardAdmin\";\r\nimport Categories from \"./categories\";\r\nimport Products from \"./products\";\r\nimport Orders from \"./orders\";\r\n\r\nexport { DashboardAdmin, Categories, Products, Orders };\r\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAE7B,SAASH,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}