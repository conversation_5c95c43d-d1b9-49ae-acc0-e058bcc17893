{"ast": null, "code": "import * as React from 'react';\nvar isBrowser = typeof document !== 'undefined';\nvar syncFallback = function syncFallback(create) {\n  return create();\n};\nvar useInsertionEffect = React['useInsertion' + 'Effect'] ? React['useInsertion' + 'Effect'] : false;\nvar useInsertionEffectAlwaysWithSyncFallback = !isBrowser ? syncFallback : useInsertionEffect || syncFallback;\nvar useInsertionEffectWithLayoutFallback = useInsertionEffect || React.useLayoutEffect;\nexport { useInsertionEffectAlwaysWithSyncFallback, useInsertionEffectWithLayoutFallback };", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "document", "syncFallback", "create", "useInsertionEffect", "useInsertionEffectAlwaysWithSyncFallback", "useInsertionEffectWithLayoutFallback", "useLayoutEffect"], "sources": ["D:/ITSS_Reference/client/node_modules/@emotion/use-insertion-effect-with-fallbacks/dist/emotion-use-insertion-effect-with-fallbacks.esm.js"], "sourcesContent": ["import * as React from 'react';\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar syncFallback = function syncFallback(create) {\n  return create();\n};\n\nvar useInsertionEffect = React['useInsertion' + 'Effect'] ? React['useInsertion' + 'Effect'] : false;\nvar useInsertionEffectAlwaysWithSyncFallback = !isBrowser ? syncFallback : useInsertionEffect || syncFallback;\nvar useInsertionEffectWithLayoutFallback = useInsertionEffect || React.useLayoutEffect;\n\nexport { useInsertionEffectAlwaysWithSyncFallback, useInsertionEffectWithLayoutFallback };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAIC,SAAS,GAAG,OAAOC,QAAQ,KAAK,WAAW;AAE/C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC/C,OAAOA,MAAM,CAAC,CAAC;AACjB,CAAC;AAED,IAAIC,kBAAkB,GAAGL,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC,GAAGA,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC,GAAG,KAAK;AACpG,IAAIM,wCAAwC,GAAG,CAACL,SAAS,GAAGE,YAAY,GAAGE,kBAAkB,IAAIF,YAAY;AAC7G,IAAII,oCAAoC,GAAGF,kBAAkB,IAAIL,KAAK,CAACQ,eAAe;AAEtF,SAASF,wCAAwC,EAAEC,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}