{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\TestProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { getAllProduct } from \"./admin/products/FetchApi\";\nimport { checkBackendHealth, logProductSample } from \"../utils/checkBackend\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log(\"Fetching products...\");\n      const data = await getAllProduct();\n      console.log(\"Raw API response:\", data);\n      if (Array.isArray(data)) {\n        setProducts(data);\n        console.log(`✅ Successfully loaded ${data.length} products`);\n      } else {\n        console.log(\"❌ API response is not an array:\", data);\n        setError(\"Invalid response format\");\n      }\n    } catch (err) {\n      console.error(\"❌ Error fetching products:\", err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-8 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xl\",\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-8 h-8 animate-spin mx-auto\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"2\",\n            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-8 text-center text-red-600\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xl\",\n        children: [\"Error: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchProducts,\n        className: \"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-6\",\n      children: \"Test Products Display\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 text-gray-600\",\n      children: [\"Found \", products.length, \" products\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: products.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border rounded-lg p-4 shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.jpg',\n            alt: product.name || 'Product',\n            className: \"w-full h-48 object-cover rounded\",\n            onError: e => {\n              e.target.src = '/placeholder-image.jpg';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-lg mb-2\",\n          children: product.name || 'No name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-sm mb-2\",\n          children: product.description || 'No description'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-green-600 mb-2\",\n          children: product.price ? `${product.price.toLocaleString('vi-VN')} VND` : 'Price not available'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"ID: \", product.productId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Category: \", product.category || 'No category']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Stock: \", product.stockQuantity || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), products.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-500 mt-8\",\n      children: \"No products found. Make sure the backend is running and has sample data.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(TestProducts, \"3+N/VFIgZOBgubN9oS5aTzm2qqY=\");\n_c = TestProducts;\nexport default TestProducts;\nvar _c;\n$RefreshReg$(_c, \"TestProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getAllProduct", "checkBackendHealth", "logProductSample", "jsxDEV", "_jsxDEV", "TestProducts", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "console", "log", "data", "Array", "isArray", "length", "err", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "map", "product", "index", "src", "images", "alt", "name", "onError", "e", "target", "description", "price", "toLocaleString", "productId", "category", "stockQuantity", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/TestProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { getAllProduct } from \"./admin/products/FetchApi\";\nimport { checkBackendHealth, logProductSample } from \"../utils/checkBackend\";\n\nconst TestProducts = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log(\"Fetching products...\");\n      const data = await getAllProduct();\n      console.log(\"Raw API response:\", data);\n      \n      if (Array.isArray(data)) {\n        setProducts(data);\n        console.log(`✅ Successfully loaded ${data.length} products`);\n      } else {\n        console.log(\"❌ API response is not an array:\", data);\n        setError(\"Invalid response format\");\n      }\n    } catch (err) {\n      console.error(\"❌ Error fetching products:\", err);\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"text-xl\">Loading products...</div>\n        <div className=\"mt-4\">\n          <svg className=\"w-8 h-8 animate-spin mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n          </svg>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"p-8 text-center text-red-600\">\n        <div className=\"text-xl\">Error: {error}</div>\n        <button \n          onClick={fetchProducts}\n          className=\"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-3xl font-bold mb-6\">Test Products Display</h1>\n      <div className=\"mb-4 text-gray-600\">\n        Found {products.length} products\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {products.map((product, index) => (\n          <div key={index} className=\"border rounded-lg p-4 shadow-md\">\n            <div className=\"mb-2\">\n              <img \n                src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder-image.jpg'}\n                alt={product.name || 'Product'}\n                className=\"w-full h-48 object-cover rounded\"\n                onError={(e) => {\n                  e.target.src = '/placeholder-image.jpg';\n                }}\n              />\n            </div>\n            <h3 className=\"font-semibold text-lg mb-2\">{product.name || 'No name'}</h3>\n            <p className=\"text-gray-600 text-sm mb-2\">{product.description || 'No description'}</p>\n            <div className=\"text-lg font-bold text-green-600 mb-2\">\n              {product.price ? `${product.price.toLocaleString('vi-VN')} VND` : 'Price not available'}\n            </div>\n            <div className=\"text-sm text-gray-500\">\n              <div>ID: {product.productId}</div>\n              <div>Category: {product.category || 'No category'}</div>\n              <div>Stock: {product.stockQuantity || 'N/A'}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {products.length === 0 && (\n        <div className=\"text-center text-gray-500 mt-8\">\n          No products found. Make sure the backend is running and has sample data.\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TestProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdc,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBI,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAMC,IAAI,GAAG,MAAMhB,aAAa,CAAC,CAAC;MAClCc,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,IAAI,CAAC;MAEtC,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvBR,WAAW,CAACQ,IAAI,CAAC;QACjBF,OAAO,CAACC,GAAG,CAAC,yBAAyBC,IAAI,CAACG,MAAM,WAAW,CAAC;MAC9D,CAAC,MAAM;QACLL,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,IAAI,CAAC;QACpDJ,QAAQ,CAAC,yBAAyB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZN,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAES,GAAG,CAAC;MAChDR,QAAQ,CAACQ,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKkB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnB,OAAA;QAAKkB,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClDvB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnB,OAAA;UAAKkB,SAAS,EAAC,8BAA8B;UAACM,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAP,QAAA,eACjGnB,OAAA;YAAM2B,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAC,GAAG;YAACC,CAAC,EAAC;UAA6G;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIhB,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKkB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CnB,OAAA;QAAKkB,SAAS,EAAC,SAAS;QAAAC,QAAA,GAAC,SAAO,EAACZ,KAAK;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CvB,OAAA;QACE+B,OAAO,EAAEtB,aAAc;QACvBS,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC5E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEvB,OAAA;IAAKkB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBnB,OAAA;MAAIkB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClEvB,OAAA;MAAKkB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAAC,QAC5B,EAAChB,QAAQ,CAACY,MAAM,EAAC,WACzB;IAAA;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFhB,QAAQ,CAAC6B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BlC,OAAA;QAAiBkB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC1DnB,OAAA;UAAKkB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnB,OAAA;YACEmC,GAAG,EAAEF,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACG,MAAM,CAACrB,MAAM,GAAG,CAAC,GAAGkB,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,GAAG,wBAAyB;YAChGC,GAAG,EAAEJ,OAAO,CAACK,IAAI,IAAI,SAAU;YAC/BpB,SAAS,EAAC,kCAAkC;YAC5CqB,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wBAAwB;YACzC;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvB,OAAA;UAAIkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEc,OAAO,CAACK,IAAI,IAAI;QAAS;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3EvB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEc,OAAO,CAACS,WAAW,IAAI;QAAgB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvFvB,OAAA;UAAKkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDc,OAAO,CAACU,KAAK,GAAG,GAAGV,OAAO,CAACU,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,MAAM,GAAG;QAAqB;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCnB,OAAA;YAAAmB,QAAA,GAAK,MAAI,EAACc,OAAO,CAACY,SAAS;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCvB,OAAA;YAAAmB,QAAA,GAAK,YAAU,EAACc,OAAO,CAACa,QAAQ,IAAI,aAAa;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDvB,OAAA;YAAAmB,QAAA,GAAK,SAAO,EAACc,OAAO,CAACc,aAAa,IAAI,KAAK;UAAA;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA,GApBEW,KAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELpB,QAAQ,CAACY,MAAM,KAAK,CAAC,iBACpBf,OAAA;MAAKkB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrB,EAAA,CAnGID,YAAY;AAAA+C,EAAA,GAAZ/C,YAAY;AAqGlB,eAAeA,YAAY;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}