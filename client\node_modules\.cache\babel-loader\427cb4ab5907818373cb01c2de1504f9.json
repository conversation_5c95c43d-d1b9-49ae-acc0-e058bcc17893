{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get wishlist products - Legacy function (may need backend implementation)\nexport const wishListProducts = async () => {\n  console.warn(\"wishListProducts: This endpoint may not be available in the current backend\");\n  let productArray = JSON.parse(localStorage.getItem(\"wishList\"));\n  try {\n    let res = await axios.post(`${apiURL}/api/product/wish-product`, {\n      productArray\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Alternative: Get multiple products by IDs using the existing product endpoint\nexport const getProductsByIds = async (userId, productIds) => {\n  try {\n    // Since there's no specific wishlist endpoint, we can get products individually\n    // or implement a batch get endpoint in the backend\n    const promises = productIds.map(productId => axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`));\n    const responses = await Promise.all(promises);\n    return responses.map(res => res.data);\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "wishListProducts", "console", "warn", "productArray", "JSON", "parse", "localStorage", "getItem", "res", "post", "data", "error", "log", "getProductsByIds", "userId", "productIds", "promises", "map", "productId", "get", "responses", "Promise", "all"], "sources": ["D:/ITSS_Reference/client/src/components/shop/wishlist/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// Get wishlist products - Legacy function (may need backend implementation)\r\nexport const wishListProducts = async () => {\r\n  console.warn(\"wishListProducts: This endpoint may not be available in the current backend\");\r\n  let productArray = JSON.parse(localStorage.getItem(\"wishList\"));\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/wish-product`, {\r\n      productArray,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Alternative: Get multiple products by IDs using the existing product endpoint\r\nexport const getProductsByIds = async (userId, productIds) => {\r\n  try {\r\n    // Since there's no specific wishlist endpoint, we can get products individually\r\n    // or implement a batch get endpoint in the backend\r\n    const promises = productIds.map(productId =>\r\n      axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`)\r\n    );\r\n\r\n    const responses = await Promise.all(promises);\r\n    return responses.map(res => res.data);\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1CC,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;EAC3F,IAAIC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;EAC/D,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,2BAA2B,EAAE;MAC/DO;IACF,CAAC,CAAC;IACF,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdV,OAAO,CAACW,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,UAAU,KAAK;EAC5D,IAAI;IACF;IACA;IACA,MAAMC,QAAQ,GAAGD,UAAU,CAACE,GAAG,CAACC,SAAS,IACvCvB,KAAK,CAACwB,GAAG,CAAC,GAAGvB,MAAM,6BAA6BsB,SAAS,WAAWJ,MAAM,EAAE,CAC9E,CAAC;IAED,MAAMM,SAAS,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC;IAC7C,OAAOI,SAAS,CAACH,GAAG,CAACT,GAAG,IAAIA,GAAG,CAACE,IAAI,CAAC;EACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdV,OAAO,CAACW,GAAG,CAACD,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}