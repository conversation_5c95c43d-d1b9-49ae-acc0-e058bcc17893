{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n  var valueFromConfig2Keys = ['url', 'method', 'data'];\n  var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy', 'params'];\n  var defaultToConfig2Keys = ['baseURL', 'transformRequest', 'transformResponse', 'paramsSerializer', 'timeout', 'timeoutMessage', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName', 'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress', 'decompress', 'maxContentLength', 'maxBodyLength', 'maxRedirects', 'transport', 'httpAgent', 'httpsAgent', 'cancelToken', 'socketPath', 'responseEncoding'];\n  var directMergeKeys = ['validateStatus'];\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  }\n  utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    }\n  });\n  utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);\n  utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n  utils.forEach(directMergeKeys, function merge(prop) {\n    if (prop in config2) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n  var axiosKeys = valueFromConfig2Keys.concat(mergeDeepPropertiesKeys).concat(defaultToConfig2Keys).concat(directMergeKeys);\n  var otherKeys = Object.keys(config1).concat(Object.keys(config2)).filter(function filterAxiosKeys(key) {\n    return axiosKeys.indexOf(key) === -1;\n  });\n  utils.forEach(otherKeys, mergeDeepProperties);\n  return config;\n};", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "mergeConfig", "config1", "config2", "config", "valueFromConfig2Keys", "mergeDeepPropertiesKeys", "defaultToConfig2Keys", "directMergeKeys", "getMergedValue", "target", "source", "isPlainObject", "merge", "isArray", "slice", "mergeDeepProperties", "prop", "isUndefined", "undefined", "for<PERSON>ach", "valueFromConfig2", "defaultToConfig2", "axios<PERSON><PERSON><PERSON>", "concat", "otherKeys", "Object", "keys", "filter", "filterAxiosKeys", "key", "indexOf"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  var valueFromConfig2Keys = ['url', 'method', 'data'];\n  var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy', 'params'];\n  var defaultToConfig2Keys = [\n    'baseURL', 'transformRequest', 'transformResponse', 'paramsSerializer',\n    'timeout', 'timeoutMessage', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName',\n    'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress', 'decompress',\n    'maxContentLength', 'maxBodyLength', 'maxRedirects', 'transport', 'httpAgent',\n    'httpsAgent', 'cancelToken', 'socketPath', 'responseEncoding'\n  ];\n  var directMergeKeys = ['validateStatus'];\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    }\n  });\n\n  utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);\n\n  utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  utils.forEach(directMergeKeys, function merge(prop) {\n    if (prop in config2) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  var axiosKeys = valueFromConfig2Keys\n    .concat(mergeDeepPropertiesKeys)\n    .concat(defaultToConfig2Keys)\n    .concat(directMergeKeys);\n\n  var otherKeys = Object\n    .keys(config1)\n    .concat(Object.keys(config2))\n    .filter(function filterAxiosKeys(key) {\n      return axiosKeys.indexOf(key) === -1;\n    });\n\n  utils.forEach(otherKeys, mergeDeepProperties);\n\n  return config;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtD;EACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,MAAM,GAAG,CAAC,CAAC;EAEf,IAAIC,oBAAoB,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;EACpD,IAAIC,uBAAuB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;EACpE,IAAIC,oBAAoB,GAAG,CACzB,SAAS,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,kBAAkB,EACtE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAC3F,gBAAgB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,YAAY,EACxE,kBAAkB,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAC7E,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,kBAAkB,CAC9D;EACD,IAAIC,eAAe,GAAG,CAAC,gBAAgB,CAAC;EAExC,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE;IACtC,IAAId,KAAK,CAACe,aAAa,CAACF,MAAM,CAAC,IAAIb,KAAK,CAACe,aAAa,CAACD,MAAM,CAAC,EAAE;MAC9D,OAAOd,KAAK,CAACgB,KAAK,CAACH,MAAM,EAAEC,MAAM,CAAC;IACpC,CAAC,MAAM,IAAId,KAAK,CAACe,aAAa,CAACD,MAAM,CAAC,EAAE;MACtC,OAAOd,KAAK,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC;IAChC,CAAC,MAAM,IAAId,KAAK,CAACiB,OAAO,CAACH,MAAM,CAAC,EAAE;MAChC,OAAOA,MAAM,CAACI,KAAK,CAAC,CAAC;IACvB;IACA,OAAOJ,MAAM;EACf;EAEA,SAASK,mBAAmBA,CAACC,IAAI,EAAE;IACjC,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACf,OAAO,CAACc,IAAI,CAAC,CAAC,EAAE;MACrCb,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACP,OAAO,CAACe,IAAI,CAAC,EAAEd,OAAO,CAACc,IAAI,CAAC,CAAC;IAC7D,CAAC,MAAM,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC,EAAE;MAC5Cb,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACU,SAAS,EAAEjB,OAAO,CAACe,IAAI,CAAC,CAAC;IACzD;EACF;EAEApB,KAAK,CAACuB,OAAO,CAACf,oBAAoB,EAAE,SAASgB,gBAAgBA,CAACJ,IAAI,EAAE;IAClE,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACf,OAAO,CAACc,IAAI,CAAC,CAAC,EAAE;MACrCb,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACU,SAAS,EAAEhB,OAAO,CAACc,IAAI,CAAC,CAAC;IACzD;EACF,CAAC,CAAC;EAEFpB,KAAK,CAACuB,OAAO,CAACd,uBAAuB,EAAEU,mBAAmB,CAAC;EAE3DnB,KAAK,CAACuB,OAAO,CAACb,oBAAoB,EAAE,SAASe,gBAAgBA,CAACL,IAAI,EAAE;IAClE,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACf,OAAO,CAACc,IAAI,CAAC,CAAC,EAAE;MACrCb,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACU,SAAS,EAAEhB,OAAO,CAACc,IAAI,CAAC,CAAC;IACzD,CAAC,MAAM,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC,EAAE;MAC5Cb,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACU,SAAS,EAAEjB,OAAO,CAACe,IAAI,CAAC,CAAC;IACzD;EACF,CAAC,CAAC;EAEFpB,KAAK,CAACuB,OAAO,CAACZ,eAAe,EAAE,SAASK,KAAKA,CAACI,IAAI,EAAE;IAClD,IAAIA,IAAI,IAAId,OAAO,EAAE;MACnBC,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACP,OAAO,CAACe,IAAI,CAAC,EAAEd,OAAO,CAACc,IAAI,CAAC,CAAC;IAC7D,CAAC,MAAM,IAAIA,IAAI,IAAIf,OAAO,EAAE;MAC1BE,MAAM,CAACa,IAAI,CAAC,GAAGR,cAAc,CAACU,SAAS,EAAEjB,OAAO,CAACe,IAAI,CAAC,CAAC;IACzD;EACF,CAAC,CAAC;EAEF,IAAIM,SAAS,GAAGlB,oBAAoB,CACjCmB,MAAM,CAAClB,uBAAuB,CAAC,CAC/BkB,MAAM,CAACjB,oBAAoB,CAAC,CAC5BiB,MAAM,CAAChB,eAAe,CAAC;EAE1B,IAAIiB,SAAS,GAAGC,MAAM,CACnBC,IAAI,CAACzB,OAAO,CAAC,CACbsB,MAAM,CAACE,MAAM,CAACC,IAAI,CAACxB,OAAO,CAAC,CAAC,CAC5ByB,MAAM,CAAC,SAASC,eAAeA,CAACC,GAAG,EAAE;IACpC,OAAOP,SAAS,CAACQ,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;EACtC,CAAC,CAAC;EAEJjC,KAAK,CAACuB,OAAO,CAACK,SAAS,EAAET,mBAAmB,CAAC;EAE7C,OAAOZ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}