{"ast": null, "code": "import axios from \"axios\";\nconst API_URL = process.env.REACT_APP_API_URL || \"http://localhost:8080\";\nexport const checkBackendHealth = async () => {\n  try {\n    console.log(\"🔍 Checking backend health...\");\n\n    // Test basic connectivity\n    const response = await axios.get(`${API_URL}/api/v1/products/customer`, {\n      timeout: 5000\n    });\n    console.log(\"✅ Backend is running!\");\n    console.log(`📊 Found ${response.data.length} products`);\n    return {\n      isRunning: true,\n      productCount: response.data.length,\n      data: response.data\n    };\n  } catch (error) {\n    var _error$response;\n    console.error(\"❌ Backend check failed:\", error.message);\n    if (error.code === 'ECONNREFUSED') {\n      console.log(\"💡 Backend server is not running. Please start it with: ./mvnw spring-boot:run\");\n    } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n      console.log(\"💡 API endpoint not found. Check if the backend has the correct endpoints.\");\n    } else {\n      console.log(\"💡 Unknown error. Check backend logs.\");\n    }\n    return {\n      isRunning: false,\n      error: error.message\n    };\n  }\n};\nexport const logProductSample = products => {\n  if (products && products.length > 0) {\n    console.log(\"📦 Sample product structure:\");\n    console.log(JSON.stringify(products[0], null, 2));\n  }\n};", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "checkBackendHealth", "console", "log", "response", "get", "timeout", "data", "length", "isRunning", "productCount", "error", "_error$response", "message", "code", "status", "logProductSample", "products", "JSON", "stringify"], "sources": ["D:/ITSS_Reference/client/src/utils/checkBackend.js"], "sourcesContent": ["import axios from \"axios\";\n\nconst API_URL = process.env.REACT_APP_API_URL || \"http://localhost:8080\";\n\nexport const checkBackendHealth = async () => {\n  try {\n    console.log(\"🔍 Checking backend health...\");\n    \n    // Test basic connectivity\n    const response = await axios.get(`${API_URL}/api/v1/products/customer`, {\n      timeout: 5000\n    });\n    \n    console.log(\"✅ Backend is running!\");\n    console.log(`📊 Found ${response.data.length} products`);\n    \n    return {\n      isRunning: true,\n      productCount: response.data.length,\n      data: response.data\n    };\n  } catch (error) {\n    console.error(\"❌ Backend check failed:\", error.message);\n    \n    if (error.code === 'ECONNREFUSED') {\n      console.log(\"💡 Backend server is not running. Please start it with: ./mvnw spring-boot:run\");\n    } else if (error.response?.status === 404) {\n      console.log(\"💡 API endpoint not found. Check if the backend has the correct endpoints.\");\n    } else {\n      console.log(\"💡 Unknown error. Check backend logs.\");\n    }\n    \n    return {\n      isRunning: false,\n      error: error.message\n    };\n  }\n};\n\nexport const logProductSample = (products) => {\n  if (products && products.length > 0) {\n    console.log(\"📦 Sample product structure:\");\n    console.log(JSON.stringify(products[0], null, 2));\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAExE,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;IAE5C;IACA,MAAMC,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,OAAO,2BAA2B,EAAE;MACtES,OAAO,EAAE;IACX,CAAC,CAAC;IAEFJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpCD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACG,IAAI,CAACC,MAAM,WAAW,CAAC;IAExD,OAAO;MACLC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAEN,QAAQ,CAACG,IAAI,CAACC,MAAM;MAClCD,IAAI,EAAEH,QAAQ,CAACG;IACjB,CAAC;EACH,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA,IAAAC,eAAA;IACdV,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAACE,OAAO,CAAC;IAEvD,IAAIF,KAAK,CAACG,IAAI,KAAK,cAAc,EAAE;MACjCZ,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;IAC/F,CAAC,MAAM,IAAI,EAAAS,eAAA,GAAAD,KAAK,CAACP,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;MACzCb,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IAC3F,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACtD;IAEA,OAAO;MACLM,SAAS,EAAE,KAAK;MAChBE,KAAK,EAAEA,KAAK,CAACE;IACf,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAIC,QAAQ,IAAK;EAC5C,IAAIA,QAAQ,IAAIA,QAAQ,CAACT,MAAM,GAAG,CAAC,EAAE;IACnCN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAACe,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACnD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}