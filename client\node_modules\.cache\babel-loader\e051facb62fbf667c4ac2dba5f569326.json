{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getBrainTreeToken = async () => {\n  let uId = JSON.parse(localStorage.getItem(\"jwt\")).user._id;\n  try {\n    let res = await axios.post(`${apiURL}/api/braintree/get-token`, {\n      uId: uId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const getPaymentProcess = async paymentData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/braintree/payment`, paymentData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const createOrder = async cartData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Place order (confirm order)\nexport const placeOrder = async orderData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get order by ID\nexport const getOrderById = async orderId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Cancel order\nexport const cancelOrder = async orderId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Pay for order\nexport const payOrder = async (orderId, paymentData) => {\n  try {\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getBrainTreeToken", "uId", "JSON", "parse", "localStorage", "getItem", "user", "_id", "res", "post", "data", "error", "console", "log", "getPaymentProcess", "paymentData", "createOrder", "cartData", "placeOrder", "orderData", "getOrderById", "orderId", "get", "cancelOrder", "payOrder"], "sources": ["D:/ITSS_Reference/client/src/components/shop/order/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getBrainTreeToken = async () => {\r\n  let uId = JSON.parse(localStorage.getItem(\"jwt\")).user._id;\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/braintree/get-token`, {\r\n      uId: uId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const getPaymentProcess = async (paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/braintree/payment`, paymentData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const createOrder = async (cartData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/order/create`, cartData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Place order (confirm order)\r\nexport const placeOrder = async (orderData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/order/place`, orderData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get order by ID\r\nexport const getOrderById = async (orderId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/order/${orderId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Cancel order\r\nexport const cancelOrder = async (orderId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/cancel`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Pay for order\r\nexport const payOrder = async (orderId, paymentData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/v1/order/${orderId}/pay`, paymentData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG;EAC1D,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,0BAA0B,EAAE;MAC9DK,GAAG,EAAEA;IACP,CAAC,CAAC;IACF,OAAOO,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAG,MAAOC,WAAW,IAAK;EACtD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,wBAAwB,EAAEmB,WAAW,CAAC;IAC1E,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAOC,QAAQ,IAAK;EAC7C,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,4BAA4B,EAAEqB,QAAQ,CAAC;IAC3E,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,UAAU,GAAG,MAAOC,SAAS,IAAK;EAC7C,IAAI;IACF,IAAIX,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,2BAA2B,EAAEuB,SAAS,CAAC;IAC3E,OAAOX,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,IAAIb,GAAG,GAAG,MAAMb,KAAK,CAAC2B,GAAG,CAAC,GAAG1B,MAAM,iBAAiByB,OAAO,EAAE,CAAC;IAC9D,OAAOb,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,WAAW,GAAG,MAAOF,OAAO,IAAK;EAC5C,IAAI;IACF,IAAIb,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,iBAAiByB,OAAO,SAAS,CAAC;IACtE,OAAOb,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,QAAQ,GAAG,MAAAA,CAAOH,OAAO,EAAEN,WAAW,KAAK;EACtD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMb,KAAK,CAACc,IAAI,CAAC,GAAGb,MAAM,iBAAiByB,OAAO,MAAM,EAAEN,WAAW,CAAC;IAChF,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}