{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\layout\\\\index.js\";\nimport React, { Fragment, createContext } from \"react\";\nimport { Navber, Footer, CartModal } from \"../partials\";\nimport LoginSignup from \"../auth/LoginSignup\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LayoutContext = /*#__PURE__*/createContext();\nconst Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow\",\n      children: [/*#__PURE__*/_jsxDEV(Navber, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoginSignup, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Fragment", "createContext", "<PERSON><PERSON><PERSON>", "Footer", "CartModal", "LoginSignup", "jsxDEV", "_jsxDEV", "LayoutContext", "Layout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/layout/index.js"], "sourcesContent": ["import React, { Fragment, createContext } from \"react\";\r\nimport { Navber, Footer, CartModal } from \"../partials\";\r\nimport LoginSignup from \"../auth/LoginSignup\";\r\n\r\nexport const LayoutContext = createContext();\r\n\r\nconst Layout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <div className=\"flex-grow\">\r\n        <Navber />\r\n        <LoginSignup />\r\n        <CartModal />\r\n        {/* All Children pass from here */}\r\n        {children}\r\n      </div>\r\n      <Footer />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AACtD,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,aAAa;AACvD,OAAOC,WAAW,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,aAAa,gBAAGP,aAAa,CAAC,CAAC;AAE5C,MAAMQ,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC/B,oBACEH,OAAA,CAACP,QAAQ;IAAAU,QAAA,gBACPH,OAAA;MAAKI,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBH,OAAA,CAACL,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVR,OAAA,CAACF,WAAW;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfR,OAAA,CAACH,SAAS;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEZL,QAAQ;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNR,OAAA,CAACJ,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACC,EAAA,GAbIP,MAAM;AAeZ,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}