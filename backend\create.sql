create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_content varchar(255), transaction_id varchar(255) generated by default as identity, transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_id));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_code varchar(255) generated by default as identity, transaction_content varchar(255), transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_code));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_code varchar(255) generated by default as identity, transaction_content varchar(255), transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_code));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
create table audit_log (role smallint check (role between 0 and 1), user_id integer, audit_log_id bigint generated by default as identity, product_id bigint, timestamp timestamp(6), action_type varchar(255) check (action_type in ('LOGIN','LOGOUT','CHANGE_PASSWORD','PLACE_ORDER','VIEW_ORDER','PAY_ORDER','CANCEL_ORDER','ORDER_ACTION','SEARCH_PRODUCTS','VIEW_PRODUCT','ADD_PRODUCT','DELETE_PRODUCT','UPDATE_PRODUCT')), keyword varchar(255), primary key (audit_log_id));
create table cart (total float4, user_id integer not null, cart_id bigint generated by default as identity, primary key (cart_id));
create table cart_item (product_price float4, quantity integer, cart_id bigint not null, product_id bigint not null, primary key (cart_id, product_id));
create table category (category_id integer generated by default as identity, category_description varchar(255), category_name varchar(255) not null, primary key (category_id));
create table delivery_info (delivery_info_id bigint generated by default as identity, address varchar(255), email varchar(255), phone_number varchar(255), province_city varchar(255), recipient_name varchar(255), shipping_instruction varchar(255), primary key (delivery_info_id));
create table order (customer_id integer not null, discount float4, is_rush_order boolean, shipping_fee float4, subtotal float4, total float4, created_timestamp timestamp(6), delivery_info_id bigint not null, order_id bigint generated by default as identity, order_status varchar(255) check (order_status in ('PENDING','CONFIRMED','SHIPPED','DELIVERED','CANCELLED')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), primary key (order_id));
create table order_item (quantity integer, unit_price float4, order_id bigint, order_item_id bigint generated by default as identity, product_id bigint, primary key (order_item_id));
create table payment_transaction (total_amount float4, order_id bigint unique, refund_timestamp timestamp(6), transaction_timestamp timestamp(6), payment_method varchar(255) check (payment_method in ('VNPAY','COD','PAYPAL')), payment_status varchar(255) check (payment_status in ('UNPAID','PAID','REFUNDED','FAILED')), refund_status varchar(255) check (refund_status in ('NOT_REQUESTED','REQUESTED','REFUNDED','FAILED')), transaction_code varchar(255) generated by default as identity, transaction_content varchar(255), transaction_type varchar(255) check (transaction_type in ('PAYMENT','REFUND')), primary key (transaction_code));
create table product (category_id integer not null, product_price float4, product_value float4, stock_quantity integer, product_id bigint generated by default as identity, warehouse_entry_timestamp timestamp(6), product_barcode varchar(255), product_description varchar(255), product_name varchar(255) not null, product_specification varchar(255), product_status varchar(255) check (product_status in ('ACTIVE','INACTIVE','OUT_OF_STOCK','DISCONTINUED','DRAFT','PENDING_APPROVAL','DELETED','ARCHIVED')), primary key (product_id));
create table product_edit_history (editor_user_id integer, edit_id bigint generated by default as identity, edit_timestamp timestamp(6), product_id bigint not null, changes_made varchar(255), primary key (edit_id));
create table product_image (image_id bigint generated by default as identity, product_id bigint, image_url varchar(255), primary key (image_id));
create table product_review (rating integer, user_id integer not null, product_id bigint not null, review_id bigint generated by default as identity, review_timestamp timestamp(6), review_comment varchar(255), primary key (review_id));
create table related_product (product_id bigint, related_product_id bigint, relation_id bigint generated by default as identity, relation_type varchar(255) check (relation_type in ('SIMILAR','FREQUENTLY_BOUGHT_TOGETHER','ACCESSORY','RECOMMENDED')), primary key (relation_id));
create table user_account (user_id integer generated by default as identity, create_at timestamp(6) not null, username varchar(30) not null unique, password varchar(50) not null, email varchar(100) not null unique, role varchar(255) not null check (role in ('CUSTOMER','MANAGER')), primary key (user_id));
alter table if exists audit_log add constraint FKj3rvh1kvv15ks8wdev3mnh960 foreign key (product_id) references product;
alter table if exists audit_log add constraint FK7sviolp1kqlltq032ekwqjmx5 foreign key (user_id) references user_account;
alter table if exists cart add constraint FKl0ul1sjqu139invpvonhyf0t0 foreign key (user_id) references user_account;
alter table if exists cart_item add constraint FK1uobyhgl1wvgt1jpccia8xxs3 foreign key (cart_id) references cart;
alter table if exists cart_item add constraint FKjcyd5wv4igqnw413rgxbfu4nv foreign key (product_id) references product;
alter table if exists order add constraint FK7s60cht41fp936hg24tx0vwpf foreign key (delivery_info_id) references delivery_info;
alter table if exists order add constraint FKk4gqvsyjjo6nn7untqmn9pvpx foreign key (customer_id) references user_account;
alter table if exists order_item add constraint FKt6wv8m7eshksp5kp8w4b2d1dm foreign key (order_id) references order;
alter table if exists order_item add constraint FK551losx9j75ss5d6bfsqvijna foreign key (product_id) references product;
alter table if exists payment_transaction add constraint FKdtet9jgdooh9crikf40loel1a foreign key (order_id) references order;
alter table if exists product add constraint FK1mtsbur82frn64de7balymq9s foreign key (category_id) references category;
alter table if exists product_edit_history add constraint FKd3kfxjqto9powie59p87g0txg foreign key (editor_user_id) references user_account;
alter table if exists product_edit_history add constraint FKs5y9yjvcebyr281if97rwriqp foreign key (product_id) references product;
alter table if exists product_image add constraint FK6oo0cvcdtb6qmwsga468uuukk foreign key (product_id) references product;
alter table if exists product_review add constraint FKkaqmhakwt05p3n0px81b9pdya foreign key (product_id) references product;
alter table if exists product_review add constraint FKhehh4gyaenb2gtie80dmsc4qu foreign key (user_id) references user_account;
alter table if exists related_product add constraint FK5jxg8cww7s70r3u2mpel5eqr5 foreign key (product_id) references product;
alter table if exists related_product add constraint FKpft39pw1n1qbuyg2gqwc5m6a8 foreign key (related_product_id) references product;
