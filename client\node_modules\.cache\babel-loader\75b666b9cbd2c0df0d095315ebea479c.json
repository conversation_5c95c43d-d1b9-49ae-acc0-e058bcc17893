{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// Get all products for customer view\nexport const getAllProduct = async () => {\n  try {\n    // This endpoint is public, no userId required for customer view\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\n    return res.data;\n  } catch (error) {\n    console.log(\"Error fetching products:\", error);\n    throw error;\n  }\n};\n\n// Get all products for manager view\nexport const getAllProductManager = async userId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get product details for customer\nexport const getProductDetails = async (userId, productId) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get product details for manager\nexport const getProductDetailsManager = async (userId, productId) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Search products for customer\nexport const searchProducts = async (userId, keyword) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/search?userId=${userId}&keyword=${keyword}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Search products for manager\nexport const searchProductsManager = async (userId, keyword) => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/manager/search?userId=${userId}&keyword=${keyword}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Get related products\nexport const getRelatedProducts = async productId => {\n  try {\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Create new product (Manager only)\nexport const createProduct = async (userId, productData) => {\n  try {\n    const payload = {\n      category: productData.pCategory,\n      name: productData.pName,\n      description: productData.pDescription,\n      weight: productData.pWeight || 0,\n      rushEligible: productData.pRushEligible || false,\n      barcode: productData.pBarcode || \"\",\n      price: parseFloat(productData.pPrice),\n      specifications: productData.pSpecifications || \"\",\n      images: productData.pImages || []\n    };\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, payload, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Update existing product (Manager only)\nexport const editProduct = async (userId, productId, productData) => {\n  try {\n    const payload = {\n      category: productData.pCategory,\n      name: productData.pName,\n      description: productData.pDescription,\n      weight: productData.pWeight || 0,\n      rushEligible: productData.pRushEligible || false,\n      barcode: productData.pBarcode || \"\",\n      price: parseFloat(productData.pPrice),\n      specifications: productData.pSpecifications || \"\",\n      images: productData.pImages || []\n    };\n    let res = await axios.put(`${apiURL}/api/v1/products/${productId}?userId=${userId}`, payload, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Delete product (Manager only)\nexport const deleteProduct = async (userId, productId) => {\n  try {\n    let res = await axios.delete(`${apiURL}/api/v1/products/${productId}?userId=${userId}`);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Legacy functions for backward compatibility - these may need to be updated based on actual backend implementation\nexport const productByCategory = async catId => {\n  try {\n    // Note: This endpoint may not exist in the current backend\n    // You may need to use the search functionality instead\n    console.warn(\"productByCategory: This endpoint may not be available in the current backend\");\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\n      catId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\nexport const productByPrice = async price => {\n  try {\n    // Note: This endpoint may not exist in the current backend\n    // You may need to use the search functionality instead\n    console.warn(\"productByPrice: This endpoint may not be available in the current backend\");\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\n      price\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};\n\n// Helper function for image upload (if needed)\nexport const createProductImage = async ({\n  pImage\n}) => {\n  /* Most important part for uploading multiple image  */\n  let formData = new FormData();\n  for (const file of pImage) {\n    formData.append(\"pImage\", file);\n  }\n  /* Most important part for uploading multiple image  */\n\n  try {\n    // Note: You may need to implement an image upload endpoint in the backend\n    console.warn(\"createProductImage: Image upload endpoint may need to be implemented\");\n    let res = await axios.post(`${apiURL}/api/v1/upload/images`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getAllProduct", "res", "get", "data", "error", "console", "log", "getAllProductManager", "userId", "getProductDetails", "productId", "getProductDetailsManager", "searchProducts", "keyword", "searchProductsManager", "getRelatedProducts", "createProduct", "productData", "payload", "category", "pCategory", "name", "pName", "description", "pDescription", "weight", "pWeight", "rushEligible", "pRushEligible", "barcode", "pBarcode", "price", "parseFloat", "pPrice", "specifications", "pSpecifications", "images", "pImages", "post", "headers", "editProduct", "put", "deleteProduct", "delete", "productByCategory", "catId", "warn", "productByPrice", "createProductImage", "pImage", "formData", "FormData", "file", "append"], "sources": ["D:/ITSS_Reference/client/src/components/admin/products/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// Get all products for customer view\r\nexport const getAllProduct = async () => {\r\n  try {\r\n    // This endpoint is public, no userId required for customer view\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(\"Error fetching products:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get all products for manager view\r\nexport const getAllProductManager = async (userId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get product details for customer\r\nexport const getProductDetails = async (userId, productId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/${productId}?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get product details for manager\r\nexport const getProductDetailsManager = async (userId, productId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager/${productId}?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Search products for customer\r\nexport const searchProducts = async (userId, keyword) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/customer/search?userId=${userId}&keyword=${keyword}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Search products for manager\r\nexport const searchProductsManager = async (userId, keyword) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/manager/search?userId=${userId}&keyword=${keyword}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Get related products\r\nexport const getRelatedProducts = async (productId) => {\r\n  try {\r\n    let res = await axios.get(`${apiURL}/api/v1/products/${productId}/related`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create new product (Manager only)\r\nexport const createProduct = async (userId, productData) => {\r\n  try {\r\n    const payload = {\r\n      category: productData.pCategory,\r\n      name: productData.pName,\r\n      description: productData.pDescription,\r\n      weight: productData.pWeight || 0,\r\n      rushEligible: productData.pRushEligible || false,\r\n      barcode: productData.pBarcode || \"\",\r\n      price: parseFloat(productData.pPrice),\r\n      specifications: productData.pSpecifications || \"\",\r\n      images: productData.pImages || []\r\n    };\r\n\r\n    let res = await axios.post(`${apiURL}/api/v1/products?userId=${userId}`, payload, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update existing product (Manager only)\r\nexport const editProduct = async (userId, productId, productData) => {\r\n  try {\r\n    const payload = {\r\n      category: productData.pCategory,\r\n      name: productData.pName,\r\n      description: productData.pDescription,\r\n      weight: productData.pWeight || 0,\r\n      rushEligible: productData.pRushEligible || false,\r\n      barcode: productData.pBarcode || \"\",\r\n      price: parseFloat(productData.pPrice),\r\n      specifications: productData.pSpecifications || \"\",\r\n      images: productData.pImages || []\r\n    };\r\n\r\n    let res = await axios.put(`${apiURL}/api/v1/products/${productId}?userId=${userId}`, payload, {\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete product (Manager only)\r\nexport const deleteProduct = async (userId, productId) => {\r\n  try {\r\n    let res = await axios.delete(`${apiURL}/api/v1/products/${productId}?userId=${userId}`);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Legacy functions for backward compatibility - these may need to be updated based on actual backend implementation\r\nexport const productByCategory = async (catId) => {\r\n  try {\r\n    // Note: This endpoint may not exist in the current backend\r\n    // You may need to use the search functionality instead\r\n    console.warn(\"productByCategory: This endpoint may not be available in the current backend\");\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-category`, {\r\n      catId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const productByPrice = async (price) => {\r\n  try {\r\n    // Note: This endpoint may not exist in the current backend\r\n    // You may need to use the search functionality instead\r\n    console.warn(\"productByPrice: This endpoint may not be available in the current backend\");\r\n    let res = await axios.post(`${apiURL}/api/product/product-by-price`, {\r\n      price,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Helper function for image upload (if needed)\r\nexport const createProductImage = async ({ pImage }) => {\r\n  /* Most important part for uploading multiple image  */\r\n  let formData = new FormData();\r\n  for (const file of pImage) {\r\n    formData.append(\"pImage\", file);\r\n  }\r\n  /* Most important part for uploading multiple image  */\r\n\r\n  try {\r\n    // Note: You may need to implement an image upload endpoint in the backend\r\n    console.warn(\"createProductImage: Image upload endpoint may need to be implemented\");\r\n    let res = await axios.post(`${apiURL}/api/v1/upload/images`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF;IACA,IAAIC,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,2BAA2B,CAAC;IAC/D,OAAOK,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,KAAK,CAAC;IAC9C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,oBAAoB,GAAG,MAAOC,MAAM,IAAK;EACpD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,mCAAmCY,MAAM,EAAE,CAAC;IAC/E,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAOD,MAAM,EAAEE,SAAS,KAAK;EAC5D,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,6BAA6Bc,SAAS,WAAWF,MAAM,EAAE,CAAC;IAC7F,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,wBAAwB,GAAG,MAAAA,CAAOH,MAAM,EAAEE,SAAS,KAAK;EACnE,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,4BAA4Bc,SAAS,WAAWF,MAAM,EAAE,CAAC;IAC5F,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,cAAc,GAAG,MAAAA,CAAOJ,MAAM,EAAEK,OAAO,KAAK;EACvD,IAAI;IACF,IAAIZ,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,2CAA2CY,MAAM,YAAYK,OAAO,EAAE,CAAC;IAC1G,OAAOZ,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,qBAAqB,GAAG,MAAAA,CAAON,MAAM,EAAEK,OAAO,KAAK;EAC9D,IAAI;IACF,IAAIZ,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,0CAA0CY,MAAM,YAAYK,OAAO,EAAE,CAAC;IACzG,OAAOZ,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,kBAAkB,GAAG,MAAOL,SAAS,IAAK;EACrD,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,MAAM,oBAAoBc,SAAS,UAAU,CAAC;IAC3E,OAAOT,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,aAAa,GAAG,MAAAA,CAAOR,MAAM,EAAES,WAAW,KAAK;EAC1D,IAAI;IACF,MAAMC,OAAO,GAAG;MACdC,QAAQ,EAAEF,WAAW,CAACG,SAAS;MAC/BC,IAAI,EAAEJ,WAAW,CAACK,KAAK;MACvBC,WAAW,EAAEN,WAAW,CAACO,YAAY;MACrCC,MAAM,EAAER,WAAW,CAACS,OAAO,IAAI,CAAC;MAChCC,YAAY,EAAEV,WAAW,CAACW,aAAa,IAAI,KAAK;MAChDC,OAAO,EAAEZ,WAAW,CAACa,QAAQ,IAAI,EAAE;MACnCC,KAAK,EAAEC,UAAU,CAACf,WAAW,CAACgB,MAAM,CAAC;MACrCC,cAAc,EAAEjB,WAAW,CAACkB,eAAe,IAAI,EAAE;MACjDC,MAAM,EAAEnB,WAAW,CAACoB,OAAO,IAAI;IACjC,CAAC;IAED,IAAIpC,GAAG,GAAG,MAAMN,KAAK,CAAC2C,IAAI,CAAC,GAAG1C,MAAM,2BAA2BY,MAAM,EAAE,EAAEU,OAAO,EAAE;MAChFqB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOtC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMoC,WAAW,GAAG,MAAAA,CAAOhC,MAAM,EAAEE,SAAS,EAAEO,WAAW,KAAK;EACnE,IAAI;IACF,MAAMC,OAAO,GAAG;MACdC,QAAQ,EAAEF,WAAW,CAACG,SAAS;MAC/BC,IAAI,EAAEJ,WAAW,CAACK,KAAK;MACvBC,WAAW,EAAEN,WAAW,CAACO,YAAY;MACrCC,MAAM,EAAER,WAAW,CAACS,OAAO,IAAI,CAAC;MAChCC,YAAY,EAAEV,WAAW,CAACW,aAAa,IAAI,KAAK;MAChDC,OAAO,EAAEZ,WAAW,CAACa,QAAQ,IAAI,EAAE;MACnCC,KAAK,EAAEC,UAAU,CAACf,WAAW,CAACgB,MAAM,CAAC;MACrCC,cAAc,EAAEjB,WAAW,CAACkB,eAAe,IAAI,EAAE;MACjDC,MAAM,EAAEnB,WAAW,CAACoB,OAAO,IAAI;IACjC,CAAC;IAED,IAAIpC,GAAG,GAAG,MAAMN,KAAK,CAAC8C,GAAG,CAAC,GAAG7C,MAAM,oBAAoBc,SAAS,WAAWF,MAAM,EAAE,EAAEU,OAAO,EAAE;MAC5FqB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOtC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMsC,aAAa,GAAG,MAAAA,CAAOlC,MAAM,EAAEE,SAAS,KAAK;EACxD,IAAI;IACF,IAAIT,GAAG,GAAG,MAAMN,KAAK,CAACgD,MAAM,CAAC,GAAG/C,MAAM,oBAAoBc,SAAS,WAAWF,MAAM,EAAE,CAAC;IACvF,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMwC,iBAAiB,GAAG,MAAOC,KAAK,IAAK;EAChD,IAAI;IACF;IACA;IACAxC,OAAO,CAACyC,IAAI,CAAC,8EAA8E,CAAC;IAC5F,IAAI7C,GAAG,GAAG,MAAMN,KAAK,CAAC2C,IAAI,CAAC,GAAG1C,MAAM,kCAAkC,EAAE;MACtEiD;IACF,CAAC,CAAC;IACF,OAAO5C,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2C,cAAc,GAAG,MAAOhB,KAAK,IAAK;EAC7C,IAAI;IACF;IACA;IACA1B,OAAO,CAACyC,IAAI,CAAC,2EAA2E,CAAC;IACzF,IAAI7C,GAAG,GAAG,MAAMN,KAAK,CAAC2C,IAAI,CAAC,GAAG1C,MAAM,+BAA+B,EAAE;MACnEmC;IACF,CAAC,CAAC;IACF,OAAO9B,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4C,kBAAkB,GAAG,MAAAA,CAAO;EAAEC;AAAO,CAAC,KAAK;EACtD;EACA,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC7B,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;EACjC;EACA;;EAEA,IAAI;IACF;IACA/C,OAAO,CAACyC,IAAI,CAAC,sEAAsE,CAAC;IACpF,IAAI7C,GAAG,GAAG,MAAMN,KAAK,CAAC2C,IAAI,CAAC,GAAG1C,MAAM,uBAAuB,EAAEsD,QAAQ,EAAE;MACrEX,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOtC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}