{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\productDetails\\\\ProductDetailsSection.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useState, useEffect, useContext } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { ProductDetailsContext } from \"./index\";\nimport { LayoutContext } from \"../layout\";\nimport Submenu from \"./Submenu\";\nimport ProductDetailsSectionTwo from \"./ProductDetailsSectionTwo\";\nimport { getSingleProduct } from \"./FetchApi\";\nimport { getCartByUser } from \"../partials/FetchApi\";\nimport { isWishReq, unWishReq, isWish } from \"../home/<USER>\";\nimport { updateQuantity, slideImage, addToCart, cartList } from \"./Mixins\";\nimport { totalCost } from \"../partials/Mixins\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst ProductDetailsSection = props => {\n  _s();\n  let {\n    id\n  } = useParams();\n  const {\n    data,\n    dispatch\n  } = useContext(ProductDetailsContext);\n  const {\n    data: layoutData,\n    dispatch: layoutDispatch\n  } = useContext(LayoutContext); // Layout Context\n\n  const sProduct = layoutData.singleProductDetail;\n  const [pImages, setPimages] = useState(null);\n  const [count, setCount] = useState(0); // Slide change state\n\n  const [quantitiy, setQuantitiy] = useState(1); // Increse and decrese quantity state\n  const [, setAlertq] = useState(false); // Alert when quantity greater than stock\n\n  const [wList, setWlist] = useState(JSON.parse(localStorage.getItem(\"wishList\"))); // Wishlist State Control\n\n  useEffect(() => {\n    fetchData();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchData = async () => {\n    dispatch({\n      type: \"loading\",\n      payload: true\n    });\n    try {\n      let responseData = await getSingleProduct(id);\n      setTimeout(() => {\n        // Backend returns product data directly, not wrapped in Product object\n        if (responseData && responseData.productId) {\n          // Map backend DTO to frontend expected format\n          const productData = {\n            _id: responseData.productId,\n            pName: responseData.name,\n            pDescription: responseData.description,\n            pPrice: responseData.price,\n            pImages: responseData.images || ['/placeholder-product.jpg'],\n            pQuantity: responseData.quantity || 0,\n            pCategory: {\n              _id: responseData.categoryId || 1,\n              cName: responseData.category || 'Unknown Category'\n            },\n            pRatingsReviews: responseData.reviews || []\n          };\n          layoutDispatch({\n            type: \"singleProductDetail\",\n            payload: productData\n          }); // Dispatch in layout context\n          setPimages(productData.pImages);\n          dispatch({\n            type: \"loading\",\n            payload: false\n          });\n          layoutDispatch({\n            type: \"inCart\",\n            payload: cartList()\n          }); // This function change cart in cart state\n        } else if (responseData && responseData.Product) {\n          // Fallback for old format\n          layoutDispatch({\n            type: \"singleProductDetail\",\n            payload: responseData.Product\n          });\n          setPimages(responseData.Product.pImages);\n          dispatch({\n            type: \"loading\",\n            payload: false\n          });\n          layoutDispatch({\n            type: \"inCart\",\n            payload: cartList()\n          });\n        } else {\n          console.log(\"No product data found\");\n          dispatch({\n            type: \"loading\",\n            payload: false\n          });\n        }\n        if (responseData && responseData.error) {\n          console.log(responseData.error);\n        }\n      }, 500);\n    } catch (error) {\n      console.log(\"Error fetching product:\", error);\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n    fetchCartProduct(); // Updating cart total\n  };\n  const fetchCartProduct = async () => {\n    try {\n      var _JSON$parse$user, _JSON$parse$user2;\n      // Get current user ID from localStorage\n      const jwt = localStorage.getItem(\"jwt\");\n      const userId = jwt ? ((_JSON$parse$user = JSON.parse(jwt).user) === null || _JSON$parse$user === void 0 ? void 0 : _JSON$parse$user.id) || ((_JSON$parse$user2 = JSON.parse(jwt).user) === null || _JSON$parse$user2 === void 0 ? void 0 : _JSON$parse$user2._id) : null;\n      if (userId) {\n        let responseData = await getCartByUser(userId);\n        if (responseData && responseData.success && responseData.data) {\n          // Backend returns cart with items array\n          const cartItems = responseData.data.items || [];\n          layoutDispatch({\n            type: \"cartProduct\",\n            payload: cartItems\n          }); // Layout context Cartproduct fetch and dispatch\n        }\n      } else {\n        // Fallback to localStorage cart if no user logged in\n        const localCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n        layoutDispatch({\n          type: \"cartProduct\",\n          payload: localCart\n        });\n      }\n    } catch (error) {\n      console.log(\"Error fetching cart:\", error);\n      // Fallback to localStorage cart on error\n      const localCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n      layoutDispatch({\n        type: \"cartProduct\",\n        payload: localCart\n      });\n    }\n  };\n  if (data.loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 animate-spin text-gray-600\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  } else if (!sProduct) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No product\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Submenu, {\n      value: {\n        categoryId: sProduct.pCategory._id,\n        product: sProduct.pName,\n        category: sProduct.pCategory.cName\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"m-4 md:mx-12 md:my-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block md:col-span-1 md:flex md:flex-col md:space-y-4 md:mr-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            onClick: e => slideImage(\"increase\", 0, count, setCount, pImages),\n            className: `${count === 0 ? \"\" : \"opacity-25\"} cursor-pointer w-20 h-20 object-cover object-center`,\n            src: `${apiURL}/uploads/products/${sProduct.pImages[0]}`,\n            alt: \"pic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            onClick: e => slideImage(\"increase\", 1, count, setCount, pImages),\n            className: `${count === 1 ? \"\" : \"opacity-25\"} cursor-pointer w-20 h-20 object-cover object-center`,\n            src: `${apiURL}/uploads/products/${sProduct.pImages[1]}`,\n            alt: \"pic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-2 md:col-span-7\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"w-full\",\n              src: `${apiURL}/uploads/products/${sProduct.pImages[count]}`,\n              alt: \"Pic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex justify-between items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                onClick: e => slideImage(\"increase\", null, count, setCount, pImages),\n                className: \"flex justify-center  w-12 h-12 text-gray-700 opacity-25 cursor-pointer hover:text-yellow-700 hover:opacity-100\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M15 19l-7-7 7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                onClick: e => slideImage(\"increase\", null, count, setCount, pImages),\n                className: \"flex justify-center  w-12 h-12 text-gray-700 opacity-25 cursor-pointer hover:text-yellow-700 hover:opacity-100\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 5l7 7-7 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-2 mt-8 md:mt-0 md:col-span-4 md:ml-6 lg:ml-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col leading-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl tracking-wider\",\n              children: sProduct.pName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl tracking-wider text-yellow-700\",\n                children: [\"$\", sProduct.pPrice, \".00\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  onClick: e => isWishReq(e, sProduct._id, setWlist),\n                  className: `${isWish(sProduct._id, wList) && \"hidden\"} w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700`,\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  onClick: e => unWishReq(e, sProduct._id, setWlist),\n                  className: `${!isWish(sProduct._id, wList) && \"hidden\"} w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700`,\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 md:my-6 text-gray-600\",\n            children: sProduct.pDescription\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 md:my-6\",\n            children: [+quantitiy === +sProduct.pQuantity ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-red-500\",\n              children: \"Stock limited\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this) : \"\", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex justify-between items-center px-4 py-2 border ${+quantitiy === +sProduct.pQuantity && \"border-red-500\"}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `${quantitiy === sProduct.pQuantity && \"text-red-500\"}`,\n                children: \"Quantity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), sProduct.pQuantity !== 0 ? /*#__PURE__*/_jsxDEV(Fragment, {\n                children: layoutData.inCart == null || layoutData.inCart !== null && layoutData.inCart.includes(sProduct._id) === false ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: e => updateQuantity(\"decrease\", sProduct.pQuantity, quantitiy, setQuantitiy, setAlertq),\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 fill-current cursor-pointer\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: quantitiy\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: e => updateQuantity(\"increase\", sProduct.pQuantity, quantitiy, setQuantitiy, setAlertq),\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 fill-current cursor-pointer\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 fill-current cursor-not-allowed\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: quantitiy\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 fill-current cursor-not-allowed\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 fill-current cursor-not-allowed\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: quantitiy\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 fill-current cursor-not-allowed\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), sProduct.pQuantity !== 0 ? /*#__PURE__*/_jsxDEV(Fragment, {\n              children: layoutData.inCart !== null && layoutData.inCart.includes(sProduct._id) === true ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: \"#303031\"\n                },\n                className: `px-4 py-2 text-white text-center cursor-not-allowed uppercase opacity-75`,\n                children: \"In cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: e => addToCart(sProduct._id, quantitiy, sProduct.pPrice, layoutDispatch, setQuantitiy, setAlertq, fetchData, totalCost),\n                style: {\n                  background: \"#303031\"\n                },\n                className: `px-4 py-2 text-white text-center cursor-pointer uppercase`,\n                children: \"Add to cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Fragment, {\n              children: layoutData.inCart !== null && layoutData.inCart.includes(sProduct._id) === true ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: \"#303031\"\n                },\n                className: `px-4 py-2 text-white text-center cursor-not-allowed uppercase opacity-75`,\n                children: \"In cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: \"#303031\"\n                },\n                disabled: true,\n                className: \"px-4 py-2 text-white opacity-50 cursor-not-allowed text-center uppercase\",\n                children: \"Out of stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductDetailsSectionTwo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailsSection, \"U6DQq8XWiw71PQ7g3MCvqO03V1g=\", false, function () {\n  return [useParams];\n});\n_c = ProductDetailsSection;\nexport default ProductDetailsSection;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsSection\");", "map": {"version": 3, "names": ["React", "Fragment", "useState", "useEffect", "useContext", "useParams", "ProductDetailsContext", "LayoutContext", "Submenu", "ProductDetailsSectionTwo", "getSingleProduct", "getCartByUser", "isWishReq", "unWishReq", "isWish", "updateQuantity", "slideImage", "addToCart", "cartList", "totalCost", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "ProductDetailsSection", "props", "_s", "id", "data", "dispatch", "layoutData", "layoutDispatch", "sProduct", "singleProductDetail", "pImages", "setPimages", "count", "setCount", "quantitiy", "setQuantitiy", "setAlertq", "wList", "setWlist", "JSON", "parse", "localStorage", "getItem", "fetchData", "type", "payload", "responseData", "setTimeout", "productId", "productData", "_id", "pName", "name", "pDescription", "description", "pPrice", "price", "images", "pQuantity", "quantity", "pCategory", "categoryId", "cName", "category", "pRatingsReviews", "reviews", "Product", "console", "log", "error", "fetchCartProduct", "_JSON$parse$user", "_JSON$parse$user2", "jwt", "userId", "user", "success", "cartItems", "items", "localCart", "loading", "className", "children", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "product", "onClick", "e", "src", "alt", "fillRule", "clipRule", "inCart", "includes", "style", "background", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/ProductDetailsSection.js"], "sourcesContent": ["import React, { Fragment, useState, useEffect, useContext } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { ProductDetailsContext } from \"./index\";\r\nimport { LayoutContext } from \"../layout\";\r\nimport Submenu from \"./Submenu\";\r\nimport ProductDetailsSectionTwo from \"./ProductDetailsSectionTwo\";\r\n\r\nimport { getSingleProduct } from \"./FetchApi\";\r\nimport { getCartByUser } from \"../partials/FetchApi\";\r\n\r\nimport { isWishReq, unWishReq, isWish } from \"../home/<USER>\";\r\nimport { updateQuantity, slideImage, addToCart, cartList } from \"./Mixins\";\r\nimport { totalCost } from \"../partials/Mixins\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst ProductDetailsSection = (props) => {\r\n  let { id } = useParams();\r\n\r\n  const { data, dispatch } = useContext(ProductDetailsContext);\r\n  const { data: layoutData, dispatch: layoutDispatch } =\r\n    useContext(LayoutContext); // Layout Context\r\n\r\n  const sProduct = layoutData.singleProductDetail;\r\n  const [pImages, setPimages] = useState(null);\r\n  const [count, setCount] = useState(0); // Slide change state\r\n\r\n  const [quantitiy, setQuantitiy] = useState(1); // Increse and decrese quantity state\r\n  const [, setAlertq] = useState(false); // Alert when quantity greater than stock\r\n\r\n  const [wList, setWlist] = useState(\r\n    JSON.parse(localStorage.getItem(\"wishList\"))\r\n  ); // Wishlist State Control\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      let responseData = await getSingleProduct(id);\r\n      setTimeout(() => {\r\n        // Backend returns product data directly, not wrapped in Product object\r\n        if (responseData && responseData.productId) {\r\n          // Map backend DTO to frontend expected format\r\n          const productData = {\r\n            _id: responseData.productId,\r\n            pName: responseData.name,\r\n            pDescription: responseData.description,\r\n            pPrice: responseData.price,\r\n            pImages: responseData.images || ['/placeholder-product.jpg'],\r\n            pQuantity: responseData.quantity || 0,\r\n            pCategory: {\r\n              _id: responseData.categoryId || 1,\r\n              cName: responseData.category || 'Unknown Category'\r\n            },\r\n            pRatingsReviews: responseData.reviews || []\r\n          };\r\n\r\n          layoutDispatch({\r\n            type: \"singleProductDetail\",\r\n            payload: productData,\r\n          }); // Dispatch in layout context\r\n          setPimages(productData.pImages);\r\n          dispatch({ type: \"loading\", payload: false });\r\n          layoutDispatch({ type: \"inCart\", payload: cartList() }); // This function change cart in cart state\r\n        } else if (responseData && responseData.Product) {\r\n          // Fallback for old format\r\n          layoutDispatch({\r\n            type: \"singleProductDetail\",\r\n            payload: responseData.Product,\r\n          });\r\n          setPimages(responseData.Product.pImages);\r\n          dispatch({ type: \"loading\", payload: false });\r\n          layoutDispatch({ type: \"inCart\", payload: cartList() });\r\n        } else {\r\n          console.log(\"No product data found\");\r\n          dispatch({ type: \"loading\", payload: false });\r\n        }\r\n        if (responseData && responseData.error) {\r\n          console.log(responseData.error);\r\n        }\r\n      }, 500);\r\n    } catch (error) {\r\n      console.log(\"Error fetching product:\", error);\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n    fetchCartProduct(); // Updating cart total\r\n  };\r\n\r\n  const fetchCartProduct = async () => {\r\n    try {\r\n      // Get current user ID from localStorage\r\n      const jwt = localStorage.getItem(\"jwt\");\r\n      const userId = jwt ? JSON.parse(jwt).user?.id || JSON.parse(jwt).user?._id : null;\r\n\r\n      if (userId) {\r\n        let responseData = await getCartByUser(userId);\r\n        if (responseData && responseData.success && responseData.data) {\r\n          // Backend returns cart with items array\r\n          const cartItems = responseData.data.items || [];\r\n          layoutDispatch({ type: \"cartProduct\", payload: cartItems }); // Layout context Cartproduct fetch and dispatch\r\n        }\r\n      } else {\r\n        // Fallback to localStorage cart if no user logged in\r\n        const localCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\r\n        layoutDispatch({ type: \"cartProduct\", payload: localCart });\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error fetching cart:\", error);\r\n      // Fallback to localStorage cart on error\r\n      const localCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\r\n      layoutDispatch({ type: \"cartProduct\", payload: localCart });\r\n    }\r\n  };\r\n\r\n  if (data.loading) {\r\n    return (\r\n      <div className=\"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center h-screen\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  } else if (!sProduct) {\r\n    return <div>No product</div>;\r\n  }\r\n  return (\r\n    <Fragment>\r\n      <Submenu\r\n        value={{\r\n          categoryId: sProduct.pCategory._id,\r\n          product: sProduct.pName,\r\n          category: sProduct.pCategory.cName,\r\n        }}\r\n      />\r\n      <section className=\"m-4 md:mx-12 md:my-6\">\r\n        <div className=\"grid grid-cols-2 md:grid-cols-12\">\r\n          <div className=\"hidden md:block md:col-span-1 md:flex md:flex-col md:space-y-4 md:mr-2\">\r\n            <img\r\n              onClick={(e) =>\r\n                slideImage(\"increase\", 0, count, setCount, pImages)\r\n              }\r\n              className={`${\r\n                count === 0 ? \"\" : \"opacity-25\"\r\n              } cursor-pointer w-20 h-20 object-cover object-center`}\r\n              src={`${apiURL}/uploads/products/${sProduct.pImages[0]}`}\r\n              alt=\"pic\"\r\n            />\r\n            <img\r\n              onClick={(e) =>\r\n                slideImage(\"increase\", 1, count, setCount, pImages)\r\n              }\r\n              className={`${\r\n                count === 1 ? \"\" : \"opacity-25\"\r\n              } cursor-pointer w-20 h-20 object-cover object-center`}\r\n              src={`${apiURL}/uploads/products/${sProduct.pImages[1]}`}\r\n              alt=\"pic\"\r\n            />\r\n          </div>\r\n          <div className=\"col-span-2 md:col-span-7\">\r\n            <div className=\"relative\">\r\n              <img\r\n                className=\"w-full\"\r\n                src={`${apiURL}/uploads/products/${sProduct.pImages[count]}`}\r\n                alt=\"Pic\"\r\n              />\r\n              <div className=\"absolute inset-0 flex justify-between items-center mb-4\">\r\n                <svg\r\n                  onClick={(e) =>\r\n                    slideImage(\"increase\", null, count, setCount, pImages)\r\n                  }\r\n                  className=\"flex justify-center  w-12 h-12 text-gray-700 opacity-25 cursor-pointer hover:text-yellow-700 hover:opacity-100\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M15 19l-7-7 7-7\"\r\n                  />\r\n                </svg>\r\n                <svg\r\n                  onClick={(e) =>\r\n                    slideImage(\"increase\", null, count, setCount, pImages)\r\n                  }\r\n                  className=\"flex justify-center  w-12 h-12 text-gray-700 opacity-25 cursor-pointer hover:text-yellow-700 hover:opacity-100\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M9 5l7 7-7 7\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"col-span-2 mt-8 md:mt-0 md:col-span-4 md:ml-6 lg:ml-12\">\r\n            <div className=\"flex flex-col leading-8\">\r\n              <div className=\"text-2xl tracking-wider\">{sProduct.pName}</div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-xl tracking-wider text-yellow-700\">\r\n                  ${sProduct.pPrice}.00\r\n                </span>\r\n                <span>\r\n                  <svg\r\n                    onClick={(e) => isWishReq(e, sProduct._id, setWlist)}\r\n                    className={`${\r\n                      isWish(sProduct._id, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700`}\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\r\n                    />\r\n                  </svg>\r\n                  <svg\r\n                    onClick={(e) => unWishReq(e, sProduct._id, setWlist)}\r\n                    className={`${\r\n                      !isWish(sProduct._id, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700`}\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"my-4 md:my-6 text-gray-600\">\r\n              {sProduct.pDescription}\r\n            </div>\r\n            <div className=\"my-4 md:my-6\">\r\n              {+quantitiy === +sProduct.pQuantity ? (\r\n                <span className=\"text-xs text-red-500\">Stock limited</span>\r\n              ) : (\r\n                \"\"\r\n              )}\r\n              <div\r\n                className={`flex justify-between items-center px-4 py-2 border ${\r\n                  +quantitiy === +sProduct.pQuantity && \"border-red-500\"\r\n                }`}\r\n              >\r\n                <div\r\n                  className={`${\r\n                    quantitiy === sProduct.pQuantity && \"text-red-500\"\r\n                  }`}\r\n                >\r\n                  Quantity\r\n                </div>\r\n                {/* Quantity Button */}\r\n                {sProduct.pQuantity !== 0 ? (\r\n                  <Fragment>\r\n                    {layoutData.inCart == null ||\r\n                    (layoutData.inCart !== null &&\r\n                      layoutData.inCart.includes(sProduct._id) === false) ? (\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span\r\n                          onClick={(e) =>\r\n                            updateQuantity(\r\n                              \"decrease\",\r\n                              sProduct.pQuantity,\r\n                              quantitiy,\r\n                              setQuantitiy,\r\n                              setAlertq\r\n                            )\r\n                          }\r\n                        >\r\n                          <svg\r\n                            className=\"w-5 h-5 fill-current cursor-pointer\"\r\n                            fill=\"currentColor\"\r\n                            viewBox=\"0 0 20 20\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                          >\r\n                            <path\r\n                              fillRule=\"evenodd\"\r\n                              d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\"\r\n                              clipRule=\"evenodd\"\r\n                            />\r\n                          </svg>\r\n                        </span>\r\n                        <span className=\"font-semibold\">{quantitiy}</span>\r\n                        <span\r\n                          onClick={(e) =>\r\n                            updateQuantity(\r\n                              \"increase\",\r\n                              sProduct.pQuantity,\r\n                              quantitiy,\r\n                              setQuantitiy,\r\n                              setAlertq\r\n                            )\r\n                          }\r\n                        >\r\n                          <svg\r\n                            className=\"w-5 h-5 fill-current cursor-pointer\"\r\n                            fill=\"currentColor\"\r\n                            viewBox=\"0 0 20 20\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                          >\r\n                            <path\r\n                              fillRule=\"evenodd\"\r\n                              d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\r\n                              clipRule=\"evenodd\"\r\n                            />\r\n                          </svg>\r\n                        </span>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span>\r\n                          <svg\r\n                            className=\"w-5 h-5 fill-current cursor-not-allowed\"\r\n                            fill=\"currentColor\"\r\n                            viewBox=\"0 0 20 20\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                          >\r\n                            <path\r\n                              fillRule=\"evenodd\"\r\n                              d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\"\r\n                              clipRule=\"evenodd\"\r\n                            />\r\n                          </svg>\r\n                        </span>\r\n                        <span className=\"font-semibold\">{quantitiy}</span>\r\n                        <span>\r\n                          <svg\r\n                            className=\"w-5 h-5 fill-current cursor-not-allowed\"\r\n                            fill=\"currentColor\"\r\n                            viewBox=\"0 0 20 20\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                          >\r\n                            <path\r\n                              fillRule=\"evenodd\"\r\n                              d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\r\n                              clipRule=\"evenodd\"\r\n                            />\r\n                          </svg>\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </Fragment>\r\n                ) : (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span>\r\n                      <svg\r\n                        className=\"w-5 h-5 fill-current cursor-not-allowed\"\r\n                        fill=\"currentColor\"\r\n                        viewBox=\"0 0 20 20\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <path\r\n                          fillRule=\"evenodd\"\r\n                          d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\"\r\n                          clipRule=\"evenodd\"\r\n                        />\r\n                      </svg>\r\n                    </span>\r\n                    <span className=\"font-semibold\">{quantitiy}</span>\r\n                    <span>\r\n                      <svg\r\n                        className=\"w-5 h-5 fill-current cursor-not-allowed\"\r\n                        fill=\"currentColor\"\r\n                        viewBox=\"0 0 20 20\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <path\r\n                          fillRule=\"evenodd\"\r\n                          d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\r\n                          clipRule=\"evenodd\"\r\n                        />\r\n                      </svg>\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                {/* Quantity Button End */}\r\n              </div>\r\n              {/* Incart and out of stock button */}\r\n              {sProduct.pQuantity !== 0 ? (\r\n                <Fragment>\r\n                  {layoutData.inCart !== null &&\r\n                  layoutData.inCart.includes(sProduct._id) === true ? (\r\n                    <div\r\n                      style={{ background: \"#303031\" }}\r\n                      className={`px-4 py-2 text-white text-center cursor-not-allowed uppercase opacity-75`}\r\n                    >\r\n                      In cart\r\n                    </div>\r\n                  ) : (\r\n                    <div\r\n                      onClick={(e) =>\r\n                        addToCart(\r\n                          sProduct._id,\r\n                          quantitiy,\r\n                          sProduct.pPrice,\r\n                          layoutDispatch,\r\n                          setQuantitiy,\r\n                          setAlertq,\r\n                          fetchData,\r\n                          totalCost\r\n                        )\r\n                      }\r\n                      style={{ background: \"#303031\" }}\r\n                      className={`px-4 py-2 text-white text-center cursor-pointer uppercase`}\r\n                    >\r\n                      Add to cart\r\n                    </div>\r\n                  )}\r\n                </Fragment>\r\n              ) : (\r\n                <Fragment>\r\n                  {layoutData.inCart !== null &&\r\n                  layoutData.inCart.includes(sProduct._id) === true ? (\r\n                    <div\r\n                      style={{ background: \"#303031\" }}\r\n                      className={`px-4 py-2 text-white text-center cursor-not-allowed uppercase opacity-75`}\r\n                    >\r\n                      In cart\r\n                    </div>\r\n                  ) : (\r\n                    <div\r\n                      style={{ background: \"#303031\" }}\r\n                      disabled={true}\r\n                      className=\"px-4 py-2 text-white opacity-50 cursor-not-allowed text-center uppercase\"\r\n                    >\r\n                      Out of stock\r\n                    </div>\r\n                  )}\r\n                </Fragment>\r\n              )}\r\n              {/* Incart and out of stock button End */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Product Details Section two */}\r\n      <ProductDetailsSectionTwo />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductDetailsSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AACxE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,qBAAqB,QAAQ,SAAS;AAC/C,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,wBAAwB,MAAM,4BAA4B;AAEjE,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AAC7D,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,UAAU;AAC1E,SAASC,SAAS,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACvC,IAAI;IAAEC;EAAG,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAExB,MAAM;IAAEyB,IAAI;IAAEC;EAAS,CAAC,GAAG3B,UAAU,CAACE,qBAAqB,CAAC;EAC5D,MAAM;IAAEwB,IAAI,EAAEE,UAAU;IAAED,QAAQ,EAAEE;EAAe,CAAC,GAClD7B,UAAU,CAACG,aAAa,CAAC,CAAC,CAAC;;EAE7B,MAAM2B,QAAQ,GAAGF,UAAU,CAACG,mBAAmB;EAC/C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvC,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,GAAGwC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEvC,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAChC2C,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAC7C,CAAC,CAAC,CAAC;;EAEH7C,SAAS,CAAC,MAAM;IACd8C,SAAS,CAAC,CAAC;IACX;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BlB,QAAQ,CAAC;MAAEmB,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5C,IAAI;MACF,IAAIC,YAAY,GAAG,MAAM1C,gBAAgB,CAACmB,EAAE,CAAC;MAC7CwB,UAAU,CAAC,MAAM;QACf;QACA,IAAID,YAAY,IAAIA,YAAY,CAACE,SAAS,EAAE;UAC1C;UACA,MAAMC,WAAW,GAAG;YAClBC,GAAG,EAAEJ,YAAY,CAACE,SAAS;YAC3BG,KAAK,EAAEL,YAAY,CAACM,IAAI;YACxBC,YAAY,EAAEP,YAAY,CAACQ,WAAW;YACtCC,MAAM,EAAET,YAAY,CAACU,KAAK;YAC1B1B,OAAO,EAAEgB,YAAY,CAACW,MAAM,IAAI,CAAC,0BAA0B,CAAC;YAC5DC,SAAS,EAAEZ,YAAY,CAACa,QAAQ,IAAI,CAAC;YACrCC,SAAS,EAAE;cACTV,GAAG,EAAEJ,YAAY,CAACe,UAAU,IAAI,CAAC;cACjCC,KAAK,EAAEhB,YAAY,CAACiB,QAAQ,IAAI;YAClC,CAAC;YACDC,eAAe,EAAElB,YAAY,CAACmB,OAAO,IAAI;UAC3C,CAAC;UAEDtC,cAAc,CAAC;YACbiB,IAAI,EAAE,qBAAqB;YAC3BC,OAAO,EAAEI;UACX,CAAC,CAAC,CAAC,CAAC;UACJlB,UAAU,CAACkB,WAAW,CAACnB,OAAO,CAAC;UAC/BL,QAAQ,CAAC;YAAEmB,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;UAC7ClB,cAAc,CAAC;YAAEiB,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAEjC,QAAQ,CAAC;UAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,MAAM,IAAIkC,YAAY,IAAIA,YAAY,CAACoB,OAAO,EAAE;UAC/C;UACAvC,cAAc,CAAC;YACbiB,IAAI,EAAE,qBAAqB;YAC3BC,OAAO,EAAEC,YAAY,CAACoB;UACxB,CAAC,CAAC;UACFnC,UAAU,CAACe,YAAY,CAACoB,OAAO,CAACpC,OAAO,CAAC;UACxCL,QAAQ,CAAC;YAAEmB,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;UAC7ClB,cAAc,CAAC;YAAEiB,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAEjC,QAAQ,CAAC;UAAE,CAAC,CAAC;QACzD,CAAC,MAAM;UACLuD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACpC3C,QAAQ,CAAC;YAAEmB,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAE;UAAM,CAAC,CAAC;QAC/C;QACA,IAAIC,YAAY,IAAIA,YAAY,CAACuB,KAAK,EAAE;UACtCF,OAAO,CAACC,GAAG,CAACtB,YAAY,CAACuB,KAAK,CAAC;QACjC;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,KAAK,CAAC;MAC7C5C,QAAQ,CAAC;QAAEmB,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;IACAyB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,gBAAA,EAAAC,iBAAA;MACF;MACA,MAAMC,GAAG,GAAGhC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;MACvC,MAAMgC,MAAM,GAAGD,GAAG,GAAG,EAAAF,gBAAA,GAAAhC,IAAI,CAACC,KAAK,CAACiC,GAAG,CAAC,CAACE,IAAI,cAAAJ,gBAAA,uBAApBA,gBAAA,CAAsBhD,EAAE,OAAAiD,iBAAA,GAAIjC,IAAI,CAACC,KAAK,CAACiC,GAAG,CAAC,CAACE,IAAI,cAAAH,iBAAA,uBAApBA,iBAAA,CAAsBtB,GAAG,IAAG,IAAI;MAEjF,IAAIwB,MAAM,EAAE;QACV,IAAI5B,YAAY,GAAG,MAAMzC,aAAa,CAACqE,MAAM,CAAC;QAC9C,IAAI5B,YAAY,IAAIA,YAAY,CAAC8B,OAAO,IAAI9B,YAAY,CAACtB,IAAI,EAAE;UAC7D;UACA,MAAMqD,SAAS,GAAG/B,YAAY,CAACtB,IAAI,CAACsD,KAAK,IAAI,EAAE;UAC/CnD,cAAc,CAAC;YAAEiB,IAAI,EAAE,aAAa;YAAEC,OAAO,EAAEgC;UAAU,CAAC,CAAC,CAAC,CAAC;QAC/D;MACF,CAAC,MAAM;QACL;QACA,MAAME,SAAS,GAAGxC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;QAChEf,cAAc,CAAC;UAAEiB,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAEkC;QAAU,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,KAAK,CAAC;MAC1C;MACA,MAAMU,SAAS,GAAGxC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;MAChEf,cAAc,CAAC;QAAEiB,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAEkC;MAAU,CAAC,CAAC;IAC7D;EACF,CAAC;EAED,IAAIvD,IAAI,CAACwD,OAAO,EAAE;IAChB,oBACEjE,OAAA;MAAKkE,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/FnE,OAAA;QACEkE,SAAS,EAAC,sCAAsC;QAChDE,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAJ,QAAA,eAElCnE,OAAA;UACEwE,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAC,GAAG;UACfC,CAAC,EAAC;QAA6G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC,MAAM,IAAI,CAAClE,QAAQ,EAAE;IACpB,oBAAOb,OAAA;MAAAmE,QAAA,EAAK;IAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EACA,oBACE/E,OAAA,CAACpB,QAAQ;IAAAuF,QAAA,gBACPnE,OAAA,CAACb,OAAO;MACN6F,KAAK,EAAE;QACLlC,UAAU,EAAEjC,QAAQ,CAACgC,SAAS,CAACV,GAAG;QAClC8C,OAAO,EAAEpE,QAAQ,CAACuB,KAAK;QACvBY,QAAQ,EAAEnC,QAAQ,CAACgC,SAAS,CAACE;MAC/B;IAAE;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF/E,OAAA;MAASkE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvCnE,OAAA;QAAKkE,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CnE,OAAA;UAAKkE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBACrFnE,OAAA;YACEkF,OAAO,EAAGC,CAAC,IACTxF,UAAU,CAAC,UAAU,EAAE,CAAC,EAAEsB,KAAK,EAAEC,QAAQ,EAAEH,OAAO,CACnD;YACDmD,SAAS,EAAE,GACTjD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,YAAY,sDACsB;YACvDmE,GAAG,EAAE,GAAGnF,MAAM,qBAAqBY,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,EAAG;YACzDsE,GAAG,EAAC;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF/E,OAAA;YACEkF,OAAO,EAAGC,CAAC,IACTxF,UAAU,CAAC,UAAU,EAAE,CAAC,EAAEsB,KAAK,EAAEC,QAAQ,EAAEH,OAAO,CACnD;YACDmD,SAAS,EAAE,GACTjD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,YAAY,sDACsB;YACvDmE,GAAG,EAAE,GAAGnF,MAAM,qBAAqBY,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,EAAG;YACzDsE,GAAG,EAAC;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN/E,OAAA;UAAKkE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCnE,OAAA;YAAKkE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBnE,OAAA;cACEkE,SAAS,EAAC,QAAQ;cAClBkB,GAAG,EAAE,GAAGnF,MAAM,qBAAqBY,QAAQ,CAACE,OAAO,CAACE,KAAK,CAAC,EAAG;cAC7DoE,GAAG,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACF/E,OAAA;cAAKkE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtEnE,OAAA;gBACEkF,OAAO,EAAGC,CAAC,IACTxF,UAAU,CAAC,UAAU,EAAE,IAAI,EAAEsB,KAAK,EAAEC,QAAQ,EAAEH,OAAO,CACtD;gBACDmD,SAAS,EAAC,gHAAgH;gBAC1HE,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,4BAA4B;gBAAAJ,QAAA,eAElCnE,OAAA;kBACEwE,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/E,OAAA;gBACEkF,OAAO,EAAGC,CAAC,IACTxF,UAAU,CAAC,UAAU,EAAE,IAAI,EAAEsB,KAAK,EAAEC,QAAQ,EAAEH,OAAO,CACtD;gBACDmD,SAAS,EAAC,gHAAgH;gBAC1HE,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,4BAA4B;gBAAAJ,QAAA,eAElCnE,OAAA;kBACEwE,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/E,OAAA;UAAKkE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEnE,OAAA;YAAKkE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnE,OAAA;cAAKkE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEtD,QAAQ,CAACuB;YAAK;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/D/E,OAAA;cAAKkE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnE,OAAA;gBAAMkE,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,GAAC,GACtD,EAACtD,QAAQ,CAAC2B,MAAM,EAAC,KACpB;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP/E,OAAA;gBAAAmE,QAAA,gBACEnE,OAAA;kBACEkF,OAAO,EAAGC,CAAC,IAAK5F,SAAS,CAAC4F,CAAC,EAAEtE,QAAQ,CAACsB,GAAG,EAAEZ,QAAQ,CAAE;kBACrD2C,SAAS,EAAE,GACTzE,MAAM,CAACoB,QAAQ,CAACsB,GAAG,EAAEb,KAAK,CAAC,IAAI,QAAQ,uDACe;kBACxD8C,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBACnBC,KAAK,EAAC,4BAA4B;kBAAAJ,QAAA,eAElCnE,OAAA;oBACEwE,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAA6H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/E,OAAA;kBACEkF,OAAO,EAAGC,CAAC,IAAK3F,SAAS,CAAC2F,CAAC,EAAEtE,QAAQ,CAACsB,GAAG,EAAEZ,QAAQ,CAAE;kBACrD2C,SAAS,EAAE,GACT,CAACzE,MAAM,CAACoB,QAAQ,CAACsB,GAAG,EAAEb,KAAK,CAAC,IAAI,QAAQ,uDACc;kBACxD8C,IAAI,EAAC,cAAc;kBACnBE,OAAO,EAAC,WAAW;kBACnBC,KAAK,EAAC,4BAA4B;kBAAAJ,QAAA,eAElCnE,OAAA;oBACEsF,QAAQ,EAAC,SAAS;oBAClBX,CAAC,EAAC,+GAA+G;oBACjHY,QAAQ,EAAC;kBAAS;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/E,OAAA;YAAKkE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxCtD,QAAQ,CAACyB;UAAY;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACN/E,OAAA;YAAKkE,SAAS,EAAC,cAAc;YAAAC,QAAA,GAC1B,CAAChD,SAAS,KAAK,CAACN,QAAQ,CAAC8B,SAAS,gBACjC3C,OAAA;cAAMkE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAE3D,EACD,eACD/E,OAAA;cACEkE,SAAS,EAAE,sDACT,CAAC/C,SAAS,KAAK,CAACN,QAAQ,CAAC8B,SAAS,IAAI,gBAAgB,EACrD;cAAAwB,QAAA,gBAEHnE,OAAA;gBACEkE,SAAS,EAAE,GACT/C,SAAS,KAAKN,QAAQ,CAAC8B,SAAS,IAAI,cAAc,EACjD;gBAAAwB,QAAA,EACJ;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAELlE,QAAQ,CAAC8B,SAAS,KAAK,CAAC,gBACvB3C,OAAA,CAACpB,QAAQ;gBAAAuF,QAAA,EACNxD,UAAU,CAAC6E,MAAM,IAAI,IAAI,IACzB7E,UAAU,CAAC6E,MAAM,KAAK,IAAI,IACzB7E,UAAU,CAAC6E,MAAM,CAACC,QAAQ,CAAC5E,QAAQ,CAACsB,GAAG,CAAC,KAAK,KAAM,gBACnDnC,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA;oBACEkF,OAAO,EAAGC,CAAC,IACTzF,cAAc,CACZ,UAAU,EACVmB,QAAQ,CAAC8B,SAAS,EAClBxB,SAAS,EACTC,YAAY,EACZC,SACF,CACD;oBAAA8C,QAAA,eAEDnE,OAAA;sBACEkE,SAAS,EAAC,qCAAqC;sBAC/CE,IAAI,EAAC,cAAc;sBACnBE,OAAO,EAAC,WAAW;sBACnBC,KAAK,EAAC,4BAA4B;sBAAAJ,QAAA,eAElCnE,OAAA;wBACEsF,QAAQ,EAAC,SAAS;wBAClBX,CAAC,EAAC,mHAAmH;wBACrHY,QAAQ,EAAC;sBAAS;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACP/E,OAAA;oBAAMkE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEhD;kBAAS;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD/E,OAAA;oBACEkF,OAAO,EAAGC,CAAC,IACTzF,cAAc,CACZ,UAAU,EACVmB,QAAQ,CAAC8B,SAAS,EAClBxB,SAAS,EACTC,YAAY,EACZC,SACF,CACD;oBAAA8C,QAAA,eAEDnE,OAAA;sBACEkE,SAAS,EAAC,qCAAqC;sBAC/CE,IAAI,EAAC,cAAc;sBACnBE,OAAO,EAAC,WAAW;sBACnBC,KAAK,EAAC,4BAA4B;sBAAAJ,QAAA,eAElCnE,OAAA;wBACEsF,QAAQ,EAAC,SAAS;wBAClBX,CAAC,EAAC,oHAAoH;wBACtHY,QAAQ,EAAC;sBAAS;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,gBAEN/E,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA;oBAAAmE,QAAA,eACEnE,OAAA;sBACEkE,SAAS,EAAC,yCAAyC;sBACnDE,IAAI,EAAC,cAAc;sBACnBE,OAAO,EAAC,WAAW;sBACnBC,KAAK,EAAC,4BAA4B;sBAAAJ,QAAA,eAElCnE,OAAA;wBACEsF,QAAQ,EAAC,SAAS;wBAClBX,CAAC,EAAC,mHAAmH;wBACrHY,QAAQ,EAAC;sBAAS;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACP/E,OAAA;oBAAMkE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEhD;kBAAS;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD/E,OAAA;oBAAAmE,QAAA,eACEnE,OAAA;sBACEkE,SAAS,EAAC,yCAAyC;sBACnDE,IAAI,EAAC,cAAc;sBACnBE,OAAO,EAAC,WAAW;sBACnBC,KAAK,EAAC,4BAA4B;sBAAAJ,QAAA,eAElCnE,OAAA;wBACEsF,QAAQ,EAAC,SAAS;wBAClBX,CAAC,EAAC,oHAAoH;wBACtHY,QAAQ,EAAC;sBAAS;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,gBAEX/E,OAAA;gBAAKkE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnE,OAAA;kBAAAmE,QAAA,eACEnE,OAAA;oBACEkE,SAAS,EAAC,yCAAyC;oBACnDE,IAAI,EAAC,cAAc;oBACnBE,OAAO,EAAC,WAAW;oBACnBC,KAAK,EAAC,4BAA4B;oBAAAJ,QAAA,eAElCnE,OAAA;sBACEsF,QAAQ,EAAC,SAAS;sBAClBX,CAAC,EAAC,mHAAmH;sBACrHY,QAAQ,EAAC;oBAAS;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACP/E,OAAA;kBAAMkE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEhD;gBAAS;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD/E,OAAA;kBAAAmE,QAAA,eACEnE,OAAA;oBACEkE,SAAS,EAAC,yCAAyC;oBACnDE,IAAI,EAAC,cAAc;oBACnBE,OAAO,EAAC,WAAW;oBACnBC,KAAK,EAAC,4BAA4B;oBAAAJ,QAAA,eAElCnE,OAAA;sBACEsF,QAAQ,EAAC,SAAS;sBAClBX,CAAC,EAAC,oHAAoH;sBACtHY,QAAQ,EAAC;oBAAS;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEE,CAAC,EAELlE,QAAQ,CAAC8B,SAAS,KAAK,CAAC,gBACvB3C,OAAA,CAACpB,QAAQ;cAAAuF,QAAA,EACNxD,UAAU,CAAC6E,MAAM,KAAK,IAAI,IAC3B7E,UAAU,CAAC6E,MAAM,CAACC,QAAQ,CAAC5E,QAAQ,CAACsB,GAAG,CAAC,KAAK,IAAI,gBAC/CnC,OAAA;gBACE0F,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBACjCzB,SAAS,EAAE,0EAA2E;gBAAAC,QAAA,EACvF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAEN/E,OAAA;gBACEkF,OAAO,EAAGC,CAAC,IACTvF,SAAS,CACPiB,QAAQ,CAACsB,GAAG,EACZhB,SAAS,EACTN,QAAQ,CAAC2B,MAAM,EACf5B,cAAc,EACdQ,YAAY,EACZC,SAAS,EACTO,SAAS,EACT9B,SACF,CACD;gBACD4F,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBACjCzB,SAAS,EAAE,2DAA4D;gBAAAC,QAAA,EACxE;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,gBAEX/E,OAAA,CAACpB,QAAQ;cAAAuF,QAAA,EACNxD,UAAU,CAAC6E,MAAM,KAAK,IAAI,IAC3B7E,UAAU,CAAC6E,MAAM,CAACC,QAAQ,CAAC5E,QAAQ,CAACsB,GAAG,CAAC,KAAK,IAAI,gBAC/CnC,OAAA;gBACE0F,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBACjCzB,SAAS,EAAE,0EAA2E;gBAAAC,QAAA,EACvF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAEN/E,OAAA;gBACE0F,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAU,CAAE;gBACjCC,QAAQ,EAAE,IAAK;gBACf1B,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EACrF;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV/E,OAAA,CAACZ,wBAAwB;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEf,CAAC;AAACxE,EAAA,CAtcIF,qBAAqB;EAAA,QACZrB,SAAS;AAAA;AAAA6G,EAAA,GADlBxF,qBAAqB;AAwc3B,eAAeA,qBAAqB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}