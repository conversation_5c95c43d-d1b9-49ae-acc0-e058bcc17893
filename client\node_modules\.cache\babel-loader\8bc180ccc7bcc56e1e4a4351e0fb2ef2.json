{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\admin\\\\orders\\\\UpdateOrderModal.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useContext, useState, useEffect } from \"react\";\nimport { OrderContext } from \"./index\";\nimport { getAllOrder, editCategory } from \"./FetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UpdateOrderModal = props => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(OrderContext);\n  const [status, setStatus] = useState(\"\");\n  const [oId, setOid] = useState(\"\");\n  useEffect(() => {\n    setOid(data.updateOrderModal.oId);\n    setStatus(data.updateOrderModal.status);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [data.updateOrderModal.modal]);\n  const fetchData = async () => {\n    let responseData = await getAllOrder();\n    if (responseData.Orders) {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData.Orders\n      });\n    }\n  };\n  const submitForm = async () => {\n    dispatch({\n      type: \"loading\",\n      payload: true\n    });\n    let responseData = await editCategory(oId, status);\n    if (responseData.error) {\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    } else if (responseData.success) {\n      console.log(responseData.success);\n      dispatch({\n        type: \"updateOrderModalClose\"\n      });\n      fetchData();\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: e => dispatch({\n        type: \"updateOrderModalClose\"\n      }),\n      className: `${data.updateOrderModal.modal ? \"\" : \"hidden\"} fixed top-0 left-0 z-30 w-full h-full bg-black opacity-50`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${data.updateOrderModal.modal ? \"\" : \"hidden\"} fixed inset-0 m-4  flex items-center z-30 justify-center`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative bg-white w-11/12 md:w-3/6 shadow-lg flex flex-col items-center space-y-4  overflow-y-auto px-4 py-4 md:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between w-full pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-left font-semibold text-2xl tracking-wider\",\n            children: \"Update Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: \"#303031\"\n            },\n            onClick: e => dispatch({\n              type: \"updateOrderModalClose\"\n            }),\n            className: \"cursor-pointer text-gray-100 py-2 px-2 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-1 w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"status\",\n            children: \"Order Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: status,\n            name: \"status\",\n            onChange: e => setStatus(e.target.value),\n            className: \"px-4 py-2 border focus:outline-none\",\n            id: \"status\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              name: \"status\",\n              value: \"Not processed\",\n              children: \"Not processed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              name: \"status\",\n              value: \"Processing\",\n              children: \"Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              name: \"status\",\n              value: \"Shipped\",\n              children: \"Shipped\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              name: \"status\",\n              value: \"Delivered\",\n              children: \"Delivered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              name: \"status\",\n              value: \"Cancelled\",\n              children: \"Cancelled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-1 w-full pb-4 md:pb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: \"#303031\"\n            },\n            onClick: e => submitForm(),\n            className: \"rounded-full bg-gray-800 text-gray-100 text-lg font-medium py-2\",\n            children: \"Update category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(UpdateOrderModal, \"Yc5luI3hH4+iZ5ojrhPf5Dqe1ow=\");\n_c = UpdateOrderModal;\nexport default UpdateOrderModal;\nvar _c;\n$RefreshReg$(_c, \"UpdateOrderModal\");", "map": {"version": 3, "names": ["React", "Fragment", "useContext", "useState", "useEffect", "OrderContext", "getAllOrder", "editCategory", "jsxDEV", "_jsxDEV", "UpdateOrderModal", "props", "_s", "data", "dispatch", "status", "setStatus", "oId", "setOid", "updateOrderModal", "modal", "fetchData", "responseData", "Orders", "type", "payload", "submitForm", "error", "success", "console", "log", "children", "onClick", "e", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "htmlFor", "value", "name", "onChange", "target", "id", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/UpdateOrderModal.js"], "sourcesContent": ["import React, { Fragment, useContext, useState, useEffect } from \"react\";\r\nimport { OrderContext } from \"./index\";\r\nimport { getAllOrder, editCategory } from \"./FetchApi\";\r\n\r\nconst UpdateOrderModal = (props) => {\r\n  const { data, dispatch } = useContext(OrderContext);\r\n\r\n  const [status, setStatus] = useState(\"\");\r\n\r\n  const [oId, setOid] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    setOid(data.updateOrderModal.oId);\r\n    setStatus(data.updateOrderModal.status);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [data.updateOrderModal.modal]);\r\n\r\n  const fetchData = async () => {\r\n    let responseData = await getAllOrder();\r\n    if (responseData.Orders) {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData.Orders,\r\n      });\r\n    }\r\n  };\r\n\r\n  const submitForm = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    let responseData = await editCategory(oId, status);\r\n    if (responseData.error) {\r\n      dispatch({ type: \"loading\", payload: false });\r\n    } else if (responseData.success) {\r\n      console.log(responseData.success);\r\n      dispatch({ type: \"updateOrderModalClose\" });\r\n      fetchData();\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Fragment>\r\n      {/* Black Overlay */}\r\n      <div\r\n        onClick={(e) => dispatch({ type: \"updateOrderModalClose\" })}\r\n        className={`${\r\n          data.updateOrderModal.modal ? \"\" : \"hidden\"\r\n        } fixed top-0 left-0 z-30 w-full h-full bg-black opacity-50`}\r\n      />\r\n      {/* End Black Overlay */}\r\n\r\n      {/* Modal Start */}\r\n      <div\r\n        className={`${\r\n          data.updateOrderModal.modal ? \"\" : \"hidden\"\r\n        } fixed inset-0 m-4  flex items-center z-30 justify-center`}\r\n      >\r\n        <div className=\"relative bg-white w-11/12 md:w-3/6 shadow-lg flex flex-col items-center space-y-4  overflow-y-auto px-4 py-4 md:px-8\">\r\n          <div className=\"flex items-center justify-between w-full pt-4\">\r\n            <span className=\"text-left font-semibold text-2xl tracking-wider\">\r\n              Update Order\r\n            </span>\r\n            {/* Close Modal */}\r\n            <span\r\n              style={{ background: \"#303031\" }}\r\n              onClick={(e) => dispatch({ type: \"updateOrderModalClose\" })}\r\n              className=\"cursor-pointer text-gray-100 py-2 px-2 rounded-full\"\r\n            >\r\n              <svg\r\n                className=\"w-6 h-6\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M6 18L18 6M6 6l12 12\"\r\n                />\r\n              </svg>\r\n            </span>\r\n          </div>\r\n          <div className=\"flex flex-col space-y-1 w-full\">\r\n            <label htmlFor=\"status\">Order Status</label>\r\n            <select\r\n              value={status}\r\n              name=\"status\"\r\n              onChange={(e) => setStatus(e.target.value)}\r\n              className=\"px-4 py-2 border focus:outline-none\"\r\n              id=\"status\"\r\n            >\r\n              <option name=\"status\" value=\"Not processed\">\r\n                Not processed\r\n              </option>\r\n              <option name=\"status\" value=\"Processing\">\r\n                Processing\r\n              </option>\r\n              <option name=\"status\" value=\"Shipped\">\r\n                Shipped\r\n              </option>\r\n              <option name=\"status\" value=\"Delivered\">\r\n                Delivered\r\n              </option>\r\n              <option name=\"status\" value=\"Cancelled\">\r\n                Cancelled\r\n              </option>\r\n            </select>\r\n          </div>\r\n          <div className=\"flex flex-col space-y-1 w-full pb-4 md:pb-6\">\r\n            <button\r\n              style={{ background: \"#303031\" }}\r\n              onClick={(e) => submitForm()}\r\n              className=\"rounded-full bg-gray-800 text-gray-100 text-lg font-medium py-2\"\r\n            >\r\n              Update category\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default UpdateOrderModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACxE,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,WAAW,EAAEC,YAAY,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGZ,UAAU,CAACG,YAAY,CAAC;EAEnD,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAACc,GAAG,EAAEC,MAAM,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAElCC,SAAS,CAAC,MAAM;IACdc,MAAM,CAACL,IAAI,CAACM,gBAAgB,CAACF,GAAG,CAAC;IACjCD,SAAS,CAACH,IAAI,CAACM,gBAAgB,CAACJ,MAAM,CAAC;IACvC;EACF,CAAC,EAAE,CAACF,IAAI,CAACM,gBAAgB,CAACC,KAAK,CAAC,CAAC;EAEjC,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIC,YAAY,GAAG,MAAMhB,WAAW,CAAC,CAAC;IACtC,IAAIgB,YAAY,CAACC,MAAM,EAAE;MACvBT,QAAQ,CAAC;QACPU,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEH,YAAY,CAACC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BZ,QAAQ,CAAC;MAAEU,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5C,IAAIH,YAAY,GAAG,MAAMf,YAAY,CAACU,GAAG,EAAEF,MAAM,CAAC;IAClD,IAAIO,YAAY,CAACK,KAAK,EAAE;MACtBb,QAAQ,CAAC;QAAEU,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIH,YAAY,CAACM,OAAO,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAACR,YAAY,CAACM,OAAO,CAAC;MACjCd,QAAQ,CAAC;QAAEU,IAAI,EAAE;MAAwB,CAAC,CAAC;MAC3CH,SAAS,CAAC,CAAC;MACXP,QAAQ,CAAC;QAAEU,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC;EAED,oBACEhB,OAAA,CAACR,QAAQ;IAAA8B,QAAA,gBAEPtB,OAAA;MACEuB,OAAO,EAAGC,CAAC,IAAKnB,QAAQ,CAAC;QAAEU,IAAI,EAAE;MAAwB,CAAC,CAAE;MAC5DU,SAAS,EAAE,GACTrB,IAAI,CAACM,gBAAgB,CAACC,KAAK,GAAG,EAAE,GAAG,QAAQ;IACgB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAIF7B,OAAA;MACEyB,SAAS,EAAE,GACTrB,IAAI,CAACM,gBAAgB,CAACC,KAAK,GAAG,EAAE,GAAG,QAAQ,2DACe;MAAAW,QAAA,eAE5DtB,OAAA;QAAKyB,SAAS,EAAC,sHAAsH;QAAAH,QAAA,gBACnItB,OAAA;UAAKyB,SAAS,EAAC,+CAA+C;UAAAH,QAAA,gBAC5DtB,OAAA;YAAMyB,SAAS,EAAC,iDAAiD;YAAAH,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP7B,OAAA;YACE8B,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCR,OAAO,EAAGC,CAAC,IAAKnB,QAAQ,CAAC;cAAEU,IAAI,EAAE;YAAwB,CAAC,CAAE;YAC5DU,SAAS,EAAC,qDAAqD;YAAAH,QAAA,eAE/DtB,OAAA;cACEyB,SAAS,EAAC,SAAS;cACnBO,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,4BAA4B;cAAAb,QAAA,eAElCtB,OAAA;gBACEoC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAsB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7B,OAAA;UAAKyB,SAAS,EAAC,gCAAgC;UAAAH,QAAA,gBAC7CtB,OAAA;YAAOwC,OAAO,EAAC,QAAQ;YAAAlB,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C7B,OAAA;YACEyC,KAAK,EAAEnC,MAAO;YACdoC,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAGnB,CAAC,IAAKjB,SAAS,CAACiB,CAAC,CAACoB,MAAM,CAACH,KAAK,CAAE;YAC3ChB,SAAS,EAAC,qCAAqC;YAC/CoB,EAAE,EAAC,QAAQ;YAAAvB,QAAA,gBAEXtB,OAAA;cAAQ0C,IAAI,EAAC,QAAQ;cAACD,KAAK,EAAC,eAAe;cAAAnB,QAAA,EAAC;YAE5C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cAAQ0C,IAAI,EAAC,QAAQ;cAACD,KAAK,EAAC,YAAY;cAAAnB,QAAA,EAAC;YAEzC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cAAQ0C,IAAI,EAAC,QAAQ;cAACD,KAAK,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAEtC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cAAQ0C,IAAI,EAAC,QAAQ;cAACD,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAExC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cAAQ0C,IAAI,EAAC,QAAQ;cAACD,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAExC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7B,OAAA;UAAKyB,SAAS,EAAC,6CAA6C;UAAAH,QAAA,eAC1DtB,OAAA;YACE8B,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAU,CAAE;YACjCR,OAAO,EAAGC,CAAC,IAAKP,UAAU,CAAC,CAAE;YAC7BQ,SAAS,EAAC,iEAAiE;YAAAH,QAAA,EAC5E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC1B,EAAA,CAvHIF,gBAAgB;AAAA6C,EAAA,GAAhB7C,gBAAgB;AAyHtB,eAAeA,gBAAgB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}