{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\dashboardUser\\\\UserOrders.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useEffect, useContext, useState } from \"react\";\nimport moment from \"moment\";\n// Ensure cancelOrder is imported and aliased to avoid name collision if needed\nimport { fetchOrderByUser, handleCancelOrder as cancelOrderAction } from \"./Action\";\nimport Layout, { DashboardUserContext } from \"./Layout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst TableHeader = () => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"thead\", {\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Total\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Recipient Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Created At\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Last Updated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n          className: \"px-4 py-2 border\",\n          children: \"Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = TableHeader;\nconst TableBody = ({\n  order,\n  onOrderClick,\n  onCancelOrder\n}) => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      className: \"border-b cursor-pointer hover:bg-gray-100\",\n      onClick: () => onOrderClick(order),\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"w-48 p-2 flex flex-col space-y-1\",\n        children: order.items.map((item, i) => {\n          const imageUrl = item.productImages && item.productImages.length > 0 ? `${apiURL}/uploads/products/${item.productImages[0]}` : '/placeholder-product.jpg'; // Fallback for missing image\n\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"w-8 h-8 object-cover object-center\",\n              src: imageUrl,\n              alt: item.productName || 'Product Image'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.productName || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [item.quantity, \"x\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center cursor-default\",\n        children: [order.orderStatus === \"DRAFT\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.orderStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this), order.orderStatus === \"PENDING\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-orange-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.orderStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), order.orderStatus === \"SHIPPED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.orderStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), order.orderStatus === \"DELIVERED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.orderStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), order.orderStatus === \"CANCELLED\" && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\",\n          children: order.orderStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: order.total ? order.total.toLocaleString('vi-VN') + ' VND' : '0 VND'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: order.deliveryInfo ? order.deliveryInfo.recipientName : \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: order.deliveryInfo ? `${order.deliveryInfo.address}, ${order.deliveryInfo.provinceCity}` : \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: moment(order.createdDate).format(\"lll\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: order.updatedDate ? moment(order.updatedDate).format(\"lll\") : \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"p-2 text-center\",\n        children: order.orderStatus === \"PENDING\" && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 text-xs\",\n          onClick: e => {\n            e.stopPropagation();\n            if (window.confirm(\"Are you sure you want to cancel this order?\")) {\n              onCancelOrder(order.orderId);\n            }\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n\n// Order Detail Modal Component\n_c2 = TableBody;\nconst OrderDetailModal = ({\n  order,\n  onClose\n}) => {\n  if (!order) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative p-5 border w-96 shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-gray-900\",\n          children: [\"Order Details (ID: \", order.orderId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 px-7 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 62\n            }, this), \" \", order.orderStatus]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 62\n            }, this), \" \", order.total ? order.total.toLocaleString('vi-VN') + ' VND' : '0 VND']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Recipient:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 62\n            }, this), \" \", order.deliveryInfo ? order.deliveryInfo.recipientName : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Address:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 62\n            }, this), \" \", order.deliveryInfo ? `${order.deliveryInfo.address}, ${order.deliveryInfo.provinceCity}` : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Created:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 62\n            }, this), \" \", moment(order.createdDate).format(\"lll\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium mt-3\",\n            children: \"Items:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-disc list-inside text-sm text-gray-700\",\n            children: order.items.map((item, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [item.productName || 'N/A', \" (x\", item.quantity, \") - \", item.lineTotal ? item.lineTotal.toLocaleString('vi-VN') + ' VND' : '0 VND']\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Shipping Fee:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 67\n            }, this), \" \", order.shippingFee ? order.shippingFee.toLocaleString('vi-VN') + ' VND' : '0 VND']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Discount:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 62\n            }, this), \" $\", order.discount ? order.discount.toFixed(2) : '0.00']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Payment Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 62\n            }, this), \" \", order.paymentStatus || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this), order.rushDeliveryTime && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Rush Delivery Time:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 67\n            }, this), \" \", moment(order.rushDeliveryTime).format(\"lll\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-center px-4 py-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            id: \"ok-btn\",\n            className: \"px-4 py-2 bg-yellow-700 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-yellow-800 focus:outline-none focus:ring-2 focus:ring-yellow-500\",\n            onClick: onClose,\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 9\n  }, this);\n};\n_c3 = OrderDetailModal;\nconst OrdersComponent = () => {\n  _s();\n  const {\n    data,\n    dispatch\n  } = useContext(DashboardUserContext);\n  const {\n    OrderByUser: orders\n  } = data;\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  useEffect(() => {\n    fetchOrderByUser(dispatch);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const handleOrderClick = order => {\n    setSelectedOrder(order);\n  };\n  const handleCloseModal = () => {\n    setSelectedOrder(null);\n  };\n  const handleCancelOrder = async orderId => {\n    try {\n      await cancelOrderAction(orderId);\n      fetchOrderByUser(dispatch);\n      alert(`Order ${orderId} cancelled successfully!`);\n    } catch (error) {\n      console.error(\"Error canceling order:\", error);\n      alert(`Failed to cancel order ${orderId}. Error: ${error.message || 'Unknown error'}`);\n    }\n  };\n  if (data.loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full md:w-9/12 flex items-center justify-center py-24\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 animate-spin text-gray-600\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: \"2\",\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-full my-4 md:my-0 md:w-9/12 md:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-4 px-4 text-lg font-semibold border-t-2 border-yellow-700\",\n          children: \"Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-auto bg-white shadow-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table-auto border w-full my-2\",\n            children: [/*#__PURE__*/_jsxDEV(TableHeader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: orders && orders.length > 0 ? orders.map((item, i) => {\n                return /*#__PURE__*/_jsxDEV(TableBody, {\n                  order: item,\n                  onOrderClick: handleOrderClick,\n                  onCancelOrder: handleCancelOrder\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 28\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"8\",\n                  className: \"text-xl text-center font-semibold py-8\",\n                  children: \"No order found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 mt-2\",\n            children: [\"Total \", orders && orders.length, \" order found\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OrderDetailModal, {\n      order: selectedOrder,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(OrdersComponent, \"y3+Qq7ge6+X6+A/JaWIQ5MxTZjE=\");\n_c4 = OrdersComponent;\nconst UserOrders = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(OrdersComponent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_c5 = UserOrders;\nexport default UserOrders;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"TableHeader\");\n$RefreshReg$(_c2, \"TableBody\");\n$RefreshReg$(_c3, \"OrderDetailModal\");\n$RefreshReg$(_c4, \"OrdersComponent\");\n$RefreshReg$(_c5, \"UserOrders\");", "map": {"version": 3, "names": ["React", "Fragment", "useEffect", "useContext", "useState", "moment", "fetchOrderByUser", "handleCancelOrder", "cancelOrderAction", "Layout", "DashboardUserContext", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "TableHeader", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TableBody", "order", "onOrderClick", "onCancelOrder", "onClick", "items", "map", "item", "i", "imageUrl", "productImages", "length", "src", "alt", "productName", "quantity", "orderStatus", "total", "toLocaleString", "deliveryInfo", "<PERSON><PERSON><PERSON>", "address", "provinceCity", "createdDate", "format", "updatedDate", "e", "stopPropagation", "window", "confirm", "orderId", "_c2", "OrderDetailModal", "onClose", "lineTotal", "shippingFee", "discount", "toFixed", "paymentStatus", "rushDeliveryTime", "id", "_c3", "OrdersComponent", "_s", "data", "dispatch", "OrderByUser", "orders", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "handleOrderClick", "handleCloseModal", "alert", "error", "console", "message", "loading", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "colSpan", "_c4", "UserOrders", "props", "_c5", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/UserOrders.js"], "sourcesContent": ["import React, { Fragment, useEffect, useContext, useState } from \"react\";\r\nimport moment from \"moment\";\r\n// Ensure cancelOrder is imported and aliased to avoid name collision if needed\r\nimport { fetchOrderByUser, handleCancelOrder as cancelOrderAction } from \"./Action\";\r\nimport Layout, { DashboardUserContext } from \"./Layout\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst TableHeader = () => {\r\n  return (\r\n    <Fragment>\r\n      <thead>\r\n        <tr>\r\n          <th className=\"px-4 py-2 border\">Products</th>\r\n          <th className=\"px-4 py-2 border\">Status</th>\r\n          <th className=\"px-4 py-2 border\">Total</th>\r\n          <th className=\"px-4 py-2 border\">Recipient Name</th>\r\n          <th className=\"px-4 py-2 border\">Address</th>\r\n          <th className=\"px-4 py-2 border\">Created At</th>\r\n          <th className=\"px-4 py-2 border\">Last Updated</th>\r\n          <th className=\"px-4 py-2 border\">Actions</th>\r\n        </tr>\r\n      </thead>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst TableBody = ({ order, onOrderClick, onCancelOrder }) => {\r\n  return (\r\n    <Fragment>\r\n      <tr className=\"border-b cursor-pointer hover:bg-gray-100\" onClick={() => onOrderClick(order)}>\r\n        <td className=\"w-48 p-2 flex flex-col space-y-1\">\r\n          {order.items.map((item, i) => {\r\n            const imageUrl = item.productImages && item.productImages.length > 0\r\n              ? `${apiURL}/uploads/products/${item.productImages[0]}`\r\n              : '/placeholder-product.jpg'; // Fallback for missing image\r\n\r\n            return (\r\n              <span className=\"block flex items-center space-x-2\" key={i}>\r\n                <img\r\n                  className=\"w-8 h-8 object-cover object-center\"\r\n                  src={imageUrl}\r\n                  alt={item.productName || 'Product Image'}\r\n                />\r\n                <span>{item.productName || 'N/A'}</span>\r\n                <span>{item.quantity}x</span>\r\n              </span>\r\n            );\r\n          })}\r\n        </td>\r\n        <td className=\"p-2 text-center cursor-default\">\r\n          {order.orderStatus === \"DRAFT\" && (\r\n            <span className=\"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.orderStatus}\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"PENDING\" && (\r\n            <span className=\"block text-orange-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.orderStatus}\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"SHIPPED\" && (\r\n            <span className=\"block text-blue-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.orderStatus}\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"DELIVERED\" && (\r\n            <span className=\"block text-green-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.orderStatus}\r\n            </span>\r\n          )}\r\n          {order.orderStatus === \"CANCELLED\" && (\r\n            <span className=\"block text-red-600 rounded-full text-center text-xs px-2 font-semibold\">\r\n              {order.orderStatus}\r\n            </span>\r\n          )}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {order.total ? order.total.toLocaleString('vi-VN') + ' VND' : '0 VND'}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {order.deliveryInfo ? order.deliveryInfo.recipientName : \"N/A\"}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {order.deliveryInfo ? `${order.deliveryInfo.address}, ${order.deliveryInfo.provinceCity}` : \"N/A\"}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {moment(order.createdDate).format(\"lll\")}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {order.updatedDate ? moment(order.updatedDate).format(\"lll\") : \"N/A\"}\r\n        </td>\r\n        <td className=\"p-2 text-center\">\r\n          {order.orderStatus === \"PENDING\" && (\r\n            <button\r\n              className=\"bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 text-xs\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                if (window.confirm(\"Are you sure you want to cancel this order?\")) {\r\n                  onCancelOrder(order.orderId);\r\n                }\r\n              }}\r\n            >\r\n              Cancel\r\n            </button>\r\n          )}\r\n        </td>\r\n      </tr>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\n// Order Detail Modal Component\r\nconst OrderDetailModal = ({ order, onClose }) => {\r\n    if (!order) return null;\r\n\r\n    return (\r\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\">\r\n            <div className=\"relative p-5 border w-96 shadow-lg rounded-md bg-white\">\r\n                <div className=\"text-center\">\r\n                    <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Order Details (ID: {order.orderId})</h3>\r\n                    <div className=\"mt-2 px-7 py-3\">\r\n                        <p className=\"text-sm text-gray-500\"><strong>Status:</strong> {order.orderStatus}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Total:</strong> {order.total ? order.total.toLocaleString('vi-VN') + ' VND' : '0 VND'}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Recipient:</strong> {order.deliveryInfo ? order.deliveryInfo.recipientName : 'N/A'}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Address:</strong> {order.deliveryInfo ? `${order.deliveryInfo.address}, ${order.deliveryInfo.provinceCity}` : 'N/A'}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Created:</strong> {moment(order.createdDate).format(\"lll\")}</p>\r\n                        <h4 className=\"text-md font-medium mt-3\">Items:</h4>\r\n                        <ul className=\"list-disc list-inside text-sm text-gray-700\">\r\n                            {order.items.map((item, i) => (\r\n                                <li key={i}>\r\n                                  {item.productName || 'N/A'} (x{item.quantity}) - {item.lineTotal ? item.lineTotal.toLocaleString('vi-VN') + ' VND' : '0 VND'}\r\n                                </li>\r\n                            ))}\r\n                        </ul>\r\n                        <p className=\"text-sm text-gray-500 mt-2\"><strong>Shipping Fee:</strong> {order.shippingFee ? order.shippingFee.toLocaleString('vi-VN') + ' VND' : '0 VND'}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Discount:</strong> ${order.discount ? order.discount.toFixed(2) : '0.00'}</p>\r\n                        <p className=\"text-sm text-gray-500\"><strong>Payment Status:</strong> {order.paymentStatus || 'N/A'}</p>\r\n                        {order.rushDeliveryTime && (\r\n                             <p className=\"text-sm text-gray-500\"><strong>Rush Delivery Time:</strong> {moment(order.rushDeliveryTime).format(\"lll\")}</p>\r\n                        )}\r\n                    </div>\r\n                    <div className=\"items-center px-4 py-3\">\r\n                        <button\r\n                            id=\"ok-btn\"\r\n                            className=\"px-4 py-2 bg-yellow-700 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-yellow-800 focus:outline-none focus:ring-2 focus:ring-yellow-500\"\r\n                            onClick={onClose}\r\n                        >\r\n                            Close\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\nconst OrdersComponent = () => {\r\n  const { data, dispatch } = useContext(DashboardUserContext);\r\n  const { OrderByUser: orders } = data;\r\n  const [selectedOrder, setSelectedOrder] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchOrderByUser(dispatch);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleOrderClick = (order) => {\r\n    setSelectedOrder(order);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setSelectedOrder(null);\r\n  };\r\n\r\n  const handleCancelOrder = async (orderId) => {\r\n    try {\r\n      await cancelOrderAction(orderId);\r\n      fetchOrderByUser(dispatch);\r\n      alert(`Order ${orderId} cancelled successfully!`);\r\n    } catch (error) {\r\n      console.error(\"Error canceling order:\", error);\r\n      alert(`Failed to cancel order ${orderId}. Error: ${error.message || 'Unknown error'}`);\r\n    }\r\n  };\r\n\r\n  if (data.loading) {\r\n    return (\r\n      <div className=\"w-full md:w-9/12 flex items-center justify-center py-24\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      <div className=\"flex flex-col w-full my-4 md:my-0 md:w-9/12 md:px-8\">\r\n        <div className=\"border\">\r\n          <div className=\"py-4 px-4 text-lg font-semibold border-t-2 border-yellow-700\">\r\n            Orders\r\n          </div>\r\n          <hr />\r\n          <div className=\"overflow-auto bg-white shadow-lg p-4\">\r\n            <table className=\"table-auto border w-full my-2\">\r\n              <TableHeader />\r\n              <tbody>\r\n                {orders && orders.length > 0 ? (\r\n                  orders.map((item, i) => {\r\n                    return <TableBody key={i} order={item} onOrderClick={handleOrderClick} onCancelOrder={handleCancelOrder} />;\r\n                  })\r\n                ) : (\r\n                  <tr>\r\n                    <td\r\n                      colSpan=\"8\"\r\n                      className=\"text-xl text-center font-semibold py-8\"\r\n                    >\r\n                      No order found\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </tbody>\r\n            </table>\r\n            <div className=\"text-sm text-gray-600 mt-2\">\r\n              Total {orders && orders.length} order found\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Order Detail Modal */}\r\n      <OrderDetailModal order={selectedOrder} onClose={handleCloseModal} />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst UserOrders = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <Layout children={<OrdersComponent />} />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default UserOrders;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACxE,OAAOC,MAAM,MAAM,QAAQ;AAC3B;AACA,SAASC,gBAAgB,EAAEC,iBAAiB,IAAIC,iBAAiB,QAAQ,UAAU;AACnF,OAAOC,MAAM,IAAIC,oBAAoB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACEL,OAAA,CAACX,QAAQ;IAAAiB,QAAA,eACPN,OAAA;MAAAM,QAAA,eACEN,OAAA;QAAAM,QAAA,gBACEN,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDX,OAAA;UAAIO,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEf,CAAC;AAACC,EAAA,GAjBIP,WAAW;AAmBjB,MAAMQ,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,YAAY;EAAEC;AAAc,CAAC,KAAK;EAC5D,oBACEhB,OAAA,CAACX,QAAQ;IAAAiB,QAAA,eACPN,OAAA;MAAIO,SAAS,EAAC,2CAA2C;MAACU,OAAO,EAAEA,CAAA,KAAMF,YAAY,CAACD,KAAK,CAAE;MAAAR,QAAA,gBAC3FN,OAAA;QAAIO,SAAS,EAAC,kCAAkC;QAAAD,QAAA,EAC7CQ,KAAK,CAACI,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;UAC5B,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa,IAAIH,IAAI,CAACG,aAAa,CAACC,MAAM,GAAG,CAAC,GAChE,GAAGvB,MAAM,qBAAqBmB,IAAI,CAACG,aAAa,CAAC,CAAC,CAAC,EAAE,GACrD,0BAA0B,CAAC,CAAC;;UAEhC,oBACEvB,OAAA;YAAMO,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBACjDN,OAAA;cACEO,SAAS,EAAC,oCAAoC;cAC9CkB,GAAG,EAAEH,QAAS;cACdI,GAAG,EAAEN,IAAI,CAACO,WAAW,IAAI;YAAgB;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACFX,OAAA;cAAAM,QAAA,EAAOc,IAAI,CAACO,WAAW,IAAI;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCX,OAAA;cAAAM,QAAA,GAAOc,IAAI,CAACQ,QAAQ,EAAC,GAAC;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP0BU,CAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQpD,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,gCAAgC;QAAAD,QAAA,GAC3CQ,KAAK,CAACe,WAAW,KAAK,OAAO,iBAC5B7B,OAAA;UAAMO,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EACrFQ,KAAK,CAACe;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP,EACAG,KAAK,CAACe,WAAW,KAAK,SAAS,iBAC9B7B,OAAA;UAAMO,SAAS,EAAC,2EAA2E;UAAAD,QAAA,EACxFQ,KAAK,CAACe;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP,EACAG,KAAK,CAACe,WAAW,KAAK,SAAS,iBAC9B7B,OAAA;UAAMO,SAAS,EAAC,yEAAyE;UAAAD,QAAA,EACtFQ,KAAK,CAACe;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP,EACAG,KAAK,CAACe,WAAW,KAAK,WAAW,iBAChC7B,OAAA;UAAMO,SAAS,EAAC,0EAA0E;UAAAD,QAAA,EACvFQ,KAAK,CAACe;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP,EACAG,KAAK,CAACe,WAAW,KAAK,WAAW,iBAChC7B,OAAA;UAAMO,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EACrFQ,KAAK,CAACe;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5BQ,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACgB,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG;MAAO;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5BQ,KAAK,CAACkB,YAAY,GAAGlB,KAAK,CAACkB,YAAY,CAACC,aAAa,GAAG;MAAK;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5BQ,KAAK,CAACkB,YAAY,GAAG,GAAGlB,KAAK,CAACkB,YAAY,CAACE,OAAO,KAAKpB,KAAK,CAACkB,YAAY,CAACG,YAAY,EAAE,GAAG;MAAK;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5Bb,MAAM,CAACqB,KAAK,CAACsB,WAAW,CAAC,CAACC,MAAM,CAAC,KAAK;MAAC;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5BQ,KAAK,CAACwB,WAAW,GAAG7C,MAAM,CAACqB,KAAK,CAACwB,WAAW,CAAC,CAACD,MAAM,CAAC,KAAK,CAAC,GAAG;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACLX,OAAA;QAAIO,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAC5BQ,KAAK,CAACe,WAAW,KAAK,SAAS,iBAC9B7B,OAAA;UACEO,SAAS,EAAC,kEAAkE;UAC5EU,OAAO,EAAGsB,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnB,IAAIC,MAAM,CAACC,OAAO,CAAC,6CAA6C,CAAC,EAAE;cACjE1B,aAAa,CAACF,KAAK,CAAC6B,OAAO,CAAC;YAC9B;UACF,CAAE;UAAArC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEf,CAAC;;AAED;AAAAiC,GAAA,GArFM/B,SAAS;AAsFf,MAAMgC,gBAAgB,GAAGA,CAAC;EAAE/B,KAAK;EAAEgC;AAAQ,CAAC,KAAK;EAC7C,IAAI,CAAChC,KAAK,EAAE,OAAO,IAAI;EAEvB,oBACId,OAAA;IAAKO,SAAS,EAAC,6GAA6G;IAAAD,QAAA,eACxHN,OAAA;MAAKO,SAAS,EAAC,wDAAwD;MAAAD,QAAA,eACnEN,OAAA;QAAKO,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxBN,OAAA;UAAIO,SAAS,EAAC,6CAA6C;UAAAD,QAAA,GAAC,qBAAmB,EAACQ,KAAK,CAAC6B,OAAO,EAAC,GAAC;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpGX,OAAA;UAAKO,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC3BN,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACe,WAAW;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACgB,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG,OAAO;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvIX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACkB,YAAY,GAAGlB,KAAK,CAACkB,YAAY,CAACC,aAAa,GAAG,KAAK;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpIX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACkB,YAAY,GAAG,GAAGlB,KAAK,CAACkB,YAAY,CAACE,OAAO,KAAKpB,KAAK,CAACkB,YAAY,CAACG,YAAY,EAAE,GAAG,KAAK;UAAA;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrKX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClB,MAAM,CAACqB,KAAK,CAACsB,WAAW,CAAC,CAACC,MAAM,CAAC,KAAK,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5GX,OAAA;YAAIO,SAAS,EAAC,0BAA0B;YAAAD,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDX,OAAA;YAAIO,SAAS,EAAC,6CAA6C;YAAAD,QAAA,EACtDQ,KAAK,CAACI,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,kBACrBrB,OAAA;cAAAM,QAAA,GACGc,IAAI,CAACO,WAAW,IAAI,KAAK,EAAC,KAAG,EAACP,IAAI,CAACQ,QAAQ,EAAC,MAAI,EAACR,IAAI,CAAC2B,SAAS,GAAG3B,IAAI,CAAC2B,SAAS,CAAChB,cAAc,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG,OAAO;YAAA,GADrHV,CAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACLX,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACkC,WAAW,GAAGlC,KAAK,CAACkC,WAAW,CAACjB,cAAc,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG,OAAO;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/JX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAACG,KAAK,CAACmC,QAAQ,GAAGnC,KAAK,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1HX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACqC,aAAa,IAAI,KAAK;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvGG,KAAK,CAACsC,gBAAgB,iBAClBpD,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAACN,OAAA;cAAAM,QAAA,EAAQ;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClB,MAAM,CAACqB,KAAK,CAACsC,gBAAgB,CAAC,CAACf,MAAM,CAAC,KAAK,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC/H;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACNX,OAAA;UAAKO,SAAS,EAAC,wBAAwB;UAAAD,QAAA,eACnCN,OAAA;YACIqD,EAAE,EAAC,QAAQ;YACX9C,SAAS,EAAC,gKAAgK;YAC1KU,OAAO,EAAE6B,OAAQ;YAAAxC,QAAA,EACpB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC2C,GAAA,GA1CIT,gBAAgB;AA6CtB,MAAMU,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGnE,UAAU,CAACO,oBAAoB,CAAC;EAC3D,MAAM;IAAE6D,WAAW,EAAEC;EAAO,CAAC,GAAGH,IAAI;EACpC,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAExDF,SAAS,CAAC,MAAM;IACdI,gBAAgB,CAACgE,QAAQ,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,gBAAgB,GAAIjD,KAAK,IAAK;IAClCgD,gBAAgB,CAAChD,KAAK,CAAC;EACzB,CAAC;EAED,MAAMkD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMnE,iBAAiB,GAAG,MAAOgD,OAAO,IAAK;IAC3C,IAAI;MACF,MAAM/C,iBAAiB,CAAC+C,OAAO,CAAC;MAChCjD,gBAAgB,CAACgE,QAAQ,CAAC;MAC1BO,KAAK,CAAC,SAAStB,OAAO,0BAA0B,CAAC;IACnD,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CD,KAAK,CAAC,0BAA0BtB,OAAO,YAAYuB,KAAK,CAACE,OAAO,IAAI,eAAe,EAAE,CAAC;IACxF;EACF,CAAC;EAED,IAAIX,IAAI,CAACY,OAAO,EAAE;IAChB,oBACErE,OAAA;MAAKO,SAAS,EAAC,yDAAyD;MAAAD,QAAA,eACtEN,OAAA;QACEO,SAAS,EAAC,sCAAsC;QAChD+D,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,4BAA4B;QAAAnE,QAAA,eAElCN,OAAA;UACE0E,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAC,GAAG;UACfC,CAAC,EAAC;QAA6G;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EACA,oBACEX,OAAA,CAACX,QAAQ;IAAAiB,QAAA,gBACPN,OAAA;MAAKO,SAAS,EAAC,qDAAqD;MAAAD,QAAA,eAClEN,OAAA;QAAKO,SAAS,EAAC,QAAQ;QAAAD,QAAA,gBACrBN,OAAA;UAAKO,SAAS,EAAC,8DAA8D;UAAAD,QAAA,EAAC;QAE9E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNX,OAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNX,OAAA;UAAKO,SAAS,EAAC,sCAAsC;UAAAD,QAAA,gBACnDN,OAAA;YAAOO,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC9CN,OAAA,CAACK,WAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfX,OAAA;cAAAM,QAAA,EACGsD,MAAM,IAAIA,MAAM,CAACpC,MAAM,GAAG,CAAC,GAC1BoC,MAAM,CAACzC,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;gBACtB,oBAAOrB,OAAA,CAACa,SAAS;kBAASC,KAAK,EAAEM,IAAK;kBAACL,YAAY,EAAEgD,gBAAiB;kBAAC/C,aAAa,EAAErB;gBAAkB,GAAjF0B,CAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkF,CAAC;cAC7G,CAAC,CAAC,gBAEFX,OAAA;gBAAAM,QAAA,eACEN,OAAA;kBACE8E,OAAO,EAAC,GAAG;kBACXvE,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,EACnD;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRX,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAAD,QAAA,GAAC,QACpC,EAACsD,MAAM,IAAIA,MAAM,CAACpC,MAAM,EAAC,cACjC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA,CAAC6C,gBAAgB;MAAC/B,KAAK,EAAE+C,aAAc;MAACf,OAAO,EAAEkB;IAAiB;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7D,CAAC;AAEf,CAAC;AAAC6C,EAAA,CAxFID,eAAe;AAAAwB,GAAA,GAAfxB,eAAe;AA0FrB,MAAMyB,UAAU,GAAIC,KAAK,IAAK;EAC5B,oBACEjF,OAAA,CAACX,QAAQ;IAAAiB,QAAA,eACPN,OAAA,CAACH,MAAM;MAACS,QAAQ,eAAEN,OAAA,CAACuD,eAAe;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEf,CAAC;AAACuE,GAAA,GANIF,UAAU;AAQhB,eAAeA,UAAU;AAAC,IAAApE,EAAA,EAAAgC,GAAA,EAAAU,GAAA,EAAAyB,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAvE,EAAA;AAAAuE,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}