// EMERGENCY FIX SCRIPT
// Copy và paste script n<PERSON><PERSON> và<PERSON> Browser Console (F12) nếu không thể tương tác với trang

console.log('🚨 EMERGENCY FIX: Bắt đầu tìm và loại bỏ overlay...');

// 1. Tìm và ẩn tất cả overlay có thể gây vấn đề
function removeProblematicOverlays() {
  const elements = document.querySelectorAll('*');
  let removedCount = 0;
  
  elements.forEach(el => {
    const style = getComputedStyle(el);
    const rect = el.getBoundingClientRect();
    
    // <PERSON><PERSON><PERSON> tra overlay che phủ toàn màn hình
    if (
      (style.position === 'fixed' || style.position === 'absolute') &&
      rect.width > window.innerWidth * 0.7 &&
      rect.height > window.innerHeight * 0.7 &&
      style.zIndex > 10
    ) {
      // Không xóa các element debug
      if (!el.textContent.includes('Emergency') && 
          !el.textContent.includes('TEST CLICK') &&
          !el.className.includes('emergency')) {
        
        console.log('🗑️ Ẩn overlay:', el.className, 'Z-index:', style.zIndex);
        el.style.display = 'none';
        el.style.pointerEvents = 'none';
        removedCount++;
      }
    }
  });
  
  console.log(`✅ Đã ẩn ${removedCount} overlay có vấn đề`);
}

// 2. Force enable pointer events
function enablePointerEvents() {
  document.body.style.pointerEvents = 'auto';
  document.documentElement.style.pointerEvents = 'auto';
  
  // Remove pointer-events: none từ tất cả elements
  document.querySelectorAll('*').forEach(el => {
    if (getComputedStyle(el).pointerEvents === 'none') {
      el.style.pointerEvents = 'auto';
    }
  });
  
  console.log('✅ Đã enable pointer events');
}

// 3. Tạo test button
function createTestButton() {
  // Xóa button cũ
  const oldButton = document.getElementById('emergency-test-btn');
  if (oldButton) oldButton.remove();
  
  const btn = document.createElement('button');
  btn.id = 'emergency-test-btn';
  btn.innerHTML = '🧪 TEST CLICK - Nếu thấy nút này, click để test!';
  btn.style.cssText = `
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 999999 !important;
    background: #ff0000 !important;
    color: white !important;
    padding: 20px !important;
    border: 3px solid #ffffff !important;
    border-radius: 10px !important;
    font-size: 18px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.5) !important;
    animation: pulse 2s infinite !important;
  `;
  
  // Thêm animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0% { transform: translateX(-50%) scale(1); }
      50% { transform: translateX(-50%) scale(1.05); }
      100% { transform: translateX(-50%) scale(1); }
    }
  `;
  document.head.appendChild(style);
  
  btn.onclick = function() {
    alert('🎉 THÀNH CÔNG! Click đã hoạt động!\n\nBây giờ bạn có thể tương tác với trang web.');
    btn.style.background = '#00ff00';
    btn.innerHTML = '✅ CLICK HOẠT ĐỘNG! Trang web đã sửa!';
    
    setTimeout(() => {
      btn.style.display = 'none';
    }, 5000);
  };
  
  document.body.appendChild(btn);
  console.log('✅ Đã tạo test button');
}

// 4. Kiểm tra và sửa modal states
function fixModalStates() {
  // Reset localStorage
  const modalKeys = ['loginSignupModal', 'cartModal', 'navberHamburger'];
  modalKeys.forEach(key => {
    localStorage.removeItem(key);
  });
  
  // Tìm và ẩn các modal overlay
  document.querySelectorAll('.fixed').forEach(el => {
    const style = getComputedStyle(el);
    if (style.backgroundColor.includes('rgba(0, 0, 0') || 
        style.backgroundColor === 'rgb(0, 0, 0)' ||
        el.className.includes('opacity-50')) {
      console.log('🗑️ Ẩn modal overlay:', el.className);
      el.style.display = 'none';
    }
  });
  
  console.log('✅ Đã reset modal states');
}

// 5. Chạy tất cả fixes
function runEmergencyFix() {
  console.log('🚨 Bắt đầu Emergency Fix...');
  
  removeProblematicOverlays();
  enablePointerEvents();
  fixModalStates();
  createTestButton();
  
  console.log('🎯 Emergency Fix hoàn thành!');
  console.log('📋 Hướng dẫn tiếp theo:');
  console.log('1. Tìm nút TEST CLICK màu đỏ ở trên cùng trang');
  console.log('2. Click vào nút đó');
  console.log('3. Nếu thấy alert "THÀNH CÔNG" → vấn đề đã được sửa');
  console.log('4. Nếu không thấy nút hoặc không click được → chạy lại script này');
}

// Chạy fix
runEmergencyFix();

// Tự động chạy lại sau 2 giây
setTimeout(runEmergencyFix, 2000);

// Export function để có thể gọi lại
window.emergencyFix = runEmergencyFix;

console.log('💡 Để chạy lại fix, gõ: emergencyFix()');
