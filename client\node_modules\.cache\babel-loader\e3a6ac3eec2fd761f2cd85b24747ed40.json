{"ast": null, "code": "'use client';\n\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/utils';\nexport { default as capitalize } from './capitalize';\nexport { default as createChainedFunction } from './createChainedFunction';\nexport { default as createSvgIcon } from './createSvgIcon';\nexport { default as debounce } from './debounce';\nexport { default as deprecatedPropType } from './deprecatedPropType';\nexport { default as isMuiElement } from './isMuiElement';\nexport { default as ownerDocument } from './ownerDocument';\nexport { default as ownerWindow } from './ownerWindow';\nexport { default as requirePropFactory } from './requirePropFactory';\nexport { default as setRef } from './setRef';\nexport { default as unstable_useEnhancedEffect } from './useEnhancedEffect';\nexport { default as unstable_useId } from './useId';\nexport { default as unsupportedProp } from './unsupportedProp';\nexport { default as useControlled } from './useControlled';\nexport { default as useEventCallback } from './useEventCallback';\nexport { default as useForkRef } from './useForkRef';\nexport { default as useIsFocusVisible } from './useIsFocusVisible';\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};", "map": {"version": 3, "names": ["unstable_ClassNameGenerator", "ClassNameGenerator", "default", "capitalize", "createChainedFunction", "createSvgIcon", "debounce", "deprecatedPropType", "isMuiElement", "ownerDocument", "ownerWindow", "requirePropFactory", "setRef", "unstable_useEnhancedEffect", "unstable_useId", "unsupportedProp", "useControlled", "useEventCallback", "useForkRef", "useIsFocusVisible", "configure", "generator", "process", "env", "NODE_ENV", "console", "warn", "join"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/utils/index.js"], "sourcesContent": ["'use client';\n\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/utils';\nexport { default as capitalize } from './capitalize';\nexport { default as createChainedFunction } from './createChainedFunction';\nexport { default as createSvgIcon } from './createSvgIcon';\nexport { default as debounce } from './debounce';\nexport { default as deprecatedPropType } from './deprecatedPropType';\nexport { default as isMuiElement } from './isMuiElement';\nexport { default as ownerDocument } from './ownerDocument';\nexport { default as ownerWindow } from './ownerWindow';\nexport { default as requirePropFactory } from './requirePropFactory';\nexport { default as setRef } from './setRef';\nexport { default as unstable_useEnhancedEffect } from './useEnhancedEffect';\nexport { default as unstable_useId } from './useId';\nexport { default as unsupportedProp } from './unsupportedProp';\nexport { default as useControlled } from './useControlled';\nexport { default as useEventCallback } from './useEventCallback';\nexport { default as useForkRef } from './useForkRef';\nexport { default as useIsFocusVisible } from './useIsFocusVisible';\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AAC9E,SAASC,OAAO,IAAIC,UAAU,QAAQ,cAAc;AACpD,SAASD,OAAO,IAAIE,qBAAqB,QAAQ,yBAAyB;AAC1E,SAASF,OAAO,IAAIG,aAAa,QAAQ,iBAAiB;AAC1D,SAASH,OAAO,IAAII,QAAQ,QAAQ,YAAY;AAChD,SAASJ,OAAO,IAAIK,kBAAkB,QAAQ,sBAAsB;AACpE,SAASL,OAAO,IAAIM,YAAY,QAAQ,gBAAgB;AACxD,SAASN,OAAO,IAAIO,aAAa,QAAQ,iBAAiB;AAC1D,SAASP,OAAO,IAAIQ,WAAW,QAAQ,eAAe;AACtD,SAASR,OAAO,IAAIS,kBAAkB,QAAQ,sBAAsB;AACpE,SAAST,OAAO,IAAIU,MAAM,QAAQ,UAAU;AAC5C,SAASV,OAAO,IAAIW,0BAA0B,QAAQ,qBAAqB;AAC3E,SAASX,OAAO,IAAIY,cAAc,QAAQ,SAAS;AACnD,SAASZ,OAAO,IAAIa,eAAe,QAAQ,mBAAmB;AAC9D,SAASb,OAAO,IAAIc,aAAa,QAAQ,iBAAiB;AAC1D,SAASd,OAAO,IAAIe,gBAAgB,QAAQ,oBAAoB;AAChE,SAASf,OAAO,IAAIgB,UAAU,QAAQ,cAAc;AACpD,SAAShB,OAAO,IAAIiB,iBAAiB,QAAQ,qBAAqB;AAClE;AACA;AACA,OAAO,MAAMnB,2BAA2B,GAAG;EACzCoB,SAAS,EAAEC,SAAS,IAAI;IACtB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,CAAC,4GAA4G,EAAE,EAAE,EAAE,gGAAgG,EAAE,EAAE,EAAE,kGAAkG,EAAE,EAAE,EAAE,wEAAwE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACra;IACA1B,kBAAkB,CAACmB,SAAS,CAACC,SAAS,CAAC;EACzC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}