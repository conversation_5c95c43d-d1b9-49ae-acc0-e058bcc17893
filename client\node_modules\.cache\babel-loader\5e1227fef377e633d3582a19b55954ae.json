{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\DebugInteraction.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from \"react\";\nimport { LayoutContext } from \"./shop\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DebugInteraction = () => {\n  _s();\n  const [clickCount, setClickCount] = useState(0);\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const {\n    data,\n    dispatch\n  } = useContext(LayoutContext);\n  const handleClick = () => {\n    setClickCount(prev => prev + 1);\n    console.log(\"✅ Click event working! Count:\", clickCount + 1);\n  };\n  const handleMouseMove = e => {\n    setMousePosition({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n  const testModalToggle = () => {\n    console.log(\"🔄 Testing modal toggle\");\n    dispatch({\n      type: \"loginSignupModalToggle\",\n      payload: !data.loginSignupModal\n    });\n  };\n  const testCartToggle = () => {\n    console.log(\"🛒 Testing cart toggle\");\n    dispatch({\n      type: \"cartModalToggle\",\n      payload: !data.cartModal\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 bg-white border-2 border-red-500 p-4 rounded-lg shadow-lg z-50 max-w-sm\",\n    onMouseMove: handleMouseMove,\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-bold text-red-600 mb-4\",\n      children: \"\\uD83D\\uDD27 Debug Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2 text-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Click Test:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClick,\n          className: \"ml-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n          children: [\"Click Me (\", clickCount, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Mouse:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), \" X: \", mousePosition.x, \", Y: \", mousePosition.y]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Layout State:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-xs mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Login Modal: \", data.loginSignupModal ? \"OPEN\" : \"CLOSED\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Cart Modal: \", data.cartModal ? \"OPEN\" : \"CLOSED\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Hamburger: \", data.navberHamburger ? \"OPEN\" : \"CLOSED\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testModalToggle,\n          className: \"block w-full px-2 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600\",\n          children: \"Toggle Login Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testCartToggle,\n          className: \"block w-full px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600\",\n          children: \"Toggle Cart Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-600 mt-2\",\n        children: \"If buttons don't work, there's an overlay issue!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugInteraction, \"6svvwVnUp1weER4Va7JSwjHJ0lQ=\");\n_c = DebugInteraction;\nexport default DebugInteraction;\nvar _c;\n$RefreshReg$(_c, \"DebugInteraction\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "LayoutContext", "jsxDEV", "_jsxDEV", "DebugInteraction", "_s", "clickCount", "setClickCount", "mousePosition", "setMousePosition", "x", "y", "data", "dispatch", "handleClick", "prev", "console", "log", "handleMouseMove", "e", "clientX", "clientY", "testModalToggle", "type", "payload", "loginSignupModal", "testCartToggle", "cartModal", "className", "onMouseMove", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "navberHamburger", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/DebugInteraction.js"], "sourcesContent": ["import React, { useState, useContext } from \"react\";\nimport { LayoutContext } from \"./shop\";\n\nconst DebugInteraction = () => {\n  const [clickCount, setClickCount] = useState(0);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const { data, dispatch } = useContext(LayoutContext);\n\n  const handleClick = () => {\n    setClickCount(prev => prev + 1);\n    console.log(\"✅ Click event working! Count:\", clickCount + 1);\n  };\n\n  const handleMouseMove = (e) => {\n    setMousePosition({ x: e.clientX, y: e.clientY });\n  };\n\n  const testModalToggle = () => {\n    console.log(\"🔄 Testing modal toggle\");\n    dispatch({ type: \"loginSignupModalToggle\", payload: !data.loginSignupModal });\n  };\n\n  const testCartToggle = () => {\n    console.log(\"🛒 Testing cart toggle\");\n    dispatch({ type: \"cartModalToggle\", payload: !data.cartModal });\n  };\n\n  return (\n    <div \n      className=\"fixed top-4 right-4 bg-white border-2 border-red-500 p-4 rounded-lg shadow-lg z-50 max-w-sm\"\n      onMouseMove={handleMouseMove}\n    >\n      <h3 className=\"text-lg font-bold text-red-600 mb-4\">🔧 Debug Panel</h3>\n      \n      <div className=\"space-y-2 text-sm\">\n        <div>\n          <strong>Click Test:</strong>\n          <button \n            onClick={handleClick}\n            className=\"ml-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          >\n            Click Me ({clickCount})\n          </button>\n        </div>\n        \n        <div>\n          <strong>Mouse:</strong> X: {mousePosition.x}, Y: {mousePosition.y}\n        </div>\n        \n        <div>\n          <strong>Layout State:</strong>\n          <ul className=\"text-xs mt-1\">\n            <li>Login Modal: {data.loginSignupModal ? \"OPEN\" : \"CLOSED\"}</li>\n            <li>Cart Modal: {data.cartModal ? \"OPEN\" : \"CLOSED\"}</li>\n            <li>Hamburger: {data.navberHamburger ? \"OPEN\" : \"CLOSED\"}</li>\n          </ul>\n        </div>\n        \n        <div className=\"space-y-1\">\n          <button \n            onClick={testModalToggle}\n            className=\"block w-full px-2 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600\"\n          >\n            Toggle Login Modal\n          </button>\n          <button \n            onClick={testCartToggle}\n            className=\"block w-full px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600\"\n          >\n            Toggle Cart Modal\n          </button>\n        </div>\n        \n        <div className=\"text-xs text-gray-600 mt-2\">\n          If buttons don't work, there's an overlay issue!\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DebugInteraction;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,aAAa,QAAQ,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGb,UAAU,CAACC,aAAa,CAAC;EAEpD,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxBP,aAAa,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEX,UAAU,GAAG,CAAC,CAAC;EAC9D,CAAC;EAED,MAAMY,eAAe,GAAIC,CAAC,IAAK;IAC7BV,gBAAgB,CAAC;MAAEC,CAAC,EAAES,CAAC,CAACC,OAAO;MAAET,CAAC,EAAEQ,CAAC,CAACE;IAAQ,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BN,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCJ,QAAQ,CAAC;MAAEU,IAAI,EAAE,wBAAwB;MAAEC,OAAO,EAAE,CAACZ,IAAI,CAACa;IAAiB,CAAC,CAAC;EAC/E,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCJ,QAAQ,CAAC;MAAEU,IAAI,EAAE,iBAAiB;MAAEC,OAAO,EAAE,CAACZ,IAAI,CAACe;IAAU,CAAC,CAAC;EACjE,CAAC;EAED,oBACExB,OAAA;IACEyB,SAAS,EAAC,6FAA6F;IACvGC,WAAW,EAAEX,eAAgB;IAAAY,QAAA,gBAE7B3B,OAAA;MAAIyB,SAAS,EAAC,qCAAqC;MAAAE,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvE/B,OAAA;MAAKyB,SAAS,EAAC,mBAAmB;MAAAE,QAAA,gBAChC3B,OAAA;QAAA2B,QAAA,gBACE3B,OAAA;UAAA2B,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5B/B,OAAA;UACEgC,OAAO,EAAErB,WAAY;UACrBc,SAAS,EAAC,iEAAiE;UAAAE,QAAA,GAC5E,YACW,EAACxB,UAAU,EAAC,GACxB;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/B,OAAA;QAAA2B,QAAA,gBACE3B,OAAA;UAAA2B,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,QAAI,EAAC1B,aAAa,CAACE,CAAC,EAAC,OAAK,EAACF,aAAa,CAACG,CAAC;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN/B,OAAA;QAAA2B,QAAA,gBACE3B,OAAA;UAAA2B,QAAA,EAAQ;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9B/B,OAAA;UAAIyB,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC1B3B,OAAA;YAAA2B,QAAA,GAAI,eAAa,EAAClB,IAAI,CAACa,gBAAgB,GAAG,MAAM,GAAG,QAAQ;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjE/B,OAAA;YAAA2B,QAAA,GAAI,cAAY,EAAClB,IAAI,CAACe,SAAS,GAAG,MAAM,GAAG,QAAQ;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzD/B,OAAA;YAAA2B,QAAA,GAAI,aAAW,EAAClB,IAAI,CAACwB,eAAe,GAAG,MAAM,GAAG,QAAQ;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN/B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAE,QAAA,gBACxB3B,OAAA;UACEgC,OAAO,EAAEb,eAAgB;UACzBM,SAAS,EAAC,qFAAqF;UAAAE,QAAA,EAChG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/B,OAAA;UACEgC,OAAO,EAAET,cAAe;UACxBE,SAAS,EAAC,mFAAmF;UAAAE,QAAA,EAC9F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/B,OAAA;QAAKyB,SAAS,EAAC,4BAA4B;QAAAE,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA5EID,gBAAgB;AAAAiC,EAAA,GAAhBjC,gBAAgB;AA8EtB,eAAeA,gBAAgB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}