{"ast": null, "code": "export const dashboardState = {\n  totalData: [],\n  totalOrders: [],\n  uploadSliderBtn: true,\n  imageUpload: false,\n  sliderImages: []\n};\nexport const dashboardReducer = (state, action) => {\n  switch (action.type) {\n    case \"totalData\":\n      return {\n        ...state,\n        totalData: action.payload\n      };\n    case \"totalOrders\":\n      return {\n        ...state,\n        totalOrders: action.payload\n      };\n    case \"uploadSliderBtn\":\n      return {\n        ...state,\n        uploadSliderBtn: action.payload\n      };\n    case \"imageUpload\":\n      return {\n        ...state,\n        imageUpload: action.payload\n      };\n    case \"sliderImages\":\n      return {\n        ...state,\n        sliderImages: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["dashboardState", "totalData", "totalOrders", "uploadSliderBtn", "imageUpload", "sliderImages", "dashboardReducer", "state", "action", "type", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/admin/dashboardAdmin/DashboardContext.js"], "sourcesContent": ["export const dashboardState = {\r\n  totalData: [],\r\n  totalOrders: [],\r\n  uploadSliderBtn: true,\r\n  imageUpload: false,\r\n  sliderImages: [],\r\n};\r\n\r\nexport const dashboardReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"totalData\":\r\n      return {\r\n        ...state,\r\n        totalData: action.payload,\r\n      };\r\n    case \"totalOrders\":\r\n      return {\r\n        ...state,\r\n        totalOrders: action.payload,\r\n      };\r\n    case \"uploadSliderBtn\":\r\n      return {\r\n        ...state,\r\n        uploadSliderBtn: action.payload,\r\n      };\r\n    case \"imageUpload\":\r\n      return {\r\n        ...state,\r\n        imageUpload: action.payload,\r\n      };\r\n    case \"sliderImages\":\r\n      return {\r\n        ...state,\r\n        sliderImages: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,SAAS,EAAE,EAAE;EACbC,WAAW,EAAE,EAAE;EACfC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAE;AAChB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACjD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,WAAW;MACd,OAAO;QACL,GAAGF,KAAK;QACRN,SAAS,EAAEO,MAAM,CAACE;MACpB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRL,WAAW,EAAEM,MAAM,CAACE;MACtB,CAAC;IACH,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGH,KAAK;QACRJ,eAAe,EAAEK,MAAM,CAACE;MAC1B,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRH,WAAW,EAAEI,MAAM,CAACE;MACtB,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGH,KAAK;QACRF,YAAY,EAAEG,MAAM,CAACE;MACvB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}