[{"D:\\ITSS_Reference\\client\\src\\index.js": "1", "D:\\ITSS_Reference\\client\\src\\serviceWorker.js": "2", "D:\\ITSS_Reference\\client\\src\\App.js": "3", "D:\\ITSS_Reference\\client\\src\\components\\index.js": "4", "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js": "5", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js": "6", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js": "7", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js": "8", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js": "9", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js": "10", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js": "11", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js": "12", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js": "13", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js": "14", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js": "15", "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js": "16", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js": "17", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js": "18", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js": "19", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js": "20", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js": "21", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js": "22", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js": "23", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js": "24", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js": "25", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js": "26", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js": "27", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js": "28", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js": "29", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js": "30", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\Action.js": "31", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js": "32", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js": "33", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js": "34", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js": "35", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js": "36", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js": "37", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js": "38", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js": "39", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js": "40", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js": "41", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js": "42", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js": "43", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js": "44", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js": "45", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js": "46", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js": "47", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js": "48", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js": "49", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js": "50", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js": "51", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js": "52", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js": "53", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js": "54", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js": "55", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js": "56", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js": "57", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js": "58", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js": "59", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js": "60", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js": "61", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js": "62", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js": "63", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js": "64", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js": "65", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js": "66", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js": "67", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js": "68", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js": "69", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js": "70", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js": "71", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js": "72", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js": "73", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js": "74", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js": "75", "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js": "76", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js": "77", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js": "78", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js": "79", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js": "80", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js": "81", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js": "82", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js": "83", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js": "84", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js": "85", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js": "86", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js": "87", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js": "88", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js": "89", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js": "90", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js": "91", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js": "92"}, {"size": 390, "mtime": 1748329836515, "results": "93", "hashOfConfig": "94"}, {"size": 5227, "mtime": 1748329836515, "results": "95", "hashOfConfig": "94"}, {"size": 447, "mtime": 1748329836492, "results": "96", "hashOfConfig": "94"}, {"size": 2308, "mtime": 1748329836502, "results": "97", "hashOfConfig": "94"}, {"size": 884, "mtime": 1748329836508, "results": "98", "hashOfConfig": "94"}, {"size": 948, "mtime": 1748329836508, "results": "99", "hashOfConfig": "94"}, {"size": 1517, "mtime": 1748329836509, "results": "100", "hashOfConfig": "94"}, {"size": 5739, "mtime": 1748329836507, "results": "101", "hashOfConfig": "94"}, {"size": 586, "mtime": 1748329836502, "results": "102", "hashOfConfig": "94"}, {"size": 537, "mtime": 1748329836503, "results": "103", "hashOfConfig": "94"}, {"size": 558, "mtime": 1748329836502, "results": "104", "hashOfConfig": "94"}, {"size": 863, "mtime": 1748329836503, "results": "105", "hashOfConfig": "94"}, {"size": 307, "mtime": 1748329836509, "results": "106", "hashOfConfig": "94"}, {"size": 1066, "mtime": 1748329836508, "results": "107", "hashOfConfig": "94"}, {"size": 531, "mtime": 1748329836509, "results": "108", "hashOfConfig": "94"}, {"size": 216, "mtime": 1748329836497, "results": "109", "hashOfConfig": "94"}, {"size": 291, "mtime": 1748329836515, "results": "110", "hashOfConfig": "94"}, {"size": 176, "mtime": 1748329836506, "results": "111", "hashOfConfig": "94"}, {"size": 785, "mtime": 1748329836514, "results": "112", "hashOfConfig": "94"}, {"size": 7637, "mtime": 1748329836509, "results": "113", "hashOfConfig": "94"}, {"size": 3067, "mtime": 1748329836501, "results": "114", "hashOfConfig": "94"}, {"size": 5978, "mtime": 1748329836507, "results": "115", "hashOfConfig": "94"}, {"size": 2919, "mtime": 1748329836503, "results": "116", "hashOfConfig": "94"}, {"size": 6615, "mtime": 1748329836505, "results": "117", "hashOfConfig": "94"}, {"size": 505, "mtime": 1748329836513, "results": "118", "hashOfConfig": "94"}, {"size": 251, "mtime": 1748329836512, "results": "119", "hashOfConfig": "94"}, {"size": 5709, "mtime": 1748329836505, "results": "120", "hashOfConfig": "94"}, {"size": 4018, "mtime": 1748329836506, "results": "121", "hashOfConfig": "94"}, {"size": 4342, "mtime": 1748329836515, "results": "122", "hashOfConfig": "94"}, {"size": 818, "mtime": 1748329836510, "results": "123", "hashOfConfig": "94"}, {"size": 3051, "mtime": 1748329836509, "results": "124", "hashOfConfig": "94"}, {"size": 3528, "mtime": 1748329836507, "results": "125", "hashOfConfig": "94"}, {"size": 1591, "mtime": 1748329836506, "results": "126", "hashOfConfig": "94"}, {"size": 731, "mtime": 1748329836511, "results": "127", "hashOfConfig": "94"}, {"size": 490, "mtime": 1748329836510, "results": "128", "hashOfConfig": "94"}, {"size": 2876, "mtime": 1748329836507, "results": "129", "hashOfConfig": "94"}, {"size": 143, "mtime": 1748329836511, "results": "130", "hashOfConfig": "94"}, {"size": 840, "mtime": 1748329836499, "results": "131", "hashOfConfig": "94"}, {"size": 1333, "mtime": 1748329836506, "results": "132", "hashOfConfig": "94"}, {"size": 5590, "mtime": 1748329836503, "results": "133", "hashOfConfig": "94"}, {"size": 3935, "mtime": 1748329836503, "results": "134", "hashOfConfig": "94"}, {"size": 950, "mtime": 1748329836502, "results": "135", "hashOfConfig": "94"}, {"size": 3398, "mtime": 1748329836504, "results": "136", "hashOfConfig": "94"}, {"size": 17810, "mtime": 1750238708711, "results": "137", "hashOfConfig": "94"}, {"size": 1057, "mtime": 1748329836504, "results": "138", "hashOfConfig": "94"}, {"size": 903, "mtime": 1748329836494, "results": "139", "hashOfConfig": "94"}, {"size": 872, "mtime": 1748329836496, "results": "140", "hashOfConfig": "94"}, {"size": 380, "mtime": 1748329836514, "results": "141", "hashOfConfig": "94"}, {"size": 6795, "mtime": 1748329836507, "results": "142", "hashOfConfig": "94"}, {"size": 1802, "mtime": 1748329836495, "results": "143", "hashOfConfig": "94"}, {"size": 15593, "mtime": 1750239476963, "results": "144", "hashOfConfig": "94"}, {"size": 1113, "mtime": 1748329836498, "results": "145", "hashOfConfig": "94"}, {"size": 7398, "mtime": 1748329836498, "results": "146", "hashOfConfig": "94"}, {"size": 4434, "mtime": 1748329836498, "results": "147", "hashOfConfig": "94"}, {"size": 417, "mtime": 1748329836510, "results": "148", "hashOfConfig": "94"}, {"size": 1643, "mtime": 1748329836501, "results": "149", "hashOfConfig": "94"}, {"size": 953, "mtime": 1748329836504, "results": "150", "hashOfConfig": "94"}, {"size": 2168, "mtime": 1748329836512, "results": "151", "hashOfConfig": "94"}, {"size": 794, "mtime": 1748329836498, "results": "152", "hashOfConfig": "94"}, {"size": 550, "mtime": 1748329836504, "results": "153", "hashOfConfig": "94"}, {"size": 817, "mtime": 1748329836496, "results": "154", "hashOfConfig": "94"}, {"size": 6378, "mtime": 1748329836495, "results": "155", "hashOfConfig": "94"}, {"size": 5023, "mtime": 1748329836496, "results": "156", "hashOfConfig": "94"}, {"size": 6255, "mtime": 1748329836495, "results": "157", "hashOfConfig": "94"}, {"size": 1520, "mtime": 1748329836494, "results": "158", "hashOfConfig": "94"}, {"size": 6976, "mtime": 1748329836493, "results": "159", "hashOfConfig": "94"}, {"size": 1197, "mtime": 1748329836493, "results": "160", "hashOfConfig": "94"}, {"size": 3261, "mtime": 1748329836505, "results": "161", "hashOfConfig": "94"}, {"size": 757, "mtime": 1748329836512, "results": "162", "hashOfConfig": "94"}, {"size": 2870, "mtime": 1748329836513, "results": "163", "hashOfConfig": "94"}, {"size": 1553, "mtime": 1748329836514, "results": "164", "hashOfConfig": "94"}, {"size": 7078, "mtime": 1748329836501, "results": "165", "hashOfConfig": "94"}, {"size": 1460, "mtime": 1748329836501, "results": "166", "hashOfConfig": "94"}, {"size": 1625, "mtime": 1748329836494, "results": "167", "hashOfConfig": "94"}, {"size": 979, "mtime": 1748329836496, "results": "168", "hashOfConfig": "94"}, {"size": 604, "mtime": 1748329836497, "results": "169", "hashOfConfig": "94"}, {"size": 2579, "mtime": 1748329836497, "results": "170", "hashOfConfig": "94"}, {"size": 983, "mtime": 1748329836499, "results": "171", "hashOfConfig": "94"}, {"size": 4313, "mtime": 1748329836499, "results": "172", "hashOfConfig": "94"}, {"size": 18689, "mtime": 1748354314086, "results": "173", "hashOfConfig": "94"}, {"size": 4591, "mtime": 1748329836494, "results": "174", "hashOfConfig": "94"}, {"size": 7390, "mtime": 1748329836493, "results": "175", "hashOfConfig": "94"}, {"size": 5092, "mtime": 1748329836513, "results": "176", "hashOfConfig": "94"}, {"size": 13121, "mtime": 1748329836500, "results": "177", "hashOfConfig": "94"}, {"size": 6645, "mtime": 1748329836512, "results": "178", "hashOfConfig": "94"}, {"size": 11601, "mtime": 1748329836500, "results": "179", "hashOfConfig": "94"}, {"size": 1391, "mtime": 1748329836506, "results": "180", "hashOfConfig": "94"}, {"size": 427, "mtime": 1748329836499, "results": "181", "hashOfConfig": "94"}, {"size": 4821, "mtime": 1748329836500, "results": "182", "hashOfConfig": "94"}, {"size": 8123, "mtime": 1748354305174, "results": "183", "hashOfConfig": "94"}, {"size": 177, "mtime": 1748329836510, "results": "184", "hashOfConfig": "94"}, {"size": 1735, "mtime": 1748329836512, "results": "185", "hashOfConfig": "94"}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c4yg6", {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ITSS_Reference\\client\\src\\index.js", [], "D:\\ITSS_Reference\\client\\src\\serviceWorker.js", [], "D:\\ITSS_Reference\\client\\src\\App.js", [], "D:\\ITSS_Reference\\client\\src\\components\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js", []]