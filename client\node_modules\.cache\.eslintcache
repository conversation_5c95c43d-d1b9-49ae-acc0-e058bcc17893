[{"D:\\ITSS_Reference\\client\\src\\index.js": "1", "D:\\ITSS_Reference\\client\\src\\App.js": "2", "D:\\ITSS_Reference\\client\\src\\serviceWorker.js": "3", "D:\\ITSS_Reference\\client\\src\\components\\index.js": "4", "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js": "5", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js": "6", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js": "7", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js": "8", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js": "9", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js": "10", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js": "11", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js": "12", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js": "13", "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js": "14", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js": "15", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js": "16", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js": "17", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js": "18", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js": "19", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js": "20", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js": "21", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js": "22", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js": "23", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js": "24", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js": "25", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js": "26", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js": "27", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js": "28", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js": "29", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js": "30", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js": "31", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js": "32", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js": "33", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js": "34", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js": "35", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js": "36", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js": "37", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js": "38", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js": "39", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js": "40", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js": "41", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js": "42", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js": "43", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js": "44", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js": "45", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js": "46", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js": "47", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js": "48", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js": "49", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js": "50", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js": "51", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js": "52", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js": "53", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js": "54", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js": "55", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js": "56", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js": "57", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js": "58", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js": "59", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js": "60", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js": "61", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js": "62", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js": "63", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js": "64", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js": "65", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js": "66", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js": "67", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js": "68", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js": "69", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js": "70", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js": "71", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js": "72", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js": "73", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js": "74", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js": "75", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js": "76", "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js": "77", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js": "78", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js": "79", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js": "80", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js": "81", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js": "82", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js": "83", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js": "84", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js": "85", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js": "86", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js": "87", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js": "88", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js": "89", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js": "90", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js": "91", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CreateOrderPage.js": "92", "D:\\ITSS_Reference\\client\\src\\components\\TestProducts.js": "93", "D:\\ITSS_Reference\\client\\src\\utils\\checkBackend.js": "94", "D:\\ITSS_Reference\\client\\src\\components\\DebugInteraction.js": "95", "D:\\ITSS_Reference\\client\\src\\components\\SimpleTest.js": "96", "D:\\ITSS_Reference\\client\\src\\components\\ErrorBoundary.js": "97", "D:\\ITSS_Reference\\client\\src\\components\\QuickFix.js": "98", "D:\\ITSS_Reference\\client\\src\\components\\EmergencyFix.js": "99", "D:\\ITSS_Reference\\client\\src\\components\\SafeLayout.js": "100"}, {"size": 610, "mtime": 1751875084778, "results": "101", "hashOfConfig": "102"}, {"size": 447, "mtime": 1751872938023, "results": "103", "hashOfConfig": "102"}, {"size": 5227, "mtime": 1751872938045, "results": "104", "hashOfConfig": "102"}, {"size": 2865, "mtime": 1751875491702, "results": "105", "hashOfConfig": "102"}, {"size": 884, "mtime": 1751872938038, "results": "106", "hashOfConfig": "102"}, {"size": 948, "mtime": 1751872938038, "results": "107", "hashOfConfig": "102"}, {"size": 301, "mtime": 1751873956711, "results": "108", "hashOfConfig": "102"}, {"size": 1517, "mtime": 1751872938039, "results": "109", "hashOfConfig": "102"}, {"size": 5739, "mtime": 1751872938037, "results": "110", "hashOfConfig": "102"}, {"size": 1288, "mtime": 1751872938034, "results": "111", "hashOfConfig": "102"}, {"size": 558, "mtime": 1751872938033, "results": "112", "hashOfConfig": "102"}, {"size": 537, "mtime": 1751872938033, "results": "113", "hashOfConfig": "102"}, {"size": 586, "mtime": 1751872938033, "results": "114", "hashOfConfig": "102"}, {"size": 216, "mtime": 1751872938028, "results": "115", "hashOfConfig": "102"}, {"size": 176, "mtime": 1751872938036, "results": "116", "hashOfConfig": "102"}, {"size": 869, "mtime": 1751875361202, "results": "117", "hashOfConfig": "102"}, {"size": 1479, "mtime": 1751874671703, "results": "118", "hashOfConfig": "102"}, {"size": 291, "mtime": 1751872938045, "results": "119", "hashOfConfig": "102"}, {"size": 785, "mtime": 1751872938044, "results": "120", "hashOfConfig": "102"}, {"size": 4954, "mtime": 1751873116229, "results": "121", "hashOfConfig": "102"}, {"size": 6045, "mtime": 1751872938031, "results": "122", "hashOfConfig": "102"}, {"size": 10701, "mtime": 1751873116228, "results": "123", "hashOfConfig": "102"}, {"size": 6615, "mtime": 1751872938035, "results": "124", "hashOfConfig": "102"}, {"size": 4018, "mtime": 1751872938036, "results": "125", "hashOfConfig": "102"}, {"size": 2919, "mtime": 1751872938033, "results": "126", "hashOfConfig": "102"}, {"size": 1590, "mtime": 1751874430926, "results": "127", "hashOfConfig": "102"}, {"size": 2876, "mtime": 1751872938038, "results": "128", "hashOfConfig": "102"}, {"size": 6574, "mtime": 1751872938037, "results": "129", "hashOfConfig": "102"}, {"size": 3528, "mtime": 1751872938037, "results": "130", "hashOfConfig": "102"}, {"size": 4342, "mtime": 1751872938044, "results": "131", "hashOfConfig": "102"}, {"size": 505, "mtime": 1751872938043, "results": "132", "hashOfConfig": "102"}, {"size": 251, "mtime": 1751872938042, "results": "133", "hashOfConfig": "102"}, {"size": 2996, "mtime": 1751873116230, "results": "134", "hashOfConfig": "102"}, {"size": 950, "mtime": 1751872938032, "results": "135", "hashOfConfig": "102"}, {"size": 872, "mtime": 1751872938028, "results": "136", "hashOfConfig": "102"}, {"size": 840, "mtime": 1751872938030, "results": "137", "hashOfConfig": "102"}, {"size": 903, "mtime": 1751872938026, "results": "138", "hashOfConfig": "102"}, {"size": 143, "mtime": 1751872938041, "results": "139", "hashOfConfig": "102"}, {"size": 3772, "mtime": 1751873116226, "results": "140", "hashOfConfig": "102"}, {"size": 5590, "mtime": 1751872938034, "results": "141", "hashOfConfig": "102"}, {"size": 3935, "mtime": 1751872938033, "results": "142", "hashOfConfig": "102"}, {"size": 1804, "mtime": 1751874115867, "results": "143", "hashOfConfig": "102"}, {"size": 1333, "mtime": 1751872938036, "results": "144", "hashOfConfig": "102"}, {"size": 1057, "mtime": 1751872938035, "results": "145", "hashOfConfig": "102"}, {"size": 1391, "mtime": 1751872938037, "results": "146", "hashOfConfig": "102"}, {"size": 6777, "mtime": 1751874478044, "results": "147", "hashOfConfig": "102"}, {"size": 1187, "mtime": 1751872938044, "results": "148", "hashOfConfig": "102"}, {"size": 19929, "mtime": 1751872938043, "results": "149", "hashOfConfig": "102"}, {"size": 1469, "mtime": 1751873116225, "results": "150", "hashOfConfig": "102"}, {"size": 1460, "mtime": 1751872938032, "results": "151", "hashOfConfig": "102"}, {"size": 1643, "mtime": 1751872938032, "results": "152", "hashOfConfig": "102"}, {"size": 7078, "mtime": 1751872938032, "results": "153", "hashOfConfig": "102"}, {"size": 817, "mtime": 1751872938027, "results": "154", "hashOfConfig": "102"}, {"size": 5023, "mtime": 1751872938027, "results": "155", "hashOfConfig": "102"}, {"size": 417, "mtime": 1751872938041, "results": "156", "hashOfConfig": "102"}, {"size": 16675, "mtime": 1751872938040, "results": "157", "hashOfConfig": "102"}, {"size": 1197, "mtime": 1751872938025, "results": "158", "hashOfConfig": "102"}, {"size": 18689, "mtime": 1751872938041, "results": "159", "hashOfConfig": "102"}, {"size": 7845, "mtime": 1751873116225, "results": "160", "hashOfConfig": "102"}, {"size": 4183, "mtime": 1751873116225, "results": "161", "hashOfConfig": "102"}, {"size": 1520, "mtime": 1751872938025, "results": "162", "hashOfConfig": "102"}, {"size": 1113, "mtime": 1751872938029, "results": "163", "hashOfConfig": "102"}, {"size": 6976, "mtime": 1751872938025, "results": "164", "hashOfConfig": "102"}, {"size": 2911, "mtime": 1751873116226, "results": "165", "hashOfConfig": "102"}, {"size": 3259, "mtime": 1751873116228, "results": "166", "hashOfConfig": "102"}, {"size": 6378, "mtime": 1751872938027, "results": "167", "hashOfConfig": "102"}, {"size": 3512, "mtime": 1751872938027, "results": "168", "hashOfConfig": "102"}, {"size": 550, "mtime": 1751872938034, "results": "169", "hashOfConfig": "102"}, {"size": 6255, "mtime": 1751872938027, "results": "170", "hashOfConfig": "102"}, {"size": 2763, "mtime": 1751872938026, "results": "171", "hashOfConfig": "102"}, {"size": 731, "mtime": 1751872938041, "results": "172", "hashOfConfig": "102"}, {"size": 1754, "mtime": 1751872938042, "results": "173", "hashOfConfig": "102"}, {"size": 2168, "mtime": 1751872938042, "results": "174", "hashOfConfig": "102"}, {"size": 3480, "mtime": 1751872938040, "results": "175", "hashOfConfig": "102"}, {"size": 2870, "mtime": 1751872938043, "results": "176", "hashOfConfig": "102"}, {"size": 1553, "mtime": 1751872938043, "results": "177", "hashOfConfig": "102"}, {"size": 604, "mtime": 1751872938028, "results": "178", "hashOfConfig": "102"}, {"size": 13121, "mtime": 1751872938031, "results": "179", "hashOfConfig": "102"}, {"size": 11601, "mtime": 1751872938031, "results": "180", "hashOfConfig": "102"}, {"size": 1544, "mtime": 1751874057036, "results": "181", "hashOfConfig": "102"}, {"size": 177, "mtime": 1751872938040, "results": "182", "hashOfConfig": "102"}, {"size": 983, "mtime": 1751872938029, "results": "183", "hashOfConfig": "102"}, {"size": 4591, "mtime": 1751872938025, "results": "184", "hashOfConfig": "102"}, {"size": 7390, "mtime": 1751872938024, "results": "185", "hashOfConfig": "102"}, {"size": 4143, "mtime": 1751873116225, "results": "186", "hashOfConfig": "102"}, {"size": 5092, "mtime": 1751872938043, "results": "187", "hashOfConfig": "102"}, {"size": 6645, "mtime": 1751872938042, "results": "188", "hashOfConfig": "102"}, {"size": 427, "mtime": 1751872938030, "results": "189", "hashOfConfig": "102"}, {"size": 4821, "mtime": 1751872938030, "results": "190", "hashOfConfig": "102"}, {"size": 8123, "mtime": 1751872938030, "results": "191", "hashOfConfig": "102"}, {"size": 1735, "mtime": 1751872938042, "results": "192", "hashOfConfig": "102"}, {"size": 21835, "mtime": 1751873116229, "results": "193", "hashOfConfig": "102"}, {"size": 3960, "mtime": 1751874626870, "results": "194", "hashOfConfig": "102"}, {"size": 1329, "mtime": 1751874603566, "results": "195", "hashOfConfig": "102"}, {"size": 2629, "mtime": 1751874950887, "results": "196", "hashOfConfig": "102"}, {"size": 3676, "mtime": 1751875020586, "results": "197", "hashOfConfig": "102"}, {"size": 3060, "mtime": 1751875059472, "results": "198", "hashOfConfig": "102"}, {"size": 4737, "mtime": 1751875151670, "results": "199", "hashOfConfig": "102"}, {"size": 4943, "mtime": 1751875335220, "results": "200", "hashOfConfig": "102"}, {"size": 6297, "mtime": 1751875469063, "results": "201", "hashOfConfig": "102"}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c4yg6", {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "207", "messages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "209", "messages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "213", "messages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "215", "messages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "217", "messages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "219", "messages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "221", "messages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "223", "messages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "225", "messages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "227", "messages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "229", "messages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "231", "messages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "233", "messages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "239", "messages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "241", "messages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "243", "usedDeprecatedRules": "206"}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "260", "usedDeprecatedRules": "206"}, {"filePath": "261", "messages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "263", "messages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "265", "messages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "267", "messages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "269", "messages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "271", "messages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "273", "messages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "275", "messages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "277", "messages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "279", "messages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "281", "messages": "282", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "283", "usedDeprecatedRules": "206"}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "292", "messages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "294", "messages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "296", "messages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "300", "messages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "302", "messages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "304", "messages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "306", "messages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "308", "messages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "310", "messages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "312", "messages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "314", "messages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "316", "messages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "318", "messages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "320", "messages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "322", "messages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "324", "messages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "326", "messages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "328", "messages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "330", "messages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "332", "messages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "334", "usedDeprecatedRules": "206"}, {"filePath": "335", "messages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "337", "messages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "339", "messages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "341", "usedDeprecatedRules": "206"}, {"filePath": "342", "messages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "344", "messages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "346", "messages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "348", "usedDeprecatedRules": "206"}, {"filePath": "349", "messages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "351", "messages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "353", "messages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "355", "messages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "357", "messages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "359", "messages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "361", "messages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "363", "messages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "365", "messages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "367", "messages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "369", "messages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "371", "messages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "373", "messages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "375", "messages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "377", "messages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "379", "messages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "381", "messages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "383", "messages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "385", "messages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "387", "messages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "389", "messages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "391", "messages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ITSS_Reference\\client\\src\\index.js", [], "D:\\ITSS_Reference\\client\\src\\App.js", [], ["409", "410"], "D:\\ITSS_Reference\\client\\src\\serviceWorker.js", [], "D:\\ITSS_Reference\\client\\src\\components\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js", ["411"], "import React, { Fragment, useContext, useState } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { LayoutContext } from \"../layout\";\r\nimport { createOrder } from \"./FetchApi\";\r\n\r\n// Placeholder for a message box/modal, replacing alert()\r\nconst MessageBox = ({ message, onClose }) => {\r\n  if (!message) return null;\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white p-6 rounded-lg shadow-xl max-w-sm w-full\">\r\n        <p className=\"text-lg text-gray-800 mb-4\">{message}</p>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition\"\r\n        >\r\n          OK\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst CheckoutProducts = () => {\r\n  const history = useHistory();\r\n  const { data } = useContext(LayoutContext);\r\n  const [message, setMessage] = useState(null); // For custom message box\r\n\r\n  // Use CartDTO structure from context\r\n  const cart = data.cart || {};\r\n  const products = cart.items || [];\r\n  const total = products.reduce((sum, item) => sum + (item.product.pPrice * item.quantity), 0);\r\n\r\n  const isLoggedIn = !!localStorage.getItem(\"token\");\r\n  // Function to handle message box close\r\n  const handleMessageClose = () => {    \r\n    setMessage(null);\r\n  }\r\n  // Call createOrder API with CartDTO and redirect to /order/create with orderId\r\n  const handleCreateOrder = async () => {\r\n    if (!isLoggedIn) {\r\n      setMessage(\"Please log in to create an order.\");\r\n      history.push(\"/login\");\r\n      return;\r\n    }\r\n    if (products.length === 0) {\r\n      setMessage(\"Your cart is empty. Please add products to create an order.\");\r\n      return;\r\n    }\r\n    try {\r\n      const result = await createOrder(cart);\r\n      const orderId = result.orderId;\r\n      history.push(\"/user/order/create\", { orderId });\r\n      setMessage(`Order created successfully! Order ID: ${orderId}.`);\r\n      console.log(`${orderId}`);\r\n    } catch (err) {\r\n      setMessage(\"Failed to create order: \" + (err.message || err.toString()));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"mx-4 mt-20 md:mx-12 md:mt-32 lg:mt-24 font-inter\">\r\n      <MessageBox message={message} onClose={handleMessageClose} />\r\n\r\n      <h2 className=\"text-3xl font-bold mb-6 text-gray-800\">Checkout</h2>\r\n\r\n      {/* Grouping of Cart Items and Cart Total */}\r\n      <div className=\"p-6 bg-white rounded-lg shadow-md mb-8\">\r\n        <h3 className=\"text-xl font-semibold mb-4 text-gray-700\">Your Cart Items</h3>\r\n        <div className=\"grid grid-cols-1 gap-4\">\r\n          {products.length > 0 ? (\r\n            products.map((item, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg bg-gray-50\"\r\n              >\r\n                {/* Product Image */}\r\n                <img\r\n                  onClick={() => history.push(`/products/${item.product._id}`)}\r\n                    className=\"cursor-pointer md:h-20 md:w-20 object-cover object-center\"\r\n                    src={`${apiURL}/uploads/products/${item.product.pImages[0]}`}\r\n                    alt=\"product\"\r\n                />\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"text-lg font-semibold text-gray-800 truncate\">\r\n                    {item.product.pName}\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">\r\n                    Price: {item.product.pPrice.toLocaleString('vi-VN')}₫\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">\r\n                    Quantity: {item.quantity}\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-bold text-gray-700 flex-shrink-0\">\r\n                  Subtotal: {(item.product.pPrice * item.quantity).toLocaleString('vi-VN')}₫\r\n                </div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"text-center text-gray-600 p-4\">No products in your cart. Add some to proceed!</div>\r\n          )}\r\n        </div>\r\n        {/* Cart Total Display */}\r\n        <div className=\"mt-6 pt-4 border-t border-gray-200 flex justify-between text-xl font-bold text-gray-800\">\r\n          <span>Cart Total</span>\r\n          <span>{total.toLocaleString('vi-VN')}₫</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"mt-6 flex justify-end\">\r\n        <button\r\n          className=\"bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 transition duration-300 ease-in-out shadow-lg text-lg font-bold\"\r\n          onClick={handleCreateOrder}\r\n        >\r\n          Create Order\r\n        </button>\r\n      </div>\r\n      <div className=\"h-20\"></div> {/* Spacer for bottom padding */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CheckoutProducts;", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js", ["412"], "import React, { Fragment, useState, useEffect, useContext } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { getAllProduct } from \"../../admin/products/FetchApi\";\r\nimport { HomeContext } from \"./index\";\r\nimport { isWishReq, unWishReq, isWish } from \"./Mixins\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst SingleProduct = (props) => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const { products } = data;\r\n  const history = useHistory();\r\n\r\n  /* WhisList State */\r\n  const [wList, setWlist] = useState(\r\n    JSON.parse(localStorage.getItem(\"wishList\"))\r\n  );\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      let responseData = await getAllProduct();\r\n      setTimeout(() => {\r\n        // Backend trả về array trực tiếp, không có wrapper object\r\n        if (responseData && Array.isArray(responseData)) {\r\n          dispatch({ type: \"setProducts\", payload: responseData });\r\n          dispatch({ type: \"loading\", payload: false });\r\n        } else {\r\n          console.log(\"No products found or invalid response format\");\r\n          dispatch({ type: \"setProducts\", payload: [] });\r\n          dispatch({ type: \"loading\", payload: false });\r\n        }\r\n      }, 500);\r\n    } catch (error) {\r\n      console.log(\"Error fetching products:\", error);\r\n      dispatch({ type: \"setProducts\", payload: [] });\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  };\r\n\r\n  if (data.loading) {\r\n    return (\r\n      <div className=\"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center py-24\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      {products && products.length > 0 ? (\r\n        products.map((item, index) => {\r\n          return (\r\n            <Fragment key={index}>\r\n              <div className=\"relative col-span-1 m-2\">\r\n                <img\r\n                  onClick={(e) => history.push(`/products/${item.productId}`)}\r\n                  className=\"w-full object-cover object-center cursor-pointer\"\r\n                  src={item.images && item.images.length > 0 ? item.images[0] : '/placeholder-image.jpg'}\r\n                  alt={item.name || 'Product'}\r\n                />\r\n                <div className=\"flex items-center justify-between mt-2\">\r\n                  <div className=\"text-gray-600 font-light truncate\">\r\n                    {item.name}\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>\r\n                      <svg\r\n                        className=\"w-4 h-4 fill-current text-yellow-700\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\r\n                        />\r\n                      </svg>\r\n                    </span>\r\n                    <span className=\"text-gray-700\">\r\n                      {item.relatedProducts ? item.relatedProducts.length : 0}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div>{item.price ? `${item.price.toLocaleString('vi-VN')} VND` : 'Price not available'}</div>\r\n                {/* WhisList Logic  */}\r\n                <div className=\"absolute top-0 right-0 mx-2 my-2 md:mx-4\">\r\n                  <svg\r\n                    onClick={(e) => isWishReq(e, item.productId, setWlist)}\r\n                    className={`${\r\n                      isWish(item.productId, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700 transition-all duration-300 ease-in`}\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\r\n                    />\r\n                  </svg>\r\n                  <svg\r\n                    onClick={(e) => unWishReq(e, item.productId, setWlist)}\r\n                    className={`${\r\n                      !isWish(item.productId, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700 transition-all duration-300 ease-in`}\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                {/* WhisList Logic End */}\r\n              </div>\r\n            </Fragment>\r\n          );\r\n        })\r\n      ) : (\r\n        <div className=\"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center py-24 text-2xl\">\r\n          No product found\r\n        </div>\r\n      )}\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default SingleProduct;\r\n", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js", ["413"], "import {\r\n  getUserById,\r\n  updatePersonalInformationFetch,\r\n  getOrderByUser,\r\n  updatePassword,\r\n} from \"./FetchApi\";\r\n\r\nexport const logout = () => {\r\n  localStorage.removeItem(\"jwt\");\r\n  localStorage.removeItem(\"cart\");\r\n  localStorage.removeItem(\"wishList\");\r\n  window.location.href = \"/\";\r\n};\r\n\r\nexport const fetchData = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let userId = JSON.parse(localStorage.getItem(\"jwt\"))\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).user._id\r\n    : \"\";\r\n  try {\r\n    let responseData = await getUserById(userId);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.User) {\r\n        dispatch({ type: \"userDetails\", payload: responseData.User });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const fetchOrderByUser = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let userId = JSON.parse(localStorage.getItem(\"jwt\"))\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).user._id\r\n    : \"\";\r\n  try {\r\n    let responseData = await getOrderByUser(userId);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.Order) {\r\n        console.log(responseData);\r\n        dispatch({ type: \"OrderByUser\", payload: responseData.Order });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const updatePersonalInformationAction = async (dispatch, fData) => {\r\n  const formData = {\r\n    uId: fData.id,\r\n    name: fData.name,\r\n    phoneNumber: fData.phone,\r\n  };\r\n  dispatch({ type: \"loading\", payload: true });\r\n  try {\r\n    let responseData = await updatePersonalInformationFetch(formData);\r\n    setTimeout(() => {\r\n      if (responseData && responseData.success) {\r\n        dispatch({ type: \"loading\", payload: false });\r\n        fetchData(dispatch);\r\n      }\r\n    }, 500);\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const handleChangePassword = async (fData, setFdata, dispatch) => {\r\n  if (!fData.newPassword || !fData.oldPassword || !fData.confirmPassword) {\r\n    setFdata({\r\n      ...fData,\r\n      error: \"Please provide your all password and a new password\",\r\n    });\r\n  } else if (fData.newPassword !== fData.confirmPassword) {\r\n    setFdata({ ...fData, error: \"Password does't match\" });\r\n  } else {\r\n    const formData = {\r\n      uId: JSON.parse(localStorage.getItem(\"jwt\")).user._id,\r\n      oldPassword: fData.oldPassword,\r\n      newPassword: fData.newPassword,\r\n    };\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      let responseData = await updatePassword(formData);\r\n      if (responseData && responseData.success) {\r\n        setFdata({\r\n          ...fData,\r\n          success: responseData.success,\r\n          error: \"\",\r\n          oldPassword: \"\",\r\n          newPassword: \"\",\r\n          confirmPassword: \"\",\r\n        });\r\n        dispatch({ type: \"loading\", payload: false });\r\n      } else if (responseData.error) {\r\n        dispatch({ type: \"loading\", payload: false });\r\n        setFdata({\r\n          ...fData,\r\n          error: responseData.error,\r\n          success: \"\",\r\n          oldPassword: \"\",\r\n          newPassword: \"\",\r\n          confirmPassword: \"\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport const handleCancelOrder = async (orderId, dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  try {\r\n    let responseData = await cancelOrder(orderId);\r\n    if (responseData && responseData.success) {\r\n      fetchOrderByUser(dispatch);\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n}", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js", ["414", "415"], "import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// ⚠️ WARNING: Backend does not have UserController\r\n// These functions return mock data or use alternative endpoints\r\n\r\nexport const getUserById = async (uId) => {\r\n  console.warn(\"getUserById: Backend does not have UserController. Returning mock user data.\");\r\n  try {\r\n    // Return mock user data based on JWT token\r\n    const jwt = localStorage.getItem(\"jwt\");\r\n    if (jwt) {\r\n      const userData = JSON.parse(jwt).user;\r\n      return {\r\n        success: true,\r\n        User: {\r\n          _id: userData.id || userData._id,\r\n          name: userData.username || userData.name || \"User\",\r\n          email: userData.email || \"<EMAIL>\",\r\n          phoneNumber: userData.phoneNumber || \"N/A\",\r\n          role: userData.role || 0\r\n        }\r\n      };\r\n    }\r\n    return { success: false, message: \"User not found\" };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updatePersonalInformationFetch = async (userData) => {\r\n  console.warn(\"updatePersonalInformationFetch: Backend does not have UserController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful update\r\n    return {\r\n      success: true,\r\n      message: \"User information updated successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getOrderByUser = async (uId) => {\r\n  try {\r\n    // Use the available backend endpoint for order history\r\n    let res = await axios.get(`${apiURL}/api/v1/order/history/${uId}`, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n      }\r\n    });\r\n    return {\r\n      success: true,\r\n      Order: res.data\r\n    };\r\n  } catch (error) {\r\n    console.log(\"Error fetching user orders:\", error);\r\n    // Return empty orders if endpoint fails\r\n    return {\r\n      success: true,\r\n      Order: []\r\n    };\r\n  }\r\n};\r\n\r\nexport const updatePassword = async (formData) => {\r\n  console.warn(\"updatePassword: Backend does not have UserController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful password update\r\n    return {\r\n      success: true,\r\n      message: \"Password updated successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const cancelOrder = async (orderId) => {\r\n  try {\r\n    let res = await axios.put(`${apiURL}/api/v1/order/cancel/${orderId}`, {}, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n      }\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(\"Error cancelling order:\", error);\r\n    throw error;\r\n  }\r\n}", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js", ["416", "417"], "import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// ⚠️ WARNING: Backend does not have CustomizeController\r\n// These functions return mock data or use alternative endpoints\r\n\r\nexport const DashboardData = async () => {\r\n  console.warn(\"DashboardData: Backend does not have CustomizeController. Returning mock dashboard data.\");\r\n  try {\r\n    // Use available endpoints to get real data\r\n    const [ordersRes, productsRes] = await Promise.all([\r\n      axios.get(`${apiURL}/api/v1/orders`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n        }\r\n      }).catch(() => ({ data: [] })),\r\n      axios.get(`${apiURL}/api/v1/products/manager`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n        }\r\n      }).catch(() => ({ data: [] }))\r\n    ]);\r\n\r\n    // Calculate mock dashboard statistics\r\n    const orders = ordersRes.data || [];\r\n    const products = productsRes.data || [];\r\n\r\n    return {\r\n      success: true,\r\n      totalData: {\r\n        totalOrders: orders.length,\r\n        totalProducts: products.length,\r\n        totalUsers: 12, // From data-init.sql\r\n        totalCategories: 9, // From data-init.sql\r\n        totalRevenue: orders.reduce((sum, order) => sum + (order.total || 0), 0)\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    // Return fallback mock data\r\n    return {\r\n      success: true,\r\n      totalData: {\r\n        totalOrders: 3,\r\n        totalProducts: 18,\r\n        totalUsers: 12,\r\n        totalCategories: 9,\r\n        totalRevenue: 6300000\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nexport const getSliderImages = async () => {\r\n  console.warn(\"getSliderImages: Backend does not have CustomizeController. Returning mock slider images.\");\r\n  try {\r\n    // Return mock slider images\r\n    return {\r\n      success: true,\r\n      sliderImages: [\r\n        {\r\n          _id: 1,\r\n          image: \"/placeholder-slider-1.jpg\",\r\n          title: \"Welcome to Marketly\",\r\n          description: \"Your one-stop e-commerce solution\"\r\n        },\r\n        {\r\n          _id: 2,\r\n          image: \"/placeholder-slider-2.jpg\",\r\n          title: \"Best Products\",\r\n          description: \"Quality products at great prices\"\r\n        }\r\n      ]\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, sliderImages: [] };\r\n  }\r\n};\r\n\r\nexport const postUploadImage = async (formData) => {\r\n  console.warn(\"postUploadImage: Backend does not have CustomizeController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful upload\r\n    return {\r\n      success: true,\r\n      message: \"Image uploaded successfully (mock response)\",\r\n      image: {\r\n        _id: Date.now(),\r\n        image: \"/placeholder-uploaded.jpg\",\r\n        title: \"New Slider Image\",\r\n        description: \"Uploaded image\"\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, message: \"Upload failed\" };\r\n  }\r\n};\r\n\r\nexport const postDeleteImage = async (id) => {\r\n  console.warn(\"postDeleteImage: Backend does not have CustomizeController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful deletion\r\n    return {\r\n      success: true,\r\n      message: \"Image deleted successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, message: \"Delete failed\" };\r\n  }\r\n};\r\n", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js", ["418", "419", "420", "421"], "import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst BearerToken = () =>\r\n  localStorage.getItem(\"jwt\")\r\n    ? JSON.parse(localStorage.getItem(\"jwt\")).token\r\n    : false;\r\nconst Headers = () => {\r\n  return {\r\n    headers: {\r\n      Authorization: `Bearer ${BearerToken()}`, // Fixed: Use Authorization instead of token\r\n    },\r\n  };\r\n};\r\n\r\n// ⚠️ WARNING: Backend does not have CategoryController\r\n// These functions return mock data or throw errors\r\nexport const getAllCategory = async () => {\r\n  console.warn(\"getAllCategory: Backend does not have CategoryController. Returning mock data.\");\r\n  try {\r\n    // Return mock categories based on the data-init.sql\r\n    return {\r\n      success: true,\r\n      Categories: [\r\n        { _id: 1, cName: \"Electronics\", cDescription: \"Electronic devices and gadgets\", cStatus: \"Active\" },\r\n        { _id: 2, cName: \"Fashion and Apparel\", cDescription: \"Clothing and accessories\", cStatus: \"Active\" },\r\n        { _id: 3, cName: \"Beauty and Personal Care\", cDescription: \"Cosmetics and personal care products\", cStatus: \"Active\" },\r\n        { _id: 4, cName: \"Furniture\", cDescription: \"Home and office furniture\", cStatus: \"Active\" },\r\n        { _id: 5, cName: \"Beverages\", cDescription: \"Drinks and beverages\", cStatus: \"Active\" },\r\n        { _id: 6, cName: \"Food\", cDescription: \"Food items and snacks\", cStatus: \"Active\" },\r\n        { _id: 7, cName: \"Household Essentials\", cDescription: \"Household cleaning and essentials\", cStatus: \"Active\" },\r\n        { _id: 8, cName: \"Toys and Hobbies\", cDescription: \"Toys and hobby-related products\", cStatus: \"Active\" },\r\n        { _id: 9, cName: \"Media\", cDescription: \"Books, movies, and music\", cStatus: \"Active\" }\r\n      ]\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const createCategory = async ({\r\n  cName,\r\n  cImage,\r\n  cDescription,\r\n  cStatus,\r\n}) => {\r\n  console.warn(\"createCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category creation is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n\r\nexport const editCategory = async (cId, des, status) => {\r\n  console.warn(\"editCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category editing is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n\r\nexport const deleteCategory = async (cId) => {\r\n  console.warn(\"deleteCategory: Backend does not have CategoryController. This operation is not supported.\");\r\n  return {\r\n    success: false,\r\n    message: \"Category deletion is not supported in the current backend implementation.\"\r\n  };\r\n};\r\n", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CreateOrderPage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\TestProducts.js", [], "D:\\ITSS_Reference\\client\\src\\utils\\checkBackend.js", [], "D:\\ITSS_Reference\\client\\src\\components\\DebugInteraction.js", [], "D:\\ITSS_Reference\\client\\src\\components\\SimpleTest.js", [], "D:\\ITSS_Reference\\client\\src\\components\\ErrorBoundary.js", [], "D:\\ITSS_Reference\\client\\src\\components\\QuickFix.js", [], "D:\\ITSS_Reference\\client\\src\\components\\EmergencyFix.js", ["422"], "D:\\ITSS_Reference\\client\\src\\components\\SafeLayout.js", [], {"ruleId": "423", "replacedBy": "424"}, {"ruleId": "425", "replacedBy": "426"}, {"ruleId": "427", "severity": 1, "message": "428", "line": 1, "column": 17, "nodeType": "429", "messageId": "430", "endLine": 1, "endColumn": 25}, {"ruleId": "427", "severity": 1, "message": "431", "line": 7, "column": 7, "nodeType": "429", "messageId": "430", "endLine": 7, "endColumn": 13}, {"ruleId": "432", "severity": 2, "message": "433", "line": 119, "column": 30, "nodeType": "429", "messageId": "434", "endLine": 119, "endColumn": 41}, {"ruleId": "435", "severity": 1, "message": "436", "line": 40, "column": 19, "nodeType": "437", "messageId": "438", "endLine": 43, "endColumn": 4}, {"ruleId": "435", "severity": 1, "message": "436", "line": 77, "column": 19, "nodeType": "437", "messageId": "438", "endLine": 80, "endColumn": 4}, {"ruleId": "435", "severity": 1, "message": "436", "line": 75, "column": 19, "nodeType": "437", "messageId": "438", "endLine": 78, "endColumn": 4}, {"ruleId": "435", "severity": 1, "message": "436", "line": 109, "column": 19, "nodeType": "437", "messageId": "438", "endLine": 112, "endColumn": 4}, {"ruleId": "427", "severity": 1, "message": "439", "line": 1, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 1, "endColumn": 13}, {"ruleId": "427", "severity": 1, "message": "431", "line": 2, "column": 7, "nodeType": "429", "messageId": "430", "endLine": 2, "endColumn": 13}, {"ruleId": "427", "severity": 1, "message": "440", "line": 8, "column": 7, "nodeType": "429", "messageId": "430", "endLine": 8, "endColumn": 14}, {"ruleId": "435", "severity": 1, "message": "436", "line": 36, "column": 19, "nodeType": "437", "messageId": "438", "endLine": 39, "endColumn": 4}, {"ruleId": "427", "severity": 1, "message": "441", "line": 1, "column": 8, "nodeType": "429", "messageId": "430", "endLine": 1, "endColumn": 13}, "no-native-reassign", ["442"], "no-negated-in-lhs", ["443"], "no-unused-vars", "'Fragment' is defined but never used.", "Identifier", "unusedVar", "'apiURL' is assigned a value but never used.", "no-undef", "'cancelOrder' is not defined.", "undef", "no-unreachable", "Unreachable code.", "BlockStatement", "unreachableCode", "'axios' is defined but never used.", "'Headers' is assigned a value but never used.", "'React' is defined but never used.", "no-global-assign", "no-unsafe-negation"]