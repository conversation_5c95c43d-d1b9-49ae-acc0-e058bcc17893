[{"D:\\ITSS_Reference\\client\\src\\index.js": "1", "D:\\ITSS_Reference\\client\\src\\serviceWorker.js": "2", "D:\\ITSS_Reference\\client\\src\\App.js": "3", "D:\\ITSS_Reference\\client\\src\\components\\index.js": "4", "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js": "5", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js": "6", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js": "7", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js": "8", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js": "9", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js": "10", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js": "11", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js": "12", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js": "13", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js": "14", "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js": "15", "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js": "16", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js": "17", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js": "18", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js": "19", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js": "20", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js": "21", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js": "22", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js": "23", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js": "24", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js": "25", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js": "26", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js": "27", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js": "28", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js": "29", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js": "30", "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\Action.js": "31", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js": "32", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js": "33", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js": "34", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js": "35", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js": "36", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js": "37", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js": "38", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js": "39", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js": "40", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js": "41", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js": "42", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js": "43", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js": "44", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js": "45", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js": "46", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js": "47", "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js": "48", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js": "49", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js": "50", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js": "51", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js": "52", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js": "53", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js": "54", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js": "55", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js": "56", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js": "57", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js": "58", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js": "59", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js": "60", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js": "61", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js": "62", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js": "63", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js": "64", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js": "65", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js": "66", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js": "67", "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js": "68", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js": "69", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js": "70", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js": "71", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js": "72", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js": "73", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js": "74", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js": "75", "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js": "76", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js": "77", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js": "78", "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js": "79", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js": "80", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js": "81", "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js": "82", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js": "83", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js": "84", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js": "85", "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js": "86", "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js": "87", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js": "88", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js": "89", "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js": "90", "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js": "91", "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js": "92", "D:\\ITSS_Reference\\client\\src\\components\\ApiTestPage.js": "93", "D:\\ITSS_Reference\\client\\src\\components\\shop\\cart\\FetchApi.js": "94", "D:\\ITSS_Reference\\client\\src\\components\\DebugProducts.js": "95"}, {"size": 390, "mtime": 1748329836515, "results": "96", "hashOfConfig": "97"}, {"size": 5227, "mtime": 1748329836515, "results": "98", "hashOfConfig": "97"}, {"size": 447, "mtime": 1748329836492, "results": "99", "hashOfConfig": "97"}, {"size": 2536, "mtime": 1751880762496, "results": "100", "hashOfConfig": "97"}, {"size": 884, "mtime": 1748329836508, "results": "101", "hashOfConfig": "97"}, {"size": 948, "mtime": 1748329836508, "results": "102", "hashOfConfig": "97"}, {"size": 1517, "mtime": 1748329836509, "results": "103", "hashOfConfig": "97"}, {"size": 5739, "mtime": 1748329836507, "results": "104", "hashOfConfig": "97"}, {"size": 586, "mtime": 1748329836502, "results": "105", "hashOfConfig": "97"}, {"size": 537, "mtime": 1748329836503, "results": "106", "hashOfConfig": "97"}, {"size": 558, "mtime": 1748329836502, "results": "107", "hashOfConfig": "97"}, {"size": 1005, "mtime": 1751876898576, "results": "108", "hashOfConfig": "97"}, {"size": 307, "mtime": 1748329836509, "results": "109", "hashOfConfig": "97"}, {"size": 1766, "mtime": 1751877559023, "results": "110", "hashOfConfig": "97"}, {"size": 531, "mtime": 1748329836509, "results": "111", "hashOfConfig": "97"}, {"size": 216, "mtime": 1748329836497, "results": "112", "hashOfConfig": "97"}, {"size": 291, "mtime": 1748329836515, "results": "113", "hashOfConfig": "97"}, {"size": 176, "mtime": 1748329836506, "results": "114", "hashOfConfig": "97"}, {"size": 785, "mtime": 1748329836514, "results": "115", "hashOfConfig": "97"}, {"size": 7637, "mtime": 1748329836509, "results": "116", "hashOfConfig": "97"}, {"size": 5263, "mtime": 1751877522933, "results": "117", "hashOfConfig": "97"}, {"size": 6943, "mtime": 1751880762527, "results": "118", "hashOfConfig": "97"}, {"size": 2919, "mtime": 1748329836503, "results": "119", "hashOfConfig": "97"}, {"size": 6615, "mtime": 1748329836505, "results": "120", "hashOfConfig": "97"}, {"size": 505, "mtime": 1748329836513, "results": "121", "hashOfConfig": "97"}, {"size": 251, "mtime": 1748329836512, "results": "122", "hashOfConfig": "97"}, {"size": 5709, "mtime": 1748329836505, "results": "123", "hashOfConfig": "97"}, {"size": 4018, "mtime": 1748329836506, "results": "124", "hashOfConfig": "97"}, {"size": 4342, "mtime": 1751878041617, "results": "125", "hashOfConfig": "97"}, {"size": 2615, "mtime": 1751877774218, "results": "126", "hashOfConfig": "97"}, {"size": 3406, "mtime": 1751877871085, "results": "127", "hashOfConfig": "97"}, {"size": 3528, "mtime": 1748329836507, "results": "128", "hashOfConfig": "97"}, {"size": 1591, "mtime": 1748329836506, "results": "129", "hashOfConfig": "97"}, {"size": 731, "mtime": 1748329836511, "results": "130", "hashOfConfig": "97"}, {"size": 887, "mtime": 1751877754262, "results": "131", "hashOfConfig": "97"}, {"size": 2876, "mtime": 1748329836507, "results": "132", "hashOfConfig": "97"}, {"size": 143, "mtime": 1748329836511, "results": "133", "hashOfConfig": "97"}, {"size": 840, "mtime": 1748329836499, "results": "134", "hashOfConfig": "97"}, {"size": 1333, "mtime": 1748329836506, "results": "135", "hashOfConfig": "97"}, {"size": 5590, "mtime": 1748329836503, "results": "136", "hashOfConfig": "97"}, {"size": 4638, "mtime": 1751877803500, "results": "137", "hashOfConfig": "97"}, {"size": 950, "mtime": 1748329836502, "results": "138", "hashOfConfig": "97"}, {"size": 3398, "mtime": 1748329836504, "results": "139", "hashOfConfig": "97"}, {"size": 17810, "mtime": 1750238708711, "results": "140", "hashOfConfig": "97"}, {"size": 1057, "mtime": 1748329836504, "results": "141", "hashOfConfig": "97"}, {"size": 903, "mtime": 1748329836494, "results": "142", "hashOfConfig": "97"}, {"size": 872, "mtime": 1748329836496, "results": "143", "hashOfConfig": "97"}, {"size": 380, "mtime": 1748329836514, "results": "144", "hashOfConfig": "97"}, {"size": 8046, "mtime": 1751877117738, "results": "145", "hashOfConfig": "97"}, {"size": 1804, "mtime": 1751877906850, "results": "146", "hashOfConfig": "97"}, {"size": 15593, "mtime": 1750239476963, "results": "147", "hashOfConfig": "97"}, {"size": 1113, "mtime": 1748329836498, "results": "148", "hashOfConfig": "97"}, {"size": 7398, "mtime": 1748329836498, "results": "149", "hashOfConfig": "97"}, {"size": 4434, "mtime": 1748329836498, "results": "150", "hashOfConfig": "97"}, {"size": 417, "mtime": 1748329836510, "results": "151", "hashOfConfig": "97"}, {"size": 1643, "mtime": 1748329836501, "results": "152", "hashOfConfig": "97"}, {"size": 953, "mtime": 1748329836504, "results": "153", "hashOfConfig": "97"}, {"size": 2168, "mtime": 1748329836512, "results": "154", "hashOfConfig": "97"}, {"size": 1138, "mtime": 1751877717243, "results": "155", "hashOfConfig": "97"}, {"size": 550, "mtime": 1748329836504, "results": "156", "hashOfConfig": "97"}, {"size": 817, "mtime": 1748329836496, "results": "157", "hashOfConfig": "97"}, {"size": 6378, "mtime": 1748329836495, "results": "158", "hashOfConfig": "97"}, {"size": 5023, "mtime": 1748329836496, "results": "159", "hashOfConfig": "97"}, {"size": 6255, "mtime": 1748329836495, "results": "160", "hashOfConfig": "97"}, {"size": 1520, "mtime": 1748329836494, "results": "161", "hashOfConfig": "97"}, {"size": 6976, "mtime": 1748329836493, "results": "162", "hashOfConfig": "97"}, {"size": 1197, "mtime": 1748329836493, "results": "163", "hashOfConfig": "97"}, {"size": 3261, "mtime": 1748329836505, "results": "164", "hashOfConfig": "97"}, {"size": 1719, "mtime": 1751877853817, "results": "165", "hashOfConfig": "97"}, {"size": 2870, "mtime": 1748329836513, "results": "166", "hashOfConfig": "97"}, {"size": 1553, "mtime": 1748329836514, "results": "167", "hashOfConfig": "97"}, {"size": 7078, "mtime": 1748329836501, "results": "168", "hashOfConfig": "97"}, {"size": 1460, "mtime": 1748329836501, "results": "169", "hashOfConfig": "97"}, {"size": 2935, "mtime": 1751877737082, "results": "170", "hashOfConfig": "97"}, {"size": 979, "mtime": 1748329836496, "results": "171", "hashOfConfig": "97"}, {"size": 604, "mtime": 1748329836497, "results": "172", "hashOfConfig": "97"}, {"size": 2856, "mtime": 1751879107245, "results": "173", "hashOfConfig": "97"}, {"size": 983, "mtime": 1748329836499, "results": "174", "hashOfConfig": "97"}, {"size": 4651, "mtime": 1751878988253, "results": "175", "hashOfConfig": "97"}, {"size": 18954, "mtime": 1751877206114, "results": "176", "hashOfConfig": "97"}, {"size": 4591, "mtime": 1748329836494, "results": "177", "hashOfConfig": "97"}, {"size": 7390, "mtime": 1748329836493, "results": "178", "hashOfConfig": "97"}, {"size": 5092, "mtime": 1748329836513, "results": "179", "hashOfConfig": "97"}, {"size": 13121, "mtime": 1748329836500, "results": "180", "hashOfConfig": "97"}, {"size": 6645, "mtime": 1748329836512, "results": "181", "hashOfConfig": "97"}, {"size": 11601, "mtime": 1748329836500, "results": "182", "hashOfConfig": "97"}, {"size": 1391, "mtime": 1748329836506, "results": "183", "hashOfConfig": "97"}, {"size": 427, "mtime": 1748329836499, "results": "184", "hashOfConfig": "97"}, {"size": 4821, "mtime": 1748329836500, "results": "185", "hashOfConfig": "97"}, {"size": 8123, "mtime": 1748354305174, "results": "186", "hashOfConfig": "97"}, {"size": 177, "mtime": 1748329836510, "results": "187", "hashOfConfig": "97"}, {"size": 1735, "mtime": 1748329836512, "results": "188", "hashOfConfig": "97"}, {"size": 9464, "mtime": 1751877163259, "results": "189", "hashOfConfig": "97"}, {"size": 1710, "mtime": 1751876916551, "results": "190", "hashOfConfig": "97"}, {"size": 3259, "mtime": 1751878803733, "results": "191", "hashOfConfig": "97"}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, "c4yg6", {"filePath": "195", "messages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "197", "messages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "199", "messages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "203", "messages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "205", "messages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "207", "messages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "209", "messages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "211", "messages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "213", "messages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "215", "messages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "217", "messages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "219", "messages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "221", "messages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "223", "messages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "225", "messages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "227", "messages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "229", "messages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "231", "messages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "233", "messages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "235", "messages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "237"}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "260", "messages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "262", "messages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "264", "messages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "266", "messages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "268", "messages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "270", "messages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "272", "messages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "292", "usedDeprecatedRules": "194"}, {"filePath": "293", "messages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "295", "messages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "297", "messages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "299", "messages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "301", "messages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "303", "messages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "305", "messages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "307", "messages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "309", "messages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "311", "messages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "313", "messages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "315", "messages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "317", "messages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "319", "messages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "321", "messages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "323", "messages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "325", "messages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "327", "messages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "329", "messages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "331", "messages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "333", "messages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "335", "messages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "337", "messages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "339", "messages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "341", "messages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "343", "messages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "345", "messages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "347", "messages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "349", "messages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "351", "messages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "353", "messages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "355", "messages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "357", "messages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "359", "messages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "361", "messages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "363", "messages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "365", "messages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "367", "messages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "369", "messages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "371", "messages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "373", "messages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "375", "messages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "377", "messages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "379", "messages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "381", "messages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, {"filePath": "383", "messages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "194"}, "D:\\ITSS_Reference\\client\\src\\index.js", [], ["385", "386"], "D:\\ITSS_Reference\\client\\src\\serviceWorker.js", [], "D:\\ITSS_Reference\\client\\src\\App.js", [], "D:\\ITSS_Reference\\client\\src\\components\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\PageNotFound.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\layoutContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductByCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\CartProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\ProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\AdminProtectedRoute.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\fetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutPage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\CheckoutProducts.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\SingleProduct.js", ["387"], "import React, { Fragment, useState, useEffect, useContext } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { getAllProduct } from \"../../admin/products/FetchApi\";\r\nimport { HomeContext } from \"./index\";\r\nimport { isWishReq, unWishReq, isWish } from \"./Mixins\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst SingleProduct = (props) => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const { products } = data;\r\n  const history = useHistory();\r\n\r\n  /* WhisList State */\r\n  const [wList, setWlist] = useState(\r\n    JSON.parse(localStorage.getItem(\"wishList\"))\r\n  );\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    console.log(\"🔍 SingleProduct: Starting fetchData...\");\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      console.log(\"📡 SingleProduct: Calling getAllProduct API...\");\r\n      let responseData = await getAllProduct();\r\n      console.log(\"📦 SingleProduct: API Response:\", responseData);\r\n      console.log(\"📊 SingleProduct: Response type:\", typeof responseData);\r\n      console.log(\"📋 SingleProduct: Is array:\", Array.isArray(responseData));\r\n\r\n      setTimeout(() => {\r\n        if (responseData && Array.isArray(responseData)) {\r\n          console.log(\"✅ SingleProduct: Setting products, count:\", responseData.length);\r\n          dispatch({ type: \"setProducts\", payload: responseData });\r\n          dispatch({ type: \"loading\", payload: false });\r\n        } else {\r\n          console.log(\"❌ SingleProduct: Invalid response format\");\r\n          console.log(\"Response:\", responseData);\r\n          dispatch({ type: \"loading\", payload: false });\r\n        }\r\n      }, 500);\r\n    } catch (error) {\r\n      console.error(\"❌ SingleProduct: Error fetching data:\", error);\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  };\r\n\r\n  if (data.loading) {\r\n    return (\r\n      <div className=\"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center py-24\">\r\n        <svg\r\n          className=\"w-12 h-12 animate-spin text-gray-600\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth=\"2\"\r\n            d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      {products && products.length > 0 ? (\r\n        products.map((item, index) => {\r\n          return (\r\n            <Fragment key={index}>\r\n              <div className=\"relative col-span-1 m-2\">\r\n                <img\r\n                  onClick={(e) => history.push(`/products/${item.productId}`)}\r\n                  className=\"w-full object-cover object-center cursor-pointer\"\r\n                  src={item.images && item.images.length > 0 ? item.images[0] : '/placeholder-image.jpg'}\r\n                  alt={item.name || 'Product'}\r\n                />\r\n                <div className=\"flex items-center justify-between mt-2\">\r\n                  <div className=\"text-gray-600 font-light truncate\">\r\n                    {item.name}\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>\r\n                      <svg\r\n                        className=\"w-4 h-4 fill-current text-yellow-700\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\r\n                        />\r\n                      </svg>\r\n                    </span>\r\n                    <span className=\"text-gray-700\">\r\n                      {/* Tạm thời hiển thị 0 vì backend chưa có reviews */}\r\n                      0\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div>{item.price ? `${item.price.toLocaleString('vi-VN')} VND` : 'Price not available'}</div>\r\n                {/* WhisList Logic  */}\r\n                <div className=\"absolute top-0 right-0 mx-2 my-2 md:mx-4\">\r\n                  <svg\r\n                    onClick={(e) => isWishReq(e, item.productId, setWlist)}\r\n                    className={`${\r\n                      isWish(item.productId, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700 transition-all duration-300 ease-in`}\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\r\n                    />\r\n                  </svg>\r\n                  <svg\r\n                    onClick={(e) => unWishReq(e, item.productId, setWlist)}\r\n                    className={`${\r\n                      !isWish(item.productId, wList) && \"hidden\"\r\n                    } w-5 h-5 md:w-6 md:h-6 cursor-pointer text-yellow-700 transition-all duration-300 ease-in`}\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                {/* WhisList Logic End */}\r\n              </div>\r\n            </Fragment>\r\n          );\r\n        })\r\n      ) : (\r\n        <div className=\"col-span-2 md:col-span-3 lg:col-span-4 flex items-center justify-center py-24 text-2xl\">\r\n          No product found\r\n        </div>\r\n      )}\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default SingleProduct;\r\n", "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\LoginSignup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\SettingUser.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Details.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\UserProfile.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\SingleWishProduct.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\order\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategory.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\HomeContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Slider.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Signup.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\auth\\Login.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSection.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Layout.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\wishlist\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\ProductCategoryDropdown.js", ["388", "389"], "import React, { Fragment, useContext, useState, useEffect } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { HomeContext } from \"./index\";\r\nimport { getAllCategory } from \"../../admin/categories/FetchApi\";\r\nimport { getAllProduct, productByPrice } from \"../../admin/products/FetchApi\";\r\nimport \"./style.css\";\r\n\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst CategoryList = () => {\r\n  const history = useHistory();\r\n  const { data } = useContext(HomeContext);\r\n  const [categories, setCategories] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      // Tạm thời sử dụng mock data cho categories vì backend chưa có CategoryController\r\n      const mockCategories = [\r\n        { _id: 'electronics', cName: 'Electronics', cImage: 'electronics.jpg' },\r\n        { _id: 'fashion', cName: 'Fashion', cImage: 'fashion.jpg' },\r\n        { _id: 'beauty', cName: 'Beauty', cImage: 'beauty.jpg' },\r\n        { _id: 'furniture', cName: 'Furniture', cImage: 'furniture.jpg' },\r\n        { _id: 'beverages', cName: 'Beverages', cImage: 'beverages.jpg' },\r\n        { _id: 'food', cName: 'Food', cImage: 'food.jpg' },\r\n        { _id: 'household', cName: 'Household', cImage: 'household.jpg' },\r\n        { _id: 'toys', cName: 'Toys', cImage: 'toys.jpg' },\r\n        { _id: 'media', cName: 'Media', cImage: 'media.jpg' }\r\n      ];\r\n      setCategories(mockCategories);\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`${data.categoryListDropdown ? \"\" : \"hidden\"} my-4`}>\r\n      <hr />\r\n      <div className=\"py-1 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4\">\r\n        {categories && categories.length > 0 ? (\r\n          categories.map((item, index) => {\r\n            return (\r\n              <Fragment key={index}>\r\n                <div\r\n                  onClick={(e) =>\r\n                    history.push(`/products/category/${item._id}`)\r\n                  }\r\n                  className=\"col-span-1 m-2 flex flex-col items-center justify-center space-y-2 cursor-pointer\"\r\n                >\r\n                  <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-2xl\">\r\n                      {item.cName === 'Electronics' && '📱'}\r\n                      {item.cName === 'Fashion' && '👕'}\r\n                      {item.cName === 'Beauty' && '💄'}\r\n                      {item.cName === 'Furniture' && '🪑'}\r\n                      {item.cName === 'Beverages' && '🥤'}\r\n                      {item.cName === 'Food' && '🍝'}\r\n                      {item.cName === 'Household' && '🧽'}\r\n                      {item.cName === 'Toys' && '🧸'}\r\n                      {item.cName === 'Media' && '📚'}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"font-medium\">{item.cName}</div>\r\n                </div>\r\n              </Fragment>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className=\"text-xl text-center my-4\">No Category</div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst FilterList = () => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const [range, setRange] = useState(0);\r\n\r\n  const rangeHandle = (e) => {\r\n    setRange(e.target.value);\r\n    fetchData(e.target.value);\r\n  };\r\n\r\n  const fetchData = async (price) => {\r\n    if (price === \"all\") {\r\n      try {\r\n        let responseData = await getAllProduct();\r\n        if (responseData && responseData.Products) {\r\n          dispatch({ type: \"setProducts\", payload: responseData.Products });\r\n        }\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    } else {\r\n      dispatch({ type: \"loading\", payload: true });\r\n      try {\r\n        setTimeout(async () => {\r\n          let responseData = await productByPrice(price);\r\n          if (responseData && responseData.Products) {\r\n            console.log(responseData.Products);\r\n            dispatch({ type: \"setProducts\", payload: responseData.Products });\r\n            dispatch({ type: \"loading\", payload: false });\r\n          }\r\n        }, 700);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const closeFilterBar = () => {\r\n    fetchData(\"all\");\r\n    dispatch({ type: \"filterListDropdown\", payload: !data.filterListDropdown });\r\n    setRange(0);\r\n  };\r\n\r\n  return (\r\n    <div className={`${data.filterListDropdown ? \"\" : \"hidden\"} my-4`}>\r\n      <hr />\r\n      <div className=\"w-full flex flex-col\">\r\n        <div className=\"font-medium py-2\">Filter by price</div>\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"flex flex-col space-y-2  w-2/3 lg:w-2/4\">\r\n            <label htmlFor=\"points\" className=\"text-sm\">\r\n              Price (between 0 and 10$):{\" \"}\r\n              <span className=\"font-semibold text-yellow-700\">{range}.00$</span>{\" \"}\r\n            </label>\r\n            <input\r\n              value={range}\r\n              className=\"slider\"\r\n              type=\"range\"\r\n              id=\"points\"\r\n              min=\"0\"\r\n              max=\"1000\"\r\n              step=\"10\"\r\n              onChange={(e) => rangeHandle(e)}\r\n            />\r\n          </div>\r\n          <div onClick={(e) => closeFilterBar()} className=\"cursor-pointer\">\r\n            <svg\r\n              className=\"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M6 18L18 6M6 6l12 12\"\r\n              />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Search = () => {\r\n  const { data, dispatch } = useContext(HomeContext);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [productArray, setPa] = useState(null);\r\n\r\n  const searchHandle = (e) => {\r\n    setSearch(e.target.value);\r\n    fetchData();\r\n    dispatch({\r\n      type: \"searchHandleInReducer\",\r\n      payload: e.target.value,\r\n      productArray: productArray,\r\n    });\r\n  };\r\n\r\n  const fetchData = async () => {\r\n    dispatch({ type: \"loading\", payload: true });\r\n    try {\r\n      setTimeout(async () => {\r\n        let responseData = await getAllProduct();\r\n        if (responseData && responseData.Products) {\r\n          setPa(responseData.Products);\r\n          dispatch({ type: \"loading\", payload: false });\r\n        }\r\n      }, 700);\r\n    } catch (error) {\r\n      console.log(error);\r\n    }\r\n  };\r\n\r\n  const closeSearchBar = () => {\r\n    dispatch({ type: \"searchDropdown\", payload: !data.searchDropdown });\r\n    fetchData();\r\n    dispatch({ type: \"setProducts\", payload: productArray });\r\n    setSearch(\"\");\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`${\r\n        data.searchDropdown ? \"\" : \"hidden\"\r\n      } my-4 flex items-center justify-between`}\r\n    >\r\n      <input\r\n        value={search}\r\n        onChange={(e) => searchHandle(e)}\r\n        className=\"px-4 text-xl py-4 focus:outline-none\"\r\n        type=\"text\"\r\n        placeholder=\"Search products...\"\r\n      />\r\n      <div onClick={(e) => closeSearchBar()} className=\"cursor-pointer\">\r\n        <svg\r\n          className=\"w-8 h-8 text-gray-700 hover:bg-gray-200 rounded-full p-1\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth={2}\r\n            d=\"M6 18L18 6M6 6l12 12\"\r\n          />\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ProductCategoryDropdown = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <CategoryList />\r\n      <FilterList />\r\n      <Search />\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default ProductCategoryDropdown;\r\n", "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\CartModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\AllOrders.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\OrderMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Footer.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Mixins.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\DashboardUserContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\Customize.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\TodaySell.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\DashboardCard.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AllCategories.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\CategoryContext.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\dashboardUser\\Sidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ProductDetailsSectionTwo.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Submenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductTable.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\ProductMenu.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\dashboardAdmin\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\layout\\index.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\Actions.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\SearchFilter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\orders\\UpdateOrderModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Navber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\EditCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\categories\\AddCategoryModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\ReviewForm.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\EditProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\AllReviews.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\products\\AddProductModal.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\home\\OrderSuccessMessage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminFooter.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminSidebar.js", [], "D:\\ITSS_Reference\\client\\src\\components\\admin\\partials\\AdminNavber.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\partials\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\productDetails\\Action.js", [], "D:\\ITSS_Reference\\client\\src\\components\\ApiTestPage.js", [], "D:\\ITSS_Reference\\client\\src\\components\\shop\\cart\\FetchApi.js", [], "D:\\ITSS_Reference\\client\\src\\components\\DebugProducts.js", [], {"ruleId": "390", "replacedBy": "391"}, {"ruleId": "392", "replacedBy": "393"}, {"ruleId": "394", "severity": 1, "message": "395", "line": 7, "column": 7, "nodeType": "396", "messageId": "397", "endLine": 7, "endColumn": 13}, {"ruleId": "394", "severity": 1, "message": "398", "line": 4, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 4, "endColumn": 24}, {"ruleId": "394", "severity": 1, "message": "395", "line": 8, "column": 7, "nodeType": "396", "messageId": "397", "endLine": 8, "endColumn": 13}, "no-native-reassign", ["399"], "no-negated-in-lhs", ["400"], "no-unused-vars", "'apiURL' is assigned a value but never used.", "Identifier", "unusedVar", "'getAllCategory' is defined but never used.", "no-global-assign", "no-unsafe-negation"]