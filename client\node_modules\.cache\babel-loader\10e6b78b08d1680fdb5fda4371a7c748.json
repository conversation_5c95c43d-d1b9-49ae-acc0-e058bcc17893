{"ast": null, "code": "var g;\n\n// This works in non-strict mode\ng = function () {\n  return this;\n}();\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;", "map": {"version": 3, "names": ["g", "Function", "e", "window", "module", "exports"], "sources": ["D:/ITSS_Reference/client/node_modules/webpack/buildin/global.js"], "sourcesContent": ["var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n"], "mappings": "AAAA,IAAIA,CAAC;;AAEL;AACAA,CAAC,GAAI,YAAW;EACf,OAAO,IAAI;AACZ,CAAC,CAAE,CAAC;AAEJ,IAAI;EACH;EACAA,CAAC,GAAGA,CAAC,IAAI,IAAIC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;EACX;EACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAEH,CAAC,GAAGG,MAAM;AAC3C;;AAEA;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}