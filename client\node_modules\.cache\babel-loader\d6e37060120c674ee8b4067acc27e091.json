{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\n\n// ⚠️ WARNING: Backend does not have CustomizeController\n// These functions return mock data or use alternative endpoints\n\nexport const DashboardData = async () => {\n  console.warn(\"DashboardData: Backend does not have CustomizeController. Returning mock dashboard data.\");\n  try {\n    // Use available endpoints to get real data\n    const [ordersRes, productsRes] = await Promise.all([axios.get(`${apiURL}/api/v1/orders`, {\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\n      }\n    }).catch(() => ({\n      data: []\n    })), axios.get(`${apiURL}/api/v1/products/manager`, {\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\n      }\n    }).catch(() => ({\n      data: []\n    }))]);\n\n    // Calculate mock dashboard statistics\n    const orders = ordersRes.data || [];\n    const products = productsRes.data || [];\n    return {\n      success: true,\n      totalData: {\n        totalOrders: orders.length,\n        totalProducts: products.length,\n        totalUsers: 12,\n        // From data-init.sql\n        totalCategories: 9,\n        // From data-init.sql\n        totalRevenue: orders.reduce((sum, order) => sum + (order.total || 0), 0)\n      }\n    };\n  } catch (error) {\n    console.log(error);\n    // Return fallback mock data\n    return {\n      success: true,\n      totalData: {\n        totalOrders: 3,\n        totalProducts: 18,\n        totalUsers: 12,\n        totalCategories: 9,\n        totalRevenue: 6300000\n      }\n    };\n  }\n};\n_c = DashboardData;\nexport const getSliderImages = async () => {\n  console.warn(\"getSliderImages: Backend does not have CustomizeController. Returning mock slider images.\");\n  try {\n    // Return mock slider images\n    return {\n      success: true,\n      sliderImages: [{\n        _id: 1,\n        image: \"/placeholder-slider-1.jpg\",\n        title: \"Welcome to Marketly\",\n        description: \"Your one-stop e-commerce solution\"\n      }, {\n        _id: 2,\n        image: \"/placeholder-slider-2.jpg\",\n        title: \"Best Products\",\n        description: \"Quality products at great prices\"\n      }]\n    };\n  } catch (error) {\n    console.log(error);\n    return {\n      success: false,\n      sliderImages: []\n    };\n  }\n};\nexport const postUploadImage = async formData => {\n  console.warn(\"postUploadImage: Backend does not have CustomizeController. This operation is not supported.\");\n  try {\n    // Mock successful upload\n    return {\n      success: true,\n      message: \"Image uploaded successfully (mock response)\",\n      image: {\n        _id: Date.now(),\n        image: \"/placeholder-uploaded.jpg\",\n        title: \"New Slider Image\",\n        description: \"Uploaded image\"\n      }\n    };\n  } catch (error) {\n    console.log(error);\n    return {\n      success: false,\n      message: \"Upload failed\"\n    };\n  }\n};\nexport const postDeleteImage = async id => {\n  console.warn(\"postDeleteImage: Backend does not have CustomizeController. This operation is not supported.\");\n  try {\n    // Mock successful deletion\n    return {\n      success: true,\n      message: \"Image deleted successfully (mock response)\"\n    };\n  } catch (error) {\n    console.log(error);\n    return {\n      success: false,\n      message: \"Delete failed\"\n    };\n  }\n};\nvar _c;\n$RefreshReg$(_c, \"DashboardData\");", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "DashboardData", "console", "warn", "ordersRes", "productsRes", "Promise", "all", "get", "headers", "localStorage", "getItem", "JSON", "parse", "token", "catch", "data", "orders", "products", "success", "totalData", "totalOrders", "length", "totalProducts", "totalUsers", "totalCategories", "totalRevenue", "reduce", "sum", "order", "total", "error", "log", "_c", "getSliderImages", "sliderImages", "_id", "image", "title", "description", "postUploadImage", "formData", "message", "Date", "now", "postDeleteImage", "id", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/admin/dashboardAdmin/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\n// ⚠️ WARNING: Backend does not have CustomizeController\r\n// These functions return mock data or use alternative endpoints\r\n\r\nexport const DashboardData = async () => {\r\n  console.warn(\"DashboardData: Backend does not have CustomizeController. Returning mock dashboard data.\");\r\n  try {\r\n    // Use available endpoints to get real data\r\n    const [ordersRes, productsRes] = await Promise.all([\r\n      axios.get(`${apiURL}/api/v1/orders`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n        }\r\n      }).catch(() => ({ data: [] })),\r\n      axios.get(`${apiURL}/api/v1/products/manager`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem(\"jwt\") ? JSON.parse(localStorage.getItem(\"jwt\")).token : \"\"}`\r\n        }\r\n      }).catch(() => ({ data: [] }))\r\n    ]);\r\n\r\n    // Calculate mock dashboard statistics\r\n    const orders = ordersRes.data || [];\r\n    const products = productsRes.data || [];\r\n\r\n    return {\r\n      success: true,\r\n      totalData: {\r\n        totalOrders: orders.length,\r\n        totalProducts: products.length,\r\n        totalUsers: 12, // From data-init.sql\r\n        totalCategories: 9, // From data-init.sql\r\n        totalRevenue: orders.reduce((sum, order) => sum + (order.total || 0), 0)\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    // Return fallback mock data\r\n    return {\r\n      success: true,\r\n      totalData: {\r\n        totalOrders: 3,\r\n        totalProducts: 18,\r\n        totalUsers: 12,\r\n        totalCategories: 9,\r\n        totalRevenue: 6300000\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nexport const getSliderImages = async () => {\r\n  console.warn(\"getSliderImages: Backend does not have CustomizeController. Returning mock slider images.\");\r\n  try {\r\n    // Return mock slider images\r\n    return {\r\n      success: true,\r\n      sliderImages: [\r\n        {\r\n          _id: 1,\r\n          image: \"/placeholder-slider-1.jpg\",\r\n          title: \"Welcome to Marketly\",\r\n          description: \"Your one-stop e-commerce solution\"\r\n        },\r\n        {\r\n          _id: 2,\r\n          image: \"/placeholder-slider-2.jpg\",\r\n          title: \"Best Products\",\r\n          description: \"Quality products at great prices\"\r\n        }\r\n      ]\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, sliderImages: [] };\r\n  }\r\n};\r\n\r\nexport const postUploadImage = async (formData) => {\r\n  console.warn(\"postUploadImage: Backend does not have CustomizeController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful upload\r\n    return {\r\n      success: true,\r\n      message: \"Image uploaded successfully (mock response)\",\r\n      image: {\r\n        _id: Date.now(),\r\n        image: \"/placeholder-uploaded.jpg\",\r\n        title: \"New Slider Image\",\r\n        description: \"Uploaded image\"\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, message: \"Upload failed\" };\r\n  }\r\n};\r\n\r\nexport const postDeleteImage = async (id) => {\r\n  console.warn(\"postDeleteImage: Backend does not have CustomizeController. This operation is not supported.\");\r\n  try {\r\n    // Mock successful deletion\r\n    return {\r\n      success: true,\r\n      message: \"Image deleted successfully (mock response)\"\r\n    };\r\n  } catch (error) {\r\n    console.log(error);\r\n    return { success: false, message: \"Delete failed\" };\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE5C;AACA;;AAEA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvCC,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;EACxG,IAAI;IACF;IACA,MAAM,CAACC,SAAS,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDX,KAAK,CAACY,GAAG,CAAC,GAAGX,MAAM,gBAAgB,EAAE;MACnCY,OAAO,EAAE;QACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACG,KAAK,GAAG,EAAE;MAC7G;IACF,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC,EAC9BpB,KAAK,CAACY,GAAG,CAAC,GAAGX,MAAM,0BAA0B,EAAE;MAC7CY,OAAO,EAAE;QACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACG,KAAK,GAAG,EAAE;MAC7G;IACF,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC,CAC/B,CAAC;;IAEF;IACA,MAAMC,MAAM,GAAGb,SAAS,CAACY,IAAI,IAAI,EAAE;IACnC,MAAME,QAAQ,GAAGb,WAAW,CAACW,IAAI,IAAI,EAAE;IAEvC,OAAO;MACLG,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE;QACTC,WAAW,EAAEJ,MAAM,CAACK,MAAM;QAC1BC,aAAa,EAAEL,QAAQ,CAACI,MAAM;QAC9BE,UAAU,EAAE,EAAE;QAAE;QAChBC,eAAe,EAAE,CAAC;QAAE;QACpBC,YAAY,EAAET,MAAM,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;MACzE;IACF,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd7B,OAAO,CAAC8B,GAAG,CAACD,KAAK,CAAC;IAClB;IACA,OAAO;MACLZ,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE;QACTC,WAAW,EAAE,CAAC;QACdE,aAAa,EAAE,EAAE;QACjBC,UAAU,EAAE,EAAE;QACdC,eAAe,EAAE,CAAC;QAClBC,YAAY,EAAE;MAChB;IACF,CAAC;EACH;AACF,CAAC;AAACO,EAAA,GA7CWhC,aAAa;AA+C1B,OAAO,MAAMiC,eAAe,GAAG,MAAAA,CAAA,KAAY;EACzChC,OAAO,CAACC,IAAI,CAAC,2FAA2F,CAAC;EACzG,IAAI;IACF;IACA,OAAO;MACLgB,OAAO,EAAE,IAAI;MACbgB,YAAY,EAAE,CACZ;QACEC,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE,2BAA2B;QAClCC,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE;MACf,CAAC,EACD;QACEH,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE,2BAA2B;QAClCC,KAAK,EAAE,eAAe;QACtBC,WAAW,EAAE;MACf,CAAC;IAEL,CAAC;EACH,CAAC,CAAC,OAAOR,KAAK,EAAE;IACd7B,OAAO,CAAC8B,GAAG,CAACD,KAAK,CAAC;IAClB,OAAO;MAAEZ,OAAO,EAAE,KAAK;MAAEgB,YAAY,EAAE;IAAG,CAAC;EAC7C;AACF,CAAC;AAED,OAAO,MAAMK,eAAe,GAAG,MAAOC,QAAQ,IAAK;EACjDvC,OAAO,CAACC,IAAI,CAAC,8FAA8F,CAAC;EAC5G,IAAI;IACF;IACA,OAAO;MACLgB,OAAO,EAAE,IAAI;MACbuB,OAAO,EAAE,6CAA6C;MACtDL,KAAK,EAAE;QACLD,GAAG,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;QACfP,KAAK,EAAE,2BAA2B;QAClCC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE;MACf;IACF,CAAC;EACH,CAAC,CAAC,OAAOR,KAAK,EAAE;IACd7B,OAAO,CAAC8B,GAAG,CAACD,KAAK,CAAC;IAClB,OAAO;MAAEZ,OAAO,EAAE,KAAK;MAAEuB,OAAO,EAAE;IAAgB,CAAC;EACrD;AACF,CAAC;AAED,OAAO,MAAMG,eAAe,GAAG,MAAOC,EAAE,IAAK;EAC3C5C,OAAO,CAACC,IAAI,CAAC,8FAA8F,CAAC;EAC5G,IAAI;IACF;IACA,OAAO;MACLgB,OAAO,EAAE,IAAI;MACbuB,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,OAAOX,KAAK,EAAE;IACd7B,OAAO,CAAC8B,GAAG,CAACD,KAAK,CAAC;IAClB,OAAO;MAAEZ,OAAO,EAAE,KAAK;MAAEuB,OAAO,EAAE;IAAgB,CAAC;EACrD;AACF,CAAC;AAAC,IAAAT,EAAA;AAAAc,YAAA,CAAAd,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}