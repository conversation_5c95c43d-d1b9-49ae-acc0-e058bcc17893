{"ast": null, "code": "'use strict';\n\nvar createError = require('./createError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(createError('Request failed with status code ' + response.status, response.config, null, response.request, response));\n  }\n};", "map": {"version": 3, "names": ["createError", "require", "module", "exports", "settle", "resolve", "reject", "response", "validateStatus", "config", "status", "request"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/core/settle.js"], "sourcesContent": ["'use strict';\n\nvar createError = require('./createError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(createError(\n      'Request failed with status code ' + response.status,\n      response.config,\n      null,\n      response.request,\n      response\n    ));\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1D,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAAM,CAACD,cAAc;EACnD,IAAI,CAACD,QAAQ,CAACG,MAAM,IAAI,CAACF,cAAc,IAAIA,cAAc,CAACD,QAAQ,CAACG,MAAM,CAAC,EAAE;IAC1EL,OAAO,CAACE,QAAQ,CAAC;EACnB,CAAC,MAAM;IACLD,MAAM,CAACN,WAAW,CAChB,kCAAkC,GAAGO,QAAQ,CAACG,MAAM,EACpDH,QAAQ,CAACE,MAAM,EACf,IAAI,EACJF,QAAQ,CAACI,OAAO,EAChBJ,QACF,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}