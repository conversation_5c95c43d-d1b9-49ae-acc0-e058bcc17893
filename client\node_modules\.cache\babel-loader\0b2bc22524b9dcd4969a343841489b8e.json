{"ast": null, "code": "'use client';\n\nimport getThemeProps from './getThemeProps';\nimport useTheme from '../useTheme';\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  const mergedProps = getThemeProps({\n    theme,\n    name,\n    props\n  });\n  return mergedProps;\n}", "map": {"version": 3, "names": ["getThemeProps", "useTheme", "useThemeProps", "props", "name", "defaultTheme", "themeId", "theme", "mergedProps"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/system/esm/useThemeProps/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport getThemeProps from './getThemeProps';\nimport useTheme from '../useTheme';\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  const mergedProps = getThemeProps({\n    theme,\n    name,\n    props\n  });\n  return mergedProps;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,aAAa;AAClC,eAAe,SAASC,aAAaA,CAAC;EACpCC,KAAK;EACLC,IAAI;EACJC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,IAAIC,KAAK,GAAGN,QAAQ,CAACI,YAAY,CAAC;EAClC,IAAIC,OAAO,EAAE;IACXC,KAAK,GAAGA,KAAK,CAACD,OAAO,CAAC,IAAIC,KAAK;EACjC;EACA,MAAMC,WAAW,GAAGR,aAAa,CAAC;IAChCO,KAAK;IACLH,IAAI;IACJD;EACF,CAAC,CAAC;EACF,OAAOK,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}