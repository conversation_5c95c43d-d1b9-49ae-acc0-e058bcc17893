{"ast": null, "code": "'use strict';\n\n/** @type {import('./eval')} */\nmodule.exports = EvalError;", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/ITSS_Reference/client/node_modules/es-errors/eval.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./eval')} */\nmodule.exports = EvalError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}