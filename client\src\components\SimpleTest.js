import React, { useState } from "react";

const SimpleTest = () => {
  const [count, setCount] = useState(0);
  const [showOverlay, setShowOverlay] = useState(false);

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-6">🧪 Simple Interaction Test</h1>
      
      <div className="space-y-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">Basic Click Test</h2>
          <button 
            onClick={() => setCount(count + 1)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Click Count: {count}
          </button>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">Overlay Test</h2>
          <button 
            onClick={() => setShowOverlay(!showOverlay)}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Toggle Overlay: {showOverlay ? "ON" : "OFF"}
          </button>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">Navigation Test</h2>
          <div className="space-x-2">
            <button 
              onClick={() => window.location.href = "/"}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Go Home
            </button>
            <button 
              onClick={() => window.location.href = "/test-products"}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Go to Test Products
            </button>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-2">Console Test</h2>
          <button 
            onClick={() => {
              console.log("🔥 Console test button clicked!");
              alert("Alert test - if you see this, JavaScript is working!");
            }}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Console & Alert Test
          </button>
        </div>
      </div>

      {/* Test Overlay */}
      {showOverlay && (
        <>
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setShowOverlay(false)}
          />
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl">
              <h3 className="text-lg font-bold mb-4">Test Overlay</h3>
              <p className="mb-4">If you can see this and click the button below, overlays are working correctly.</p>
              <button 
                onClick={() => setShowOverlay(false)}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Close Overlay
              </button>
            </div>
          </div>
        </>
      )}

      <div className="mt-8 p-4 bg-yellow-100 border border-yellow-400 rounded">
        <h3 className="font-bold text-yellow-800">Troubleshooting:</h3>
        <ul className="text-yellow-700 text-sm mt-2">
          <li>• If buttons don't respond: Check browser console (F12) for errors</li>
          <li>• If overlay doesn't work: There might be CSS z-index conflicts</li>
          <li>• If nothing works: JavaScript might be disabled or there are critical errors</li>
        </ul>
      </div>
    </div>
  );
};

export default SimpleTest;
