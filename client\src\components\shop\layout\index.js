import React, { Fragment, createContext } from "react";
import { Nav<PERSON>, Footer, CartModal } from "../partials";
import LoginSignup from "../auth/LoginSignup";
import DebugInteraction from "../../DebugInteraction";
import QuickFix from "../../QuickFix";
import EmergencyFix from "../../EmergencyFix";

export const LayoutContext = createContext();

const Layout = ({ children }) => {
  return (
    <Fragment>
      {/* Emergency fix - chạy ngay để loại bỏ overlay */}
      <EmergencyFix />

      <div className="flex-grow">
        <Navber />
        <LoginSignup />
        <CartModal />
        {/* All Children pass from here */}
        {children}
      </div>
      <Footer />
      {/* Debug panels - remove in production */}
      <DebugInteraction />
      <QuickFix />
    </Fragment>
  );
};

export default Layout;
