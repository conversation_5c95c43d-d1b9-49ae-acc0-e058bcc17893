{"ast": null, "code": "import { getAllOrders } from \"./FetchApi\";\nexport const fetchData = async dispatch => {\n  dispatch({\n    type: \"loading\",\n    payload: true\n  });\n  let responseData = await getAllOrders();\n  setTimeout(() => {\n    if (responseData && Array.isArray(responseData)) {\n      dispatch({\n        type: \"fetchOrderAndChangeState\",\n        payload: responseData\n      });\n      dispatch({\n        type: \"loading\",\n        payload: false\n      });\n    }\n  }, 1000);\n};\n\n/* This method call the editmodal & dispatch category context */\nexport const editOrderReq = (orderId, type, status, dispatch) => {\n  if (type) {\n    console.log(\"click update\");\n    dispatch({\n      type: \"updateOrderModalOpen\",\n      orderId: orderId,\n      status: status\n    });\n  }\n};\n\n/* Filter All Order */\nexport const filterOrder = async (type, data, dispatch, dropdown, setDropdown) => {\n  let status = type === \"All\" ? undefined : type;\n  let responseData = await getAllOrders(status);\n  if (responseData && Array.isArray(responseData)) {\n    dispatch({\n      type: \"fetchOrderAndChangeState\",\n      payload: responseData\n    });\n    setDropdown(!dropdown);\n  }\n};", "map": {"version": 3, "names": ["getAllOrders", "fetchData", "dispatch", "type", "payload", "responseData", "setTimeout", "Array", "isArray", "editOrderReq", "orderId", "status", "console", "log", "filterOrder", "data", "dropdown", "setDropdown", "undefined"], "sources": ["D:/ITSS_Reference/client/src/components/admin/orders/Actions.js"], "sourcesContent": ["import { getAllOrders } from \"./FetchApi\";\r\n\r\nexport const fetchData = async (dispatch) => {\r\n  dispatch({ type: \"loading\", payload: true });\r\n  let responseData = await getAllOrders();\r\n  setTimeout(() => {\r\n    if (responseData && Array.isArray(responseData)) {\r\n      dispatch({\r\n        type: \"fetchOrderAndChangeState\",\r\n        payload: responseData,\r\n      });\r\n      dispatch({ type: \"loading\", payload: false });\r\n    }\r\n  }, 1000);\r\n};\r\n\r\n/* This method call the editmodal & dispatch category context */\r\nexport const editOrderReq = (orderId, type, status, dispatch) => {\r\n  if (type) {\r\n    console.log(\"click update\");\r\n    dispatch({ type: \"updateOrderModalOpen\", orderId: orderId, status: status });\r\n  }\r\n};\r\n\r\n/* Filter All Order */\r\nexport const filterOrder = async (\r\n  type,\r\n  data,\r\n  dispatch,\r\n  dropdown,\r\n  setDropdown\r\n) => {\r\n  let status = type === \"All\" ? undefined : type;\r\n  let responseData = await getAllOrders(status);\r\n  if (responseData && Array.isArray(responseData)) {\r\n    dispatch({\r\n      type: \"fetchOrderAndChangeState\",\r\n      payload: responseData,\r\n    });\r\n    setDropdown(!dropdown);\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,YAAY;AAEzC,OAAO,MAAMC,SAAS,GAAG,MAAOC,QAAQ,IAAK;EAC3CA,QAAQ,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,IAAIC,YAAY,GAAG,MAAML,YAAY,CAAC,CAAC;EACvCM,UAAU,CAAC,MAAM;IACf,IAAID,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;MAC/CH,QAAQ,CAAC;QACPC,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAEC;MACX,CAAC,CAAC;MACFH,QAAQ,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA,OAAO,MAAMK,YAAY,GAAGA,CAACC,OAAO,EAAEP,IAAI,EAAEQ,MAAM,EAAET,QAAQ,KAAK;EAC/D,IAAIC,IAAI,EAAE;IACRS,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BX,QAAQ,CAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEO,OAAO,EAAEA,OAAO;MAAEC,MAAM,EAAEA;IAAO,CAAC,CAAC;EAC9E;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,WAAW,GAAG,MAAAA,CACzBX,IAAI,EACJY,IAAI,EACJb,QAAQ,EACRc,QAAQ,EACRC,WAAW,KACR;EACH,IAAIN,MAAM,GAAGR,IAAI,KAAK,KAAK,GAAGe,SAAS,GAAGf,IAAI;EAC9C,IAAIE,YAAY,GAAG,MAAML,YAAY,CAACW,MAAM,CAAC;EAC7C,IAAIN,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;IAC/CH,QAAQ,CAAC;MACPC,IAAI,EAAE,0BAA0B;MAChCC,OAAO,EAAEC;IACX,CAAC,CAAC;IACFY,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}