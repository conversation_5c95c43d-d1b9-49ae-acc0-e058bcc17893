{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultProps\", \"mixins\", \"overrides\", \"palette\", \"props\", \"styleOverrides\"],\n  _excluded2 = [\"type\", \"mode\"];\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n      defaultProps = {},\n      mixins = {},\n      overrides = {},\n      palette = {},\n      props = {},\n      styleOverrides = {}\n    } = inputTheme,\n    other = _objectWithoutPropertiesLoose(inputTheme, _excluded);\n  const theme = _extends({}, other, {\n    components: {}\n  });\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = _extends({\n    gutters: (styles = {}) => {\n      return _extends({\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2)\n      }, styles, {\n        [breakpoints.up('sm')]: _extends({\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3)\n        }, styles[breakpoints.up('sm')])\n      });\n    }\n  }, mixins);\n  const {\n      type: typeInput,\n      mode: modeInput\n    } = palette,\n    paletteRest = _objectWithoutPropertiesLoose(palette, _excluded2);\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = _extends({\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode\n  }, paletteRest);\n  return theme;\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "createBreakpoints", "createSpacing", "adaptV4Theme", "inputTheme", "process", "env", "NODE_ENV", "console", "warn", "join", "defaultProps", "mixins", "overrides", "palette", "props", "styleOverrides", "other", "theme", "components", "Object", "keys", "for<PERSON>ach", "component", "componentValue", "spacing", "breakpoints", "gutters", "styles", "paddingLeft", "paddingRight", "up", "type", "typeInput", "mode", "modeInput", "paletteRest", "finalMode", "text", "hint"], "sources": ["D:/ITSS_Reference/client/node_modules/@mui/material/styles/adaptV4Theme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultProps\", \"mixins\", \"overrides\", \"palette\", \"props\", \"styleOverrides\"],\n  _excluded2 = [\"type\", \"mode\"];\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n      defaultProps = {},\n      mixins = {},\n      overrides = {},\n      palette = {},\n      props = {},\n      styleOverrides = {}\n    } = inputTheme,\n    other = _objectWithoutPropertiesLoose(inputTheme, _excluded);\n  const theme = _extends({}, other, {\n    components: {}\n  });\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = _extends({\n    gutters: (styles = {}) => {\n      return _extends({\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2)\n      }, styles, {\n        [breakpoints.up('sm')]: _extends({\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3)\n        }, styles[breakpoints.up('sm')])\n      });\n    }\n  }, mixins);\n  const {\n      type: typeInput,\n      mode: modeInput\n    } = palette,\n    paletteRest = _objectWithoutPropertiesLoose(palette, _excluded2);\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = _extends({\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode\n  }, paletteRest);\n  return theme;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC;EAC7FC,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AAC/B,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,aAAa;AAC9D,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAE;EAC/C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,IAAI,CAAC,CAAC,oCAAoC,EAAE,mEAAmE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACtI;EACA,MAAM;MACFC,YAAY,GAAG,CAAC,CAAC;MACjBC,MAAM,GAAG,CAAC,CAAC;MACXC,SAAS,GAAG,CAAC,CAAC;MACdC,OAAO,GAAG,CAAC,CAAC;MACZC,KAAK,GAAG,CAAC,CAAC;MACVC,cAAc,GAAG,CAAC;IACpB,CAAC,GAAGZ,UAAU;IACda,KAAK,GAAGnB,6BAA6B,CAACM,UAAU,EAAEL,SAAS,CAAC;EAC9D,MAAMmB,KAAK,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IAChCE,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;;EAEF;EACAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAAC,CAACW,OAAO,CAACC,SAAS,IAAI;IAC7C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGA,YAAY,CAACY,SAAS,CAAC;IACrDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAACC,SAAS,IAAI;IACtC,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGI,KAAK,CAACQ,SAAS,CAAC;IAC9CL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAJ,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,OAAO,CAACC,SAAS,IAAI;IAC/C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGA,cAAc,CAACO,SAAS,CAAC;IACzDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,OAAO,CAACC,SAAS,IAAI;IAC1C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGH,SAAS,CAACU,SAAS,CAAC;IACpDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAN,KAAK,CAACO,OAAO,GAAGvB,aAAa,CAACE,UAAU,CAACqB,OAAO,CAAC;;EAEjD;EACA,MAAMC,WAAW,GAAGzB,iBAAiB,CAACG,UAAU,CAACsB,WAAW,IAAI,CAAC,CAAC,CAAC;EACnE,MAAMD,OAAO,GAAGP,KAAK,CAACO,OAAO;EAC7BP,KAAK,CAACN,MAAM,GAAGf,QAAQ,CAAC;IACtB8B,OAAO,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;MACxB,OAAO/B,QAAQ,CAAC;QACdgC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC;QACvBK,YAAY,EAAEL,OAAO,CAAC,CAAC;MACzB,CAAC,EAAEG,MAAM,EAAE;QACT,CAACF,WAAW,CAACK,EAAE,CAAC,IAAI,CAAC,GAAGlC,QAAQ,CAAC;UAC/BgC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC;UACvBK,YAAY,EAAEL,OAAO,CAAC,CAAC;QACzB,CAAC,EAAEG,MAAM,CAACF,WAAW,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ;EACF,CAAC,EAAEnB,MAAM,CAAC;EACV,MAAM;MACFoB,IAAI,EAAEC,SAAS;MACfC,IAAI,EAAEC;IACR,CAAC,GAAGrB,OAAO;IACXsB,WAAW,GAAGtC,6BAA6B,CAACgB,OAAO,EAAEd,UAAU,CAAC;EAClE,MAAMqC,SAAS,GAAGF,SAAS,IAAIF,SAAS,IAAI,OAAO;EACnDf,KAAK,CAACJ,OAAO,GAAGjB,QAAQ,CAAC;IACvB;IACAyC,IAAI,EAAE;MACJC,IAAI,EAAEF,SAAS,KAAK,MAAM,GAAG,0BAA0B,GAAG;IAC5D,CAAC;IACDH,IAAI,EAAEG,SAAS;IACfL,IAAI,EAAEK;EACR,CAAC,EAAED,WAAW,CAAC;EACf,OAAOlB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}