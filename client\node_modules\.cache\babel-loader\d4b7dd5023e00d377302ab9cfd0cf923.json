{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\common\\\\ImageWithFallback.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageWithFallback = ({\n  src,\n  alt,\n  className = '',\n  fallbackSrc = 'https://via.placeholder.com/400x400/e5e7eb/6b7280?text=No+Image',\n  onClick,\n  ...props\n}) => {\n  _s();\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const getValidImageUrl = imageUrl => {\n    if (!imageUrl) {\n      return fallbackSrc;\n    }\n\n    // Check if it's a broken example.com URL\n    if (imageUrl.includes('example.com')) {\n      return fallbackSrc;\n    }\n    return imageUrl;\n  };\n  const handleError = e => {\n    if (!hasError) {\n      setHasError(true);\n      setIsLoading(false);\n      e.target.src = fallbackSrc;\n\n      // Suppress console errors by overriding console.error temporarily\n      const originalConsoleError = console.error;\n      console.error = () => {};\n      setTimeout(() => {\n        console.error = originalConsoleError;\n      }, 100);\n\n      // Prevent default error handling\n      e.preventDefault();\n      e.stopPropagation();\n    }\n  };\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: hasError ? fallbackSrc : getValidImageUrl(src),\n    alt: alt,\n    className: `${className} ${isLoading ? 'opacity-70' : 'opacity-100'} transition-opacity duration-200`,\n    onClick: onClick,\n    onError: handleError,\n    onLoad: handleLoad,\n    loading: \"lazy\",\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageWithFallback, \"Hc8FcLGVX5xpQ0WRw81BP8LX/Rs=\");\n_c = ImageWithFallback;\nexport default ImageWithFallback;\nvar _c;\n$RefreshReg$(_c, \"ImageWithFallback\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ImageWithFallback", "src", "alt", "className", "fallbackSrc", "onClick", "props", "_s", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "isLoading", "setIsLoading", "getValidImageUrl", "imageUrl", "includes", "handleError", "e", "target", "originalConsoleError", "console", "error", "setTimeout", "preventDefault", "stopPropagation", "handleLoad", "onError", "onLoad", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/common/ImageWithFallback.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst ImageWithFallback = ({ \n  src, \n  alt, \n  className = '', \n  fallbackSrc = 'https://via.placeholder.com/400x400/e5e7eb/6b7280?text=No+Image',\n  onClick,\n  ...props \n}) => {\n  const [hasError, setHasError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const getValidImageUrl = (imageUrl) => {\n    if (!imageUrl) {\n      return fallbackSrc;\n    }\n    \n    // Check if it's a broken example.com URL\n    if (imageUrl.includes('example.com')) {\n      return fallbackSrc;\n    }\n    \n    return imageUrl;\n  };\n\n  const handleError = (e) => {\n    if (!hasError) {\n      setHasError(true);\n      setIsLoading(false);\n      e.target.src = fallbackSrc;\n\n      // Suppress console errors by overriding console.error temporarily\n      const originalConsoleError = console.error;\n      console.error = () => {};\n      setTimeout(() => {\n        console.error = originalConsoleError;\n      }, 100);\n\n      // Prevent default error handling\n      e.preventDefault();\n      e.stopPropagation();\n    }\n  };\n\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n\n  return (\n    <img\n      src={hasError ? fallbackSrc : getValidImageUrl(src)}\n      alt={alt}\n      className={`${className} ${isLoading ? 'opacity-70' : 'opacity-100'} transition-opacity duration-200`}\n      onClick={onClick}\n      onError={handleError}\n      onLoad={handleLoad}\n      loading=\"lazy\"\n      {...props}\n    />\n  );\n};\n\nexport default ImageWithFallback;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,GAAG;EACHC,GAAG;EACHC,SAAS,GAAG,EAAE;EACdC,WAAW,GAAG,iEAAiE;EAC/EC,OAAO;EACP,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMe,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,IAAI,CAACA,QAAQ,EAAE;MACb,OAAOT,WAAW;IACpB;;IAEA;IACA,IAAIS,QAAQ,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpC,OAAOV,WAAW;IACpB;IAEA,OAAOS,QAAQ;EACjB,CAAC;EAED,MAAME,WAAW,GAAIC,CAAC,IAAK;IACzB,IAAI,CAACR,QAAQ,EAAE;MACbC,WAAW,CAAC,IAAI,CAAC;MACjBE,YAAY,CAAC,KAAK,CAAC;MACnBK,CAAC,CAACC,MAAM,CAAChB,GAAG,GAAGG,WAAW;;MAE1B;MACA,MAAMc,oBAAoB,GAAGC,OAAO,CAACC,KAAK;MAC1CD,OAAO,CAACC,KAAK,GAAG,MAAM,CAAC,CAAC;MACxBC,UAAU,CAAC,MAAM;QACfF,OAAO,CAACC,KAAK,GAAGF,oBAAoB;MACtC,CAAC,EAAE,GAAG,CAAC;;MAEP;MACAF,CAAC,CAACM,cAAc,CAAC,CAAC;MAClBN,CAAC,CAACO,eAAe,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBb,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEZ,OAAA;IACEE,GAAG,EAAEO,QAAQ,GAAGJ,WAAW,GAAGQ,gBAAgB,CAACX,GAAG,CAAE;IACpDC,GAAG,EAAEA,GAAI;IACTC,SAAS,EAAE,GAAGA,SAAS,IAAIO,SAAS,GAAG,YAAY,GAAG,aAAa,kCAAmC;IACtGL,OAAO,EAAEA,OAAQ;IACjBoB,OAAO,EAAEV,WAAY;IACrBW,MAAM,EAAEF,UAAW;IACnBG,OAAO,EAAC,MAAM;IAAA,GACVrB;EAAK;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACxB,EAAA,CA3DIP,iBAAiB;AAAAgC,EAAA,GAAjBhC,iBAAiB;AA6DvB,eAAeA,iBAAiB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}