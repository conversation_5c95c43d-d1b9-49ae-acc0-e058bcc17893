{"ast": null, "code": "export const dashboardUserState = {\n  userDetails: null,\n  loading: false,\n  OrderByUser: null\n};\nexport const dashboardUserReducer = (state, action) => {\n  switch (action.type) {\n    case \"userDetails\":\n      return {\n        ...state,\n        userDetails: action.payload\n      };\n    case \"OrderByUser\":\n      return {\n        ...state,\n        OrderByUser: action.payload\n      };\n    case \"loading\":\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["dashboardUserState", "userDetails", "loading", "OrderByUser", "dashboardUserReducer", "state", "action", "type", "payload"], "sources": ["D:/ITSS_Reference/client/src/components/shop/dashboardUser/DashboardUserContext.js"], "sourcesContent": ["export const dashboardUserState = {\r\n  userDetails: null,\r\n  loading: false,\r\n  OrderByUser: null,\r\n};\r\n\r\nexport const dashboardUserReducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"userDetails\":\r\n      return {\r\n        ...state,\r\n        userDetails: action.payload,\r\n      };\r\n    case \"OrderByUser\":\r\n      return {\r\n        ...state,\r\n        OrderByUser: action.payload,\r\n      };\r\n    case \"loading\":\r\n      return {\r\n        ...state,\r\n        loading: action.payload,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG;EAChCC,WAAW,EAAE,IAAI;EACjBC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;AACf,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QACL,GAAGF,KAAK;QACRJ,WAAW,EAAEK,MAAM,CAACE;MACtB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRF,WAAW,EAAEG,MAAM,CAACE;MACtB,CAAC;IACH,KAAK,SAAS;MACZ,OAAO;QACL,GAAGH,KAAK;QACRH,OAAO,EAAEI,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}