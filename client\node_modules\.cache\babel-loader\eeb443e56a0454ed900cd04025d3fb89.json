{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\shop\\\\wishlist\\\\SingleWishProduct.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useEffect, useState } from \"react\";\nimport { useHistory } from \"react-router-dom\";\nimport { wishLists } from \"./FetchApi\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst apiURL = process.env.REACT_APP_API_URL;\nconst Product = () => {\n  _s();\n  const history = useHistory();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    setLoading(true);\n    let responseData = await wishListProducts();\n    setTimeout(() => {\n      if (responseData && responseData.Products) {\n        setProducts(responseData.Products);\n        setLoading(false);\n      }\n    }, 50);\n  };\n  const removeFromWishList = id => {\n    let list = localStorage.getItem(\"wishList\") ? JSON.parse(localStorage.getItem(\"wishList\")) : [];\n    if (list.length > 0) {\n      if (list.includes(id) === true) {\n        list.splice(list.indexOf(id), 1);\n        localStorage.setItem(\"wishList\", JSON.stringify(list));\n        fetchData();\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-32 text-2xl text-center\",\n      children: \"No product found in wishList\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-1\",\n      children: products.length > 0 ? products.map((product, index) => {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative m-2 md:py-6 md:border-t md:border-b md:my-2 md:mx-0 col-span-1 md:flex md:items-center md:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 md:flex md:items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              onClick: e => history.push(`/products/${product._id}`),\n              className: \"cursor-pointer md:h-20 md:w-20 object-cover object-center\",\n              src: `${apiURL}/uploads/products/${product.pImages[0]}`,\n              alt: \"wishListproduct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg md:ml-6 truncate\",\n              children: product.pName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 md:flex md:items-center md:justify-around\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-semibold text-gray-600\",\n              children: [\"$\", product.pPrice, \".00\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 19\n            }, this), product.pQuantity > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-500 my-1 md:my-0\",\n              children: \"In Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-red-500 my-1 md:my-0\",\n              children: \"Out Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: \"#303031\"\n              },\n              onClick: e => history.push(`/products/${product._id}`),\n              className: \"inline-block px-4 py-2 text-white text-xs md:text-base text-center cursor-pointer hover:opacity-75\",\n              children: \"View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 mx-2 my-2 md:relative\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              onClick: e => removeFromWishList(product._id),\n              className: \"w-6 h-6 cursor-pointer\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No product found in wishList\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(Product, \"vh0NvASZj5ZCfxbCA1om7Mzfk9E=\", false, function () {\n  return [useHistory];\n});\n_c = Product;\nconst SingleWishProduct = props => {\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"mx-4 mt-20 md:mx-12 md:mt-32 lg:mt-24\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl mx-2 mb-6\",\n        children: \"Wishlist\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Product, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SingleWishProduct;\nexport default SingleWishProduct;\nvar _c, _c2;\n$RefreshReg$(_c, \"Product\");\n$RefreshReg$(_c2, \"SingleWishProduct\");", "map": {"version": 3, "names": ["React", "Fragment", "useEffect", "useState", "useHistory", "wishLists", "jsxDEV", "_jsxDEV", "apiURL", "process", "env", "REACT_APP_API_URL", "Product", "_s", "history", "products", "setProducts", "loading", "setLoading", "fetchData", "responseData", "wishListProducts", "setTimeout", "Products", "removeFromWishList", "id", "list", "localStorage", "getItem", "JSON", "parse", "length", "includes", "splice", "indexOf", "setItem", "stringify", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "product", "index", "onClick", "e", "push", "_id", "src", "pImages", "alt", "pName", "pPrice", "pQuantity", "style", "background", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "_c", "SingleWishProduct", "props", "_c2", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/shop/wishlist/SingleWishProduct.js"], "sourcesContent": ["import React, { Fragment, useEffect, useState } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { wishLists } from \"./FetchApi\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nconst Product = () => {\r\n  const history = useHistory();\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    setLoading(true);\r\n    let responseData = await wishListProducts();\r\n    setTimeout(() => {\r\n      if (responseData && responseData.Products) {\r\n        setProducts(responseData.Products);\r\n        setLoading(false);\r\n      }\r\n    }, 50);\r\n  };\r\n\r\n  const removeFromWishList = (id) => {\r\n    let list = localStorage.getItem(\"wishList\")\r\n      ? JSON.parse(localStorage.getItem(\"wishList\"))\r\n      : [];\r\n    if (list.length > 0) {\r\n      if (list.includes(id) === true) {\r\n        list.splice(list.indexOf(id), 1);\r\n        localStorage.setItem(\"wishList\", JSON.stringify(list));\r\n        fetchData();\r\n      }\r\n    }\r\n  };\r\n  if (loading) {\r\n    return (\r\n      <div className=\"my-32 text-2xl text-center\">\r\n        No product found in wishList\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <Fragment>\r\n      <div className=\"grid grid-cols-2 md:grid-cols-1\">\r\n        {products.length > 0 ? (\r\n          products.map((product, index) => {\r\n            return (\r\n              <div\r\n                key={index}\r\n                className=\"relative m-2 md:py-6 md:border-t md:border-b md:my-2 md:mx-0 col-span-1 md:flex md:items-center md:justify-between\"\r\n              >\r\n                <div className=\"md:w-1/2 md:flex md:items-center\">\r\n                  <img\r\n                    onClick={(e) => history.push(`/products/${product._id}`)}\r\n                    className=\"cursor-pointer md:h-20 md:w-20 object-cover object-center\"\r\n                    src={`${apiURL}/uploads/products/${product.pImages[0]}`}\r\n                    alt=\"wishListproduct\"\r\n                  />\r\n                  <div className=\"text-lg md:ml-6 truncate\">\r\n                    {product.pName}\r\n                  </div>\r\n                </div>\r\n                <div className=\"md:w-1/2 md:flex md:items-center md:justify-around\">\r\n                  <div className=\"font-semibold text-gray-600\">\r\n                    ${product.pPrice}.00\r\n                  </div>\r\n                  {product.pQuantity > 0 ? (\r\n                    <div className=\"text-green-500 my-1 md:my-0\">In Stock</div>\r\n                  ) : (\r\n                    <div className=\"text-red-500 my-1 md:my-0\">Out Stock</div>\r\n                  )}\r\n\r\n                  <div\r\n                    style={{ background: \"#303031\" }}\r\n                    onClick={(e) => history.push(`/products/${product._id}`)}\r\n                    className=\"inline-block px-4 py-2 text-white text-xs md:text-base text-center cursor-pointer hover:opacity-75\"\r\n                  >\r\n                    View\r\n                  </div>\r\n                </div>\r\n                <div className=\"absolute top-0 right-0 mx-2 my-2 md:relative\">\r\n                  <svg\r\n                    onClick={(e) => removeFromWishList(product._id)}\r\n                    className=\"w-6 h-6 cursor-pointer\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <div>No product found in wishList</div>\r\n        )}\r\n      </div>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nconst SingleWishProduct = (props) => {\r\n  return (\r\n    <Fragment>\r\n      <section className=\"mx-4 mt-20 md:mx-12 md:mt-32 lg:mt-24\">\r\n        <div className=\"text-2xl mx-2 mb-6\">Wishlist</div>\r\n        {/* Product List */}\r\n        <Product />\r\n      </section>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default SingleWishProduct;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACvC,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,OAAO,GAAGV,UAAU,CAAC,CAAC;EAC5B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACdiB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIE,YAAY,GAAG,MAAMC,gBAAgB,CAAC,CAAC;IAC3CC,UAAU,CAAC,MAAM;MACf,IAAIF,YAAY,IAAIA,YAAY,CAACG,QAAQ,EAAE;QACzCP,WAAW,CAACI,YAAY,CAACG,QAAQ,CAAC;QAClCL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EAED,MAAMM,kBAAkB,GAAIC,EAAE,IAAK;IACjC,IAAIC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GACvCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,EAAE;IACN,IAAIF,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;MACnB,IAAIL,IAAI,CAACM,QAAQ,CAACP,EAAE,CAAC,KAAK,IAAI,EAAE;QAC9BC,IAAI,CAACO,MAAM,CAACP,IAAI,CAACQ,OAAO,CAACT,EAAE,CAAC,EAAE,CAAC,CAAC;QAChCE,YAAY,CAACQ,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACV,IAAI,CAAC,CAAC;QACtDP,SAAS,CAAC,CAAC;MACb;IACF;EACF,CAAC;EACD,IAAIF,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK8B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAE5C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EACA,oBACEnC,OAAA,CAACN,QAAQ;IAAAqC,QAAA,eACP/B,OAAA;MAAK8B,SAAS,EAAC,iCAAiC;MAAAC,QAAA,EAC7CvB,QAAQ,CAACgB,MAAM,GAAG,CAAC,GAClBhB,QAAQ,CAAC4B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;QAC/B,oBACEtC,OAAA;UAEE8B,SAAS,EAAC,oHAAoH;UAAAC,QAAA,gBAE9H/B,OAAA;YAAK8B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C/B,OAAA;cACEuC,OAAO,EAAGC,CAAC,IAAKjC,OAAO,CAACkC,IAAI,CAAC,aAAaJ,OAAO,CAACK,GAAG,EAAE,CAAE;cACzDZ,SAAS,EAAC,2DAA2D;cACrEa,GAAG,EAAE,GAAG1C,MAAM,qBAAqBoC,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC,EAAG;cACxDC,GAAG,EAAC;YAAiB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACFnC,OAAA;cAAK8B,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCM,OAAO,CAACS;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjE/B,OAAA;cAAK8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAC,GAC1C,EAACM,OAAO,CAACU,MAAM,EAAC,KACnB;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLE,OAAO,CAACW,SAAS,GAAG,CAAC,gBACpBhD,OAAA;cAAK8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAE3DnC,OAAA;cAAK8B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC1D,eAEDnC,OAAA;cACEiD,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAAU,CAAE;cACjCX,OAAO,EAAGC,CAAC,IAAKjC,OAAO,CAACkC,IAAI,CAAC,aAAaJ,OAAO,CAACK,GAAG,EAAE,CAAE;cACzDZ,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAC/G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,eAC3D/B,OAAA;cACEuC,OAAO,EAAGC,CAAC,IAAKvB,kBAAkB,CAACoB,OAAO,CAACK,GAAG,CAAE;cAChDZ,SAAS,EAAC,wBAAwB;cAClCqB,IAAI,EAAC,cAAc;cACnBC,OAAO,EAAC,WAAW;cACnBC,KAAK,EAAC,4BAA4B;cAAAtB,QAAA,eAElC/B,OAAA;gBACEsD,QAAQ,EAAC,SAAS;gBAClBC,CAAC,EAAC,oMAAoM;gBACtMC,QAAQ,EAAC;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA9CDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+CP,CAAC;MAEV,CAAC,CAAC,gBAEFnC,OAAA;QAAA+B,QAAA,EAAK;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACvC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC7B,EAAA,CAtGID,OAAO;EAAA,QACKR,UAAU;AAAA;AAAA4D,EAAA,GADtBpD,OAAO;AAwGb,MAAMqD,iBAAiB,GAAIC,KAAK,IAAK;EACnC,oBACE3D,OAAA,CAACN,QAAQ;IAAAqC,QAAA,eACP/B,OAAA;MAAS8B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACxD/B,OAAA;QAAK8B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAElDnC,OAAA,CAACK,OAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAACyB,GAAA,GAVIF,iBAAiB;AAYvB,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}