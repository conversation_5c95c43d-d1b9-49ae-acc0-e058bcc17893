# Hướng dẫn Setup và Chạy Ứng dụng E-commerce

## 🚀 Tổng quan
Ứng dụng này bao gồm:
- **Backend**: Spring Boot (Java) với PostgreSQL
- **Frontend**: React.js
- **Database**: PostgreSQL với dữ liệu mẫu có sẵn

## 📋 Yêu cầu hệ thống
- Java 17+
- Node.js 16+
- PostgreSQL 12+
- Maven 3.6+

## 🗄️ Setup Database

### 1. Cài đặt PostgreSQL
- Tải và cài đặt PostgreSQL từ https://www.postgresql.org/download/
- Tạo database tên `marketly`
- Username: `postgres`, Password: `0000` (hoặc thay đổi trong `application.properties`)

### 2. Tạo database
```sql
CREATE DATABASE marketly;
```

### 3. Chạy script tạo bảng và dữ liệu mẫu
Database sẽ được tự động tạo khi chạy backend lần đầu (ddl-auto=update).
Dữ liệu mẫu có trong file `backend/src/main/resources/db/data-init.sql` bao gồm:
- 18 sản phẩm thuộc 9 danh mục
- User accounts (customer và manager)
- Sample orders và reviews

## 🔧 Setup Backend

### 1. Di chuyển vào thư mục backend
```bash
cd backend
```

### 2. Cài đặt dependencies
```bash
./mvnw clean install
```

### 3. Chạy backend
```bash
./mvnw spring-boot:run
```

Backend sẽ chạy tại: http://localhost:8080

### 4. Kiểm tra API
- Products: http://localhost:8080/api/v1/products/customer
- Health check: http://localhost:8080/actuator/health (nếu có)

## 🎨 Setup Frontend

### 1. Di chuyển vào thư mục client
```bash
cd client
```

### 2. Cài đặt dependencies
```bash
npm install
```

### 3. Chạy frontend
```bash
npm start
```

Frontend sẽ chạy tại: http://localhost:3000

## 🧪 Test và Kiểm tra

### 1. Truy cập trang test
Mở browser và vào: http://localhost:3000/test-products

### 2. Kiểm tra console
Mở Developer Tools (F12) và xem Console để theo dõi:
- Kết nối backend
- Dữ liệu sản phẩm được load
- Cấu trúc dữ liệu

### 3. Trang chủ
Vào http://localhost:3000 để xem trang chủ với sản phẩm

## 📊 Dữ liệu mẫu có sẵn

### Sản phẩm (18 items):
- Electronics: Smartphone X, Wireless Earbuds
- Fashion: Denim Jacket, Sneakers  
- Beauty: Moisturizer, Lipstick
- Furniture: Coffee Table, Office Chair
- Beverages: Cola Drink, Orange Juice
- Food: Chocolate Bar, Pasta Pack
- Household: Detergent, Dish Soap
- Toys: Action Figure, Puzzle Set
- Media: Novel Book, Music CD

### User accounts:
- Customer: <EMAIL> (password: password123)
- Manager: <EMAIL> (password: password123)

## 🔍 Troubleshooting

### Backend không chạy được:
1. Kiểm tra PostgreSQL đã chạy chưa
2. Kiểm tra database `marketly` đã tạo chưa
3. Kiểm tra port 8080 có bị chiếm không

### Frontend không load được sản phẩm:
1. Kiểm tra backend đã chạy tại port 8080
2. Kiểm tra file `.env` có đúng `REACT_APP_API_URL=http://localhost:8080`
3. Mở http://localhost:3000/test-products để debug

### Database connection error:
1. Kiểm tra PostgreSQL service đã start
2. Kiểm tra username/password trong `application.properties`
3. Kiểm tra database `marketly` đã tồn tại

## 📝 API Endpoints chính

- `GET /api/v1/products/customer` - Lấy tất cả sản phẩm
- `GET /api/v1/products/customer/{id}` - Chi tiết sản phẩm
- `GET /api/v1/products/customer/search` - Tìm kiếm sản phẩm
- `GET /api/v1/cart/{userId}` - Xem giỏ hàng
- `POST /api/v1/cart/{userId}/add` - Thêm vào giỏ hàng

## 🎯 Các tính năng đã hoạt động

✅ Hiển thị danh sách sản phẩm trên trang chủ
✅ API backend với dữ liệu mẫu
✅ Responsive design
✅ Search và filter sản phẩm
✅ Wishlist functionality
✅ Cart management
✅ Order management
✅ Admin panel

Chúc bạn setup thành công! 🎉
