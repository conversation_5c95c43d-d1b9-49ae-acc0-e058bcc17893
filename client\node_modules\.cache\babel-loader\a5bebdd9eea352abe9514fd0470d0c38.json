{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\SimpleTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleTest = () => {\n  _s();\n  const [count, setCount] = useState(0);\n  const [showOverlay, setShowOverlay] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-8 bg-gray-100 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-6\",\n      children: \"\\uD83E\\uDDEA Simple Interaction Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Basic Click Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCount(count + 1),\n          className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n          children: [\"Click Count: \", count]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Overlay Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowOverlay(!showOverlay),\n          className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\",\n          children: [\"Toggle Overlay: \", showOverlay ? \"ON\" : \"OFF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Navigation Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = \"/\",\n            className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n            children: \"Go Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = \"/test-products\",\n            className: \"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600\",\n            children: \"Go to Test Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Console Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log(\"🔥 Console test button clicked!\");\n            alert(\"Alert test - if you see this, JavaScript is working!\");\n          },\n          className: \"px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600\",\n          children: \"Console & Alert Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), showOverlay && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n        onClick: () => setShowOverlay(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold mb-4\",\n            children: \"Test Overlay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"If you can see this and click the button below, overlays are working correctly.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowOverlay(false),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n            children: \"Close Overlay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 p-4 bg-yellow-100 border border-yellow-400 rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-bold text-yellow-800\",\n        children: \"Troubleshooting:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"text-yellow-700 text-sm mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 If buttons don't respond: Check browser console (F12) for errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 If overlay doesn't work: There might be CSS z-index conflicts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u2022 If nothing works: JavaScript might be disabled or there are critical errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleTest, \"bAXeYuFfV5ScjneKpAf06QA6aOc=\");\n_c = SimpleTest;\nexport default SimpleTest;\nvar _c;\n$RefreshReg$(_c, \"SimpleTest\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleTest", "_s", "count", "setCount", "showOverlay", "setShowOverlay", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "href", "console", "log", "alert", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/SimpleTest.js"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst SimpleTest = () => {\n  const [count, setCount] = useState(0);\n  const [showOverlay, setShowOverlay] = useState(false);\n\n  return (\n    <div className=\"p-8 bg-gray-100 min-h-screen\">\n      <h1 className=\"text-3xl font-bold mb-6\">🧪 Simple Interaction Test</h1>\n      \n      <div className=\"space-y-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow\">\n          <h2 className=\"text-xl font-semibold mb-2\">Basic Click Test</h2>\n          <button \n            onClick={() => setCount(count + 1)}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          >\n            Click Count: {count}\n          </button>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow\">\n          <h2 className=\"text-xl font-semibold mb-2\">Overlay Test</h2>\n          <button \n            onClick={() => setShowOverlay(!showOverlay)}\n            className=\"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\"\n          >\n            Toggle Overlay: {showOverlay ? \"ON\" : \"OFF\"}\n          </button>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow\">\n          <h2 className=\"text-xl font-semibold mb-2\">Navigation Test</h2>\n          <div className=\"space-x-2\">\n            <button \n              onClick={() => window.location.href = \"/\"}\n              className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n            >\n              Go Home\n            </button>\n            <button \n              onClick={() => window.location.href = \"/test-products\"}\n              className=\"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600\"\n            >\n              Go to Test Products\n            </button>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow\">\n          <h2 className=\"text-xl font-semibold mb-2\">Console Test</h2>\n          <button \n            onClick={() => {\n              console.log(\"🔥 Console test button clicked!\");\n              alert(\"Alert test - if you see this, JavaScript is working!\");\n            }}\n            className=\"px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600\"\n          >\n            Console & Alert Test\n          </button>\n        </div>\n      </div>\n\n      {/* Test Overlay */}\n      {showOverlay && (\n        <>\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n            onClick={() => setShowOverlay(false)}\n          />\n          <div className=\"fixed inset-0 flex items-center justify-center z-50\">\n            <div className=\"bg-white p-6 rounded-lg shadow-xl\">\n              <h3 className=\"text-lg font-bold mb-4\">Test Overlay</h3>\n              <p className=\"mb-4\">If you can see this and click the button below, overlays are working correctly.</p>\n              <button \n                onClick={() => setShowOverlay(false)}\n                className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n              >\n                Close Overlay\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n\n      <div className=\"mt-8 p-4 bg-yellow-100 border border-yellow-400 rounded\">\n        <h3 className=\"font-bold text-yellow-800\">Troubleshooting:</h3>\n        <ul className=\"text-yellow-700 text-sm mt-2\">\n          <li>• If buttons don't respond: Check browser console (F12) for errors</li>\n          <li>• If overlay doesn't work: There might be CSS z-index conflicts</li>\n          <li>• If nothing works: JavaScript might be disabled or there are critical errors</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEE,OAAA;IAAKS,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3CV,OAAA;MAAIS,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvEd,OAAA;MAAKS,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBV,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CV,OAAA;UAAIS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAE;UACnCI,SAAS,EAAC,4DAA4D;UAAAC,QAAA,GACvE,eACc,EAACL,KAAK;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENd,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CV,OAAA;UAAIS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5Dd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CE,SAAS,EAAC,0DAA0D;UAAAC,QAAA,GACrE,kBACiB,EAACH,WAAW,GAAG,IAAI,GAAG,KAAK;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENd,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CV,OAAA;UAAIS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/Dd,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBV,OAAA;YACEe,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAI;YAC1CT,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EACzE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTd,OAAA;YACEe,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAiB;YACvDT,SAAS,EAAC,gEAAgE;YAAAC,QAAA,EAC3E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENd,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CV,OAAA;UAAIS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5Dd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAM;YACbI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CC,KAAK,CAAC,sDAAsD,CAAC;UAC/D,CAAE;UACFZ,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAC3E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLP,WAAW,iBACVP,OAAA,CAAAE,SAAA;MAAAQ,QAAA,gBACEV,OAAA;QACES,SAAS,EAAC,2CAA2C;QACrDM,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,KAAK;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFd,OAAA;QAAKS,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEV,OAAA;UAAKS,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDV,OAAA;YAAIS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDd,OAAA;YAAGS,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvGd,OAAA;YACEe,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,KAAK,CAAE;YACrCC,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACvE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH,eAEDd,OAAA;MAAKS,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBACtEV,OAAA;QAAIS,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/Dd,OAAA;QAAIS,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC1CV,OAAA;UAAAU,QAAA,EAAI;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3Ed,OAAA;UAAAU,QAAA,EAAI;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEd,OAAA;UAAAU,QAAA,EAAI;QAA6E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA7FID,UAAU;AAAAmB,EAAA,GAAVnB,UAAU;AA+FhB,eAAeA,UAAU;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}