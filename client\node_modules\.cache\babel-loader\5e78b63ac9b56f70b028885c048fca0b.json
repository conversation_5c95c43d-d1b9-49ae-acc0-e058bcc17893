{"ast": null, "code": "import axios from \"axios\";\nconst apiURL = process.env.REACT_APP_API_URL;\nexport const getSingleProduct = async pId => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/single-product`, {\n      pId: pId\n    });\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const postAddReview = async formData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/add-review`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};\nexport const postDeleteReview = async formData => {\n  try {\n    let res = await axios.post(`${apiURL}/api/product/delete-review`, formData);\n    return res.data;\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "apiURL", "process", "env", "REACT_APP_API_URL", "getSingleProduct", "pId", "res", "post", "data", "error", "console", "log", "postAddReview", "formData", "postDeleteReview"], "sources": ["D:/ITSS_Reference/client/src/components/shop/productDetails/FetchApi.js"], "sourcesContent": ["import axios from \"axios\";\r\nconst apiURL = process.env.REACT_APP_API_URL;\r\n\r\nexport const getSingleProduct = async (pId) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/single-product`, {\r\n      pId: pId,\r\n    });\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const postAddReview = async (formData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/add-review`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n\r\nexport const postDeleteReview = async (formData) => {\r\n  try {\r\n    let res = await axios.post(`${apiURL}/api/product/delete-review`, formData);\r\n    return res.data;\r\n  } catch (error) {\r\n    console.log(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;AAE5C,OAAO,MAAMC,gBAAgB,GAAG,MAAOC,GAAG,IAAK;EAC7C,IAAI;IACF,IAAIC,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,6BAA6B,EAAE;MACjEK,GAAG,EAAEA;IACP,CAAC,CAAC;IACF,OAAOC,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAOC,QAAQ,IAAK;EAC/C,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,yBAAyB,EAAEa,QAAQ,CAAC;IACxE,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,gBAAgB,GAAG,MAAOD,QAAQ,IAAK;EAClD,IAAI;IACF,IAAIP,GAAG,GAAG,MAAMP,KAAK,CAACQ,IAAI,CAAC,GAAGP,MAAM,4BAA4B,EAAEa,QAAQ,CAAC;IAC3E,OAAOP,GAAG,CAACE,IAAI;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}