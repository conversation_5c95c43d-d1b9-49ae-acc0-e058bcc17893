import React, { useState, useEffect } from 'react';
import { getAllProduct } from './admin/products/FetchApi';

const DebugProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    console.log('🚀 DebugProducts: Starting fetch...');
    setLoading(true);
    setError(null);
    
    try {
      console.log('📡 DebugProducts: Calling getAllProduct...');
      const data = await getAllProduct();
      console.log('✅ DebugProducts: Got data:', data);
      setProducts(data || []);
    } catch (err) {
      console.error('❌ DebugProducts: Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">🔧 Debug Products Component</h1>
      
      <div className="mb-4">
        <button 
          onClick={fetchProducts}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Refresh Products'}
        </button>
      </div>

      <div className="mb-4 p-4 bg-gray-100 rounded">
        <h2 className="font-semibold">Debug Info:</h2>
        <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL}</p>
        <p><strong>Products Count:</strong> {products.length}</p>
        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {error || 'None'}</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h3 className="font-semibold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {loading && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          Loading products...
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product, index) => (
          <div key={product.productId || index} className="border rounded-lg p-4 shadow">
            <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
            <p className="text-gray-600 mb-2">{product.category}</p>
            <p className="text-green-600 font-semibold mb-2">
              {product.price?.toLocaleString('vi-VN')} VND
            </p>
            <p className="text-sm text-gray-500">{product.description}</p>
            {product.images && product.images.length > 0 && (
              <img 
                src={product.images[0]} 
                alt={product.name}
                className="w-full h-32 object-cover mt-2 rounded"
                onError={(e) => {
                  e.target.src = '/placeholder-image.jpg';
                }}
              />
            )}
          </div>
        ))}
      </div>

      {products.length === 0 && !loading && !error && (
        <div className="text-center text-gray-500 py-8">
          No products found. Check console for debug logs.
        </div>
      )}
    </div>
  );
};

export default DebugProducts;
