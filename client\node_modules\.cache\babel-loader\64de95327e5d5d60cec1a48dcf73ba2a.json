{"ast": null, "code": "var _jsxFileName = \"D:\\\\ITSS_Reference\\\\client\\\\src\\\\components\\\\debug\\\\DebugProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getAllProducts } from '../../api/productApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DebugProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const response = await getAllProducts();\n        console.log('Raw API Response:', response);\n        setProducts(response.data || response);\n        setLoading(false);\n      } catch (err) {\n        console.error('Error fetching products:', err);\n        setError(err.message);\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 text-red-500\",\n    children: [\"Error: \", error]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-2xl font-bold mb-4\",\n      children: \"Debug Products Data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold\",\n        children: [\"Total Products: \", products.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: products.slice(0, 3).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border p-4 rounded\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-bold\",\n          children: product.name || product.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Product ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 16\n          }, this), \" \", product.productId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Price:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 16\n          }, this), \" \", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Images:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), product.images && product.images.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: product.images.map((img, imgIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"URL: \", img]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img,\n                alt: \"Product\",\n                className: \"w-32 h-32 object-cover border mt-1\",\n                onError: e => {\n                  console.log('Image failed to load:', img);\n                  e.target.style.border = '2px solid red';\n                },\n                onLoad: () => {\n                  console.log('Image loaded successfully:', img);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 23\n              }, this)]\n            }, imgIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-500 ml-4\",\n            children: \"No images found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            className: \"cursor-pointer font-semibold\",\n            children: \"Raw JSON\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"bg-gray-100 p-2 mt-2 text-xs overflow-auto\",\n            children: JSON.stringify(product, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugProducts, \"3+N/VFIgZOBgubN9oS5aTzm2qqY=\");\n_c = DebugProducts;\nexport default DebugProducts;\nvar _c;\n$RefreshReg$(_c, \"DebugProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getAllProducts", "jsxDEV", "_jsxDEV", "DebugProducts", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "fetchProducts", "response", "console", "log", "data", "err", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "slice", "map", "product", "index", "name", "productName", "productId", "price", "images", "img", "imgIndex", "src", "alt", "onError", "e", "target", "style", "border", "onLoad", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["D:/ITSS_Reference/client/src/components/debug/DebugProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getAllProducts } from '../../api/productApi';\n\nconst DebugProducts = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const response = await getAllProducts();\n        console.log('Raw API Response:', response);\n        setProducts(response.data || response);\n        setLoading(false);\n      } catch (err) {\n        console.error('Error fetching products:', err);\n        setError(err.message);\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  if (loading) return <div className=\"p-4\">Loading...</div>;\n  if (error) return <div className=\"p-4 text-red-500\">Error: {error}</div>;\n\n  return (\n    <div className=\"p-4\">\n      <h1 className=\"text-2xl font-bold mb-4\">Debug Products Data</h1>\n      \n      <div className=\"mb-4\">\n        <h2 className=\"text-lg font-semibold\">Total Products: {products.length}</h2>\n      </div>\n\n      <div className=\"space-y-4\">\n        {products.slice(0, 3).map((product, index) => (\n          <div key={index} className=\"border p-4 rounded\">\n            <h3 className=\"font-bold\">{product.name || product.productName}</h3>\n            <p><strong>Product ID:</strong> {product.productId}</p>\n            <p><strong>Price:</strong> {product.price}</p>\n            \n            <div className=\"mt-2\">\n              <strong>Images:</strong>\n              {product.images && product.images.length > 0 ? (\n                <div className=\"ml-4\">\n                  {product.images.map((img, imgIndex) => (\n                    <div key={imgIndex} className=\"mt-2\">\n                      <p>URL: {img}</p>\n                      <img \n                        src={img} \n                        alt=\"Product\" \n                        className=\"w-32 h-32 object-cover border mt-1\"\n                        onError={(e) => {\n                          console.log('Image failed to load:', img);\n                          e.target.style.border = '2px solid red';\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', img);\n                        }}\n                      />\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <span className=\"text-red-500 ml-4\">No images found</span>\n              )}\n            </div>\n\n            <details className=\"mt-2\">\n              <summary className=\"cursor-pointer font-semibold\">Raw JSON</summary>\n              <pre className=\"bg-gray-100 p-2 mt-2 text-xs overflow-auto\">\n                {JSON.stringify(product, null, 2)}\n              </pre>\n            </details>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default DebugProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAMY,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMZ,cAAc,CAAC,CAAC;QACvCa,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,QAAQ,CAAC;QAC1CN,WAAW,CAACM,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAAC;QACtCJ,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZH,OAAO,CAACJ,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;QAC9CN,QAAQ,CAACM,GAAG,CAACC,OAAO,CAAC;QACrBT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE,oBAAOL,OAAA;IAAKgB,SAAS,EAAC,KAAK;IAAAC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzD,IAAId,KAAK,EAAE,oBAAOP,OAAA;IAAKgB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,GAAC,SAAO,EAACV,KAAK;EAAA;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAExE,oBACErB,OAAA;IAAKgB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBjB,OAAA;MAAIgB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEhErB,OAAA;MAAKgB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBjB,OAAA;QAAIgB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,kBAAgB,EAACd,QAAQ,CAACmB,MAAM;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAENrB,OAAA;MAAKgB,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBd,QAAQ,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvC1B,OAAA;QAAiBgB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC7CjB,OAAA;UAAIgB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEQ,OAAO,CAACE,IAAI,IAAIF,OAAO,CAACG;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpErB,OAAA;UAAAiB,QAAA,gBAAGjB,OAAA;YAAAiB,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACI,OAAO,CAACI,SAAS;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDrB,OAAA;UAAAiB,QAAA,gBAAGjB,OAAA;YAAAiB,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACI,OAAO,CAACK,KAAK;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE9CrB,OAAA;UAAKgB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjB,OAAA;YAAAiB,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACvBI,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACM,MAAM,CAACT,MAAM,GAAG,CAAC,gBAC1CtB,OAAA;YAAKgB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAClBQ,OAAO,CAACM,MAAM,CAACP,GAAG,CAAC,CAACQ,GAAG,EAAEC,QAAQ,kBAChCjC,OAAA;cAAoBgB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClCjB,OAAA;gBAAAiB,QAAA,GAAG,OAAK,EAACe,GAAG;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBrB,OAAA;gBACEkC,GAAG,EAAEF,GAAI;gBACTG,GAAG,EAAC,SAAS;gBACbnB,SAAS,EAAC,oCAAoC;gBAC9CoB,OAAO,EAAGC,CAAC,IAAK;kBACd1B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoB,GAAG,CAAC;kBACzCK,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,eAAe;gBACzC,CAAE;gBACFC,MAAM,EAAEA,CAAA,KAAM;kBACZ9B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoB,GAAG,CAAC;gBAChD;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAbMY,QAAQ;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENrB,OAAA;YAAMgB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC1D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrB,OAAA;UAASgB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACvBjB,OAAA;YAASgB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACpErB,OAAA;YAAKgB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EACxDyB,IAAI,CAACC,SAAS,CAAClB,OAAO,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GArCFK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsCV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CA9EID,aAAa;AAAA2C,EAAA,GAAb3C,aAAa;AAgFnB,eAAeA,aAAa;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}