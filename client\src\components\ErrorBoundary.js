import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error('🚨 Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-8">
          <div className="bg-white border border-red-200 rounded-lg p-6 max-w-2xl w-full">
            <h1 className="text-2xl font-bold text-red-600 mb-4">🚨 Something went wrong!</h1>
            <p className="text-gray-700 mb-4">
              An error occurred that prevented the app from working properly. This might be why you can't interact with the interface.
            </p>
            
            <div className="bg-gray-100 p-4 rounded mb-4">
              <h3 className="font-semibold text-gray-800 mb-2">Error Details:</h3>
              <pre className="text-sm text-red-600 whitespace-pre-wrap">
                {this.state.error && this.state.error.toString()}
              </pre>
            </div>

            <div className="bg-gray-100 p-4 rounded mb-4">
              <h3 className="font-semibold text-gray-800 mb-2">Stack Trace:</h3>
              <pre className="text-xs text-gray-600 whitespace-pre-wrap overflow-auto max-h-40">
                {this.state.errorInfo.componentStack}
              </pre>
            </div>

            <div className="space-y-2">
              <button 
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                🔄 Reload Page
              </button>
              <button 
                onClick={() => window.location.href = '/simple-test'}
                className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                🧪 Go to Simple Test Page
              </button>
            </div>

            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded">
              <h4 className="font-semibold text-yellow-800">Troubleshooting Tips:</h4>
              <ul className="text-yellow-700 text-sm mt-1">
                <li>• Check browser console (F12) for more errors</li>
                <li>• Try refreshing the page</li>
                <li>• Clear browser cache and cookies</li>
                <li>• Make sure backend server is running</li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
