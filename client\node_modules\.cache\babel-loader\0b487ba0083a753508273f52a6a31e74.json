{"ast": null, "code": "'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n  var token = this;\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\nmodule.exports = CancelToken;", "map": {"version": 3, "names": ["Cancel", "require", "CancelToken", "executor", "TypeError", "resolvePromise", "promise", "Promise", "promiseExecutor", "resolve", "token", "cancel", "message", "reason", "prototype", "throwIfRequested", "source", "c", "module", "exports"], "sources": ["D:/ITSS_Reference/client/node_modules/axios/lib/cancel/CancelToken.js"], "sourcesContent": ["'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n\n  var token = this;\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\n\nmodule.exports = CancelToken;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,MAAM,IAAIC,SAAS,CAAC,8BAA8B,CAAC;EACrD;EAEA,IAAIC,cAAc;EAClB,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,SAASC,eAAeA,CAACC,OAAO,EAAE;IAC3DJ,cAAc,GAAGI,OAAO;EAC1B,CAAC,CAAC;EAEF,IAAIC,KAAK,GAAG,IAAI;EAChBP,QAAQ,CAAC,SAASQ,MAAMA,CAACC,OAAO,EAAE;IAChC,IAAIF,KAAK,CAACG,MAAM,EAAE;MAChB;MACA;IACF;IAEAH,KAAK,CAACG,MAAM,GAAG,IAAIb,MAAM,CAACY,OAAO,CAAC;IAClCP,cAAc,CAACK,KAAK,CAACG,MAAM,CAAC;EAC9B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACAX,WAAW,CAACY,SAAS,CAACC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACnE,IAAI,IAAI,CAACF,MAAM,EAAE;IACf,MAAM,IAAI,CAACA,MAAM;EACnB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACAX,WAAW,CAACc,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACrC,IAAIL,MAAM;EACV,IAAID,KAAK,GAAG,IAAIR,WAAW,CAAC,SAASC,QAAQA,CAACc,CAAC,EAAE;IAC/CN,MAAM,GAAGM,CAAC;EACZ,CAAC,CAAC;EACF,OAAO;IACLP,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AAEDO,MAAM,CAACC,OAAO,GAAGjB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}