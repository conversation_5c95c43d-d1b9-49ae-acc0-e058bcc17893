{"ast": null, "code": "// Suppress image loading errors in console\nexport const suppressImageErrors = () => {\n  // Override console.error to filter out image loading errors\n  const originalConsoleError = console.error;\n  console.error = (...args) => {\n    const message = args[0];\n\n    // Filter out common image loading error messages\n    if (typeof message === 'string') {\n      const imageErrorPatterns = ['Failed to load resource', 'GET http://example.com', 'net::ERR_NAME_NOT_RESOLVED', 'Image load error', 'img src error'];\n      const isImageError = imageErrorPatterns.some(pattern => message.includes(pattern));\n      if (isImageError) {\n        // Don't log image errors\n        return;\n      }\n    }\n\n    // Log other errors normally\n    originalConsoleError.apply(console, args);\n  };\n};\n\n// Restore original console.error\nexport const restoreConsoleError = () => {\n  // This would need to store the original function reference\n  // For now, just reload the page to restore\n};\n\n// Add global error event listener for images\nexport const addGlobalImageErrorHandler = () => {\n  window.addEventListener('error', e => {\n    if (e.target && e.target.tagName === 'IMG') {\n      // Prevent the error from being logged\n      e.preventDefault();\n      e.stopPropagation();\n\n      // Set fallback image if not already set\n      if (!e.target.src.includes('placeholder')) {\n        e.target.src = 'https://via.placeholder.com/400x400/e5e7eb/6b7280?text=No+Image';\n      }\n    }\n  }, true);\n};", "map": {"version": 3, "names": ["suppressImageErrors", "originalConsoleError", "console", "error", "args", "message", "imageErrorPatterns", "isImageError", "some", "pattern", "includes", "apply", "restoreConsoleError", "addGlobalImageErrorHandler", "window", "addEventListener", "e", "target", "tagName", "preventDefault", "stopPropagation", "src"], "sources": ["D:/ITSS_Reference/client/src/utils/errorSuppressor.js"], "sourcesContent": ["// Suppress image loading errors in console\nexport const suppressImageErrors = () => {\n  // Override console.error to filter out image loading errors\n  const originalConsoleError = console.error;\n  \n  console.error = (...args) => {\n    const message = args[0];\n    \n    // Filter out common image loading error messages\n    if (typeof message === 'string') {\n      const imageErrorPatterns = [\n        'Failed to load resource',\n        'GET http://example.com',\n        'net::ERR_NAME_NOT_RESOLVED',\n        'Image load error',\n        'img src error'\n      ];\n      \n      const isImageError = imageErrorPatterns.some(pattern => \n        message.includes(pattern)\n      );\n      \n      if (isImageError) {\n        // Don't log image errors\n        return;\n      }\n    }\n    \n    // Log other errors normally\n    originalConsoleError.apply(console, args);\n  };\n};\n\n// Restore original console.error\nexport const restoreConsoleError = () => {\n  // This would need to store the original function reference\n  // For now, just reload the page to restore\n};\n\n// Add global error event listener for images\nexport const addGlobalImageErrorHandler = () => {\n  window.addEventListener('error', (e) => {\n    if (e.target && e.target.tagName === 'IMG') {\n      // Prevent the error from being logged\n      e.preventDefault();\n      e.stopPropagation();\n      \n      // Set fallback image if not already set\n      if (!e.target.src.includes('placeholder')) {\n        e.target.src = 'https://via.placeholder.com/400x400/e5e7eb/6b7280?text=No+Image';\n      }\n    }\n  }, true);\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,mBAAmB,GAAGA,CAAA,KAAM;EACvC;EACA,MAAMC,oBAAoB,GAAGC,OAAO,CAACC,KAAK;EAE1CD,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGC,IAAI,KAAK;IAC3B,MAAMC,OAAO,GAAGD,IAAI,CAAC,CAAC,CAAC;;IAEvB;IACA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CACzB,yBAAyB,EACzB,wBAAwB,EACxB,4BAA4B,EAC5B,kBAAkB,EAClB,eAAe,CAChB;MAED,MAAMC,YAAY,GAAGD,kBAAkB,CAACE,IAAI,CAACC,OAAO,IAClDJ,OAAO,CAACK,QAAQ,CAACD,OAAO,CAC1B,CAAC;MAED,IAAIF,YAAY,EAAE;QAChB;QACA;MACF;IACF;;IAEA;IACAN,oBAAoB,CAACU,KAAK,CAACT,OAAO,EAAEE,IAAI,CAAC;EAC3C,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;EACvC;EACA;AAAA,CACD;;AAED;AACA,OAAO,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAC9CC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;IACtC,IAAIA,CAAC,CAACC,MAAM,IAAID,CAAC,CAACC,MAAM,CAACC,OAAO,KAAK,KAAK,EAAE;MAC1C;MACAF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBH,CAAC,CAACI,eAAe,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACJ,CAAC,CAACC,MAAM,CAACI,GAAG,CAACX,QAAQ,CAAC,aAAa,CAAC,EAAE;QACzCM,CAAC,CAACC,MAAM,CAACI,GAAG,GAAG,iEAAiE;MAClF;IACF;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}